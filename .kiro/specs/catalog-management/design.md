# Design Document

## Overview

Система управления каталогом запчастей представляет собой комплексное решение для администрирования всех аспектов каталога PartTec3. Система построена на принципах модульности, типобезопасности и максимального переиспользования компонентов. Основные модули включают управление брендами, категориями, каталожными позициями, группами взаимозаменяемости, а также инструменты импорта/экспорта и аналитики.

## Architecture

### Frontend Architecture
```
frontend/src/pages/admin/catalog/
├── brands/                 # Управление брендами
│   ├── index.astro        # Список брендов
│   └── [id].astro         # Редактирование бренда
├── categories/            # Управление категориями
│   ├── index.astro        # Дерево категорий
│   └── [id].astro         # Редактирование категории
├── items/                 # Каталожные позиции
│   ├── index.astro        # Список позиций
│   ├── create.astro       # Создание позиции
│   └── [id].astro         # Редактирование позиции
├── parts/                 # Группы взаимозаменяемости
│   ├── index.astro        # Список групп
│   ├── create.astro       # Создание группы
│   └── [id].astro         # Редактирование группы
├── import-export/         # Импорт/экспорт
│   └── index.astro        # Инструменты импорта/экспорта
└── analytics/             # Аналитика каталога
    └── index.astro        # Дашборд аналитики
```

### Backend Architecture
```
api/routers/catalog/
├── brands.ts              # CRUD операции с брендами
├── categories.ts          # Управление категориями
├── catalog-items.ts       # Операции с каталожными позициями
├── parts.ts              # Управление группами взаимозаменяемости
├── import-export.ts      # Импорт/экспорт данных
└── analytics.ts          # Аналитика и статистика
```

### Data Flow
1. **Создание каталожной позиции**: Выбор бренда → Автоматическая подстановка атрибутов → Заполнение данных → Валидация → Сохранение
2. **Создание группы взаимозаменяемости**: Выбор категории → Определение базовых атрибутов → Добавление позиций → Указание точности применимости
3. **Импорт данных**: Загрузка файла → Валидация структуры → Сопоставление полей → Обработка конфликтов → Массовое создание записей

## Components and Interfaces

### Core Components

#### BrandManager Component
```typescript
interface BrandManagerProps {
  initialFilters?: BrandFilters;
  mode?: 'list' | 'select';
  onBrandSelect?: (brand: Brand) => void;
}

interface BrandFilters {
  search?: string;
  isOem?: boolean;
  country?: string;
}
```

**Функциональность:**
- Список брендов с фильтрацией и поиском
- Создание/редактирование брендов
- Валидация уникальности slug
- Отображение статистики по каталожным позициям

#### CategoryTreeManager Component
```typescript
interface CategoryTreeManagerProps {
  selectable?: boolean;
  onCategorySelect?: (category: PartCategory) => void;
  showStats?: boolean;
}

interface CategoryNode extends PartCategory {
  children: CategoryNode[];
  itemCount?: number;
  expanded?: boolean;
}
```

**Функциональность:**
- Иерархическое дерево категорий
- Drag & drop для перемещения категорий
- Контекстное меню для операций
- Отображение количества запчастей в каждой категории

#### CatalogItemForm Component
```typescript
interface CatalogItemFormProps {
  item?: CatalogItem;
  brandId?: number;
  categoryId?: number;
  onSave: (item: CatalogItemInput) => Promise<void>;
  onCancel: () => void;
}

interface CatalogItemInput {
  sku: string;
  description?: string;
  brandId: number;
  isPublic: boolean;
  attributes: AttributeValue[];
  images?: File[];
}
```

**Функциональность:**
- Динамическая форма на основе шаблонов атрибутов
- Загрузка и предпросмотр изображений
- Валидация SKU в рамках бренда
- Автосохранение черновиков

#### PartGroupManager Component
```typescript
interface PartGroupManagerProps {
  parentId?: number;
  categoryId?: number;
  mode?: 'hierarchy' | 'flat';
}

interface PartApplicabilityInput {
  catalogItemId: number;
  accuracy: ApplicabilityAccuracy;
  notes?: string;
}
```

**Функциональность:**
- Управление группами взаимозаменяемости
- Добавление/удаление каталожных позиций
- Настройка точности применимости
- Иерархическое представление групп

### Utility Components

#### AttributeValueEditor Component
```typescript
interface AttributeValueEditorProps {
  template: AttributeTemplate;
  value?: string;
  onChange: (value: string) => void;
  error?: string;
}
```

**Функциональность:**
- Универсальный редактор значений атрибутов
- Поддержка всех типов данных (STRING, NUMBER, BOOLEAN, DATE, JSON)
- Валидация на основе ограничений шаблона
- Автодополнение для enum значений

#### ImageUploader Component
```typescript
interface ImageUploaderProps {
  maxFiles?: number;
  maxSize?: number;
  acceptedTypes?: string[];
  onUpload: (files: File[]) => Promise<string[]>;
  existingImages?: string[];
}
```

**Функциональность:**
- Drag & drop загрузка изображений
- Предпросмотр и кроппинг
- Прогресс загрузки
- Управление существующими изображениями

## Data Models

### Enhanced Brand Model
```typescript
interface BrandWithStats extends Brand {
  _count: {
    catalogItems: number;
    equipmentModels: number;
  };
  stats?: {
    publicItems: number;
    privateItems: number;
    lastUpdated: Date;
  };
}
```

### Enhanced CatalogItem Model
```typescript
interface CatalogItemWithRelations extends CatalogItem {
  brand: Brand;
  attributes: CatalogItemAttributeWithTemplate[];
  applicabilities: PartApplicabilityWithPart[];
  images: CatalogItemImage[];
}

interface CatalogItemAttributeWithTemplate extends CatalogItemAttribute {
  template: AttributeTemplate;
}
```

### Part Group Model
```typescript
interface PartWithRelations extends Part {
  category: PartCategory;
  parent?: Part;
  children: Part[];
  attributes: PartAttributeWithTemplate[];
  applicabilities: PartApplicabilityWithItem[];
  stats: {
    totalItems: number;
    exactMatches: number;
    partialMatches: number;
  };
}
```

## Error Handling

### Validation Strategy
1. **Client-side validation**: Немедленная обратная связь с пользователем
2. **Server-side validation**: Окончательная проверка перед сохранением
3. **Business logic validation**: Проверка бизнес-правил и ограничений

### Error Types
```typescript
enum CatalogErrorCode {
  DUPLICATE_SKU = 'DUPLICATE_SKU',
  INVALID_BRAND = 'INVALID_BRAND',
  MISSING_REQUIRED_ATTRIBUTES = 'MISSING_REQUIRED_ATTRIBUTES',
  CATEGORY_HAS_CHILDREN = 'CATEGORY_HAS_CHILDREN',
  PART_HAS_APPLICABILITIES = 'PART_HAS_APPLICABILITIES',
  IMPORT_FORMAT_ERROR = 'IMPORT_FORMAT_ERROR',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE'
}
```

### Error Handling Components
```typescript
interface ErrorBoundaryProps {
  fallback: (error: Error) => React.ReactNode;
  onError?: (error: Error) => void;
}

interface ValidationErrorDisplayProps {
  errors: Record<string, string[]>;
  field?: string;
}
```

## Testing Strategy

### Unit Testing
- **Validation functions**: Тестирование всех правил валидации
- **Utility functions**: Проверка вспомогательных функций
- **Data transformations**: Тестирование преобразований данных

### Integration Testing
- **API endpoints**: Тестирование всех CRUD операций
- **File upload/download**: Проверка импорта/экспорта
- **Database transactions**: Тестирование сложных операций

### E2E Testing
- **Complete workflows**: Полные сценарии создания каталожных позиций
- **Import/Export flows**: Тестирование импорта больших файлов
- **Permission checks**: Проверка ролевой модели доступа

### Performance Testing
- **Large dataset handling**: Тестирование с большими объемами данных
- **Search performance**: Проверка скорости поиска и фильтрации
- **Import performance**: Тестирование импорта файлов различного размера

## Security Considerations

### Access Control
```typescript
// Роли и разрешения для каталога
const CatalogPermissions = {
  ADMIN: ['create', 'read', 'update', 'delete', 'import', 'export'],
  SHOP: ['read', 'export'],
  USER: ['read'],
  GUEST: []
} as const;
```

### Data Validation
- **Input sanitization**: Очистка всех пользовательских данных
- **File validation**: Проверка типов и размеров загружаемых файлов
- **SQL injection prevention**: Использование параметризованных запросов
- **XSS protection**: Экранирование выводимых данных

### Audit Trail
```typescript
interface CatalogAuditLog {
  id: string;
  userId: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE' | 'IMPORT' | 'EXPORT';
  entityType: 'Brand' | 'Category' | 'CatalogItem' | 'Part';
  entityId: string;
  changes: Record<string, { old: any; new: any }>;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
}
```

## Performance Optimization

### Database Optimization
- **Indexes**: Создание индексов для часто используемых полей поиска
- **Query optimization**: Оптимизация сложных запросов с JOIN
- **Pagination**: Эффективная пагинация больших списков
- **Caching**: Кеширование часто запрашиваемых данных

### Frontend Optimization
- **Virtual scrolling**: Для больших списков каталожных позиций
- **Lazy loading**: Отложенная загрузка изображений и данных
- **Debounced search**: Оптимизация поисковых запросов
- **Component memoization**: Предотвращение лишних перерендеров

### File Handling
- **Image optimization**: Автоматическое сжатие и ресайз изображений
- **Progressive upload**: Загрузка файлов частями
- **Background processing**: Обработка импорта в фоновом режиме
- **CDN integration**: Использование CDN для статических файлов

## Monitoring and Analytics

### Key Metrics
- **Catalog completeness**: Процент позиций с полными атрибутами
- **Data quality score**: Оценка качества данных каталога
- **User activity**: Статистика использования различных функций
- **Performance metrics**: Время отклика и пропускная способность

### Alerting
- **Data quality alerts**: Уведомления о проблемах с данными
- **Performance alerts**: Предупреждения о снижении производительности
- **Error rate monitoring**: Отслеживание частоты ошибок
- **Import/Export status**: Уведомления о статусе длительных операций