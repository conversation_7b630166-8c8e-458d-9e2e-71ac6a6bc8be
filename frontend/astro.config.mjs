// @ts-check
import { defineConfig } from 'astro/config';
import tailwindcss from '@tailwindcss/vite';
import path from 'path';

import vue from '@astrojs/vue';

import node from '@astrojs/node';

// https://astro.build/config
export default defineConfig({
  output: 'server',
  
  integrations: [vue({ appEntrypoint: '/src/plugins/primevue' })],

  vite: {
    plugins: [tailwindcss()],
    resolve: {
      alias: [
        { find: "@", replacement: path.resolve(process.cwd(), "./src") },
        { find: "@/components", replacement: path.resolve(process.cwd(), "./src/components") },
        { find: "@/composables", replacement: path.resolve(process.cwd(), "./src/composables") },
        { find: "@/volt", replacement: path.resolve(process.cwd(), "./src/volt") },
        { find: "@/lib", replacement: path.resolve(process.cwd(), "./src/lib") },
        { find: "@/types", replacement: path.resolve(process.cwd(), "./src/types") },
        { find: "@/layouts", replacement: path.resolve(process.cwd(), "./src/layouts") },
        { find: "@/pages", replacement: path.resolve(process.cwd(), "./src/pages") },
        { find: "@/styles", replacement: path.resolve(process.cwd(), "./src/styles") }
      ]
    }
  },

  adapter: node({
    mode: 'standalone',
  }),
});