# Система тем и UI компоненты PartTec

## 🎨 Система переключения тем

### Возможности
- **Светлая тема** - классическая светлая тема для дневной работы
- **Темная тема** - темная тема для работы в условиях низкой освещенности
- **Системная тема** - автоматическое переключение в зависимости от настроек ОС
- **Сохранение предпочтений** - выбранная тема сохраняется в localStorage
- **Плавные переходы** - анимированное переключение между темами
- **Предотвращение мигания** - скрипт предзагрузки темы в `<head>`

### Использование

#### Composable `useTheme`
```typescript
import { useTheme } from '@/composables/useTheme'

const {
  currentTheme,    // 'light' | 'dark' | 'system'
  activeTheme,     // 'light' | 'dark' (вычисляемая)
  isDark,          // boolean
  isLight,         // boolean
  isSystem,        // boolean
  themeIcon,       // иконка для текущей темы
  themeName,       // название темы
  setTheme,        // установить тему
  toggleTheme,     // переключить тему
  resetToSystem    // сбросить к системной
} = useTheme()
```

#### Компонент `ThemeToggle`
```vue
<!-- Простая кнопка переключения -->
<ThemeToggle mode="toggle" />

<!-- Выпадающее меню -->
<ThemeToggle mode="menu" />

<!-- Группа кнопок -->
<ThemeToggle mode="buttons" show-label />
```

### CSS переменные
Система использует CSS переменные для всех цветов:

```css
/* Светлая тема */
:root {
  --p-surface-0: #ffffff;
  --p-surface-50: #fafafa;
  --p-text-color: #18181b;
  /* ... */
}

/* Темная тема */
[data-theme="dark"] {
  --p-surface-0: #09090b;
  --p-surface-50: #18181b;
  --p-text-color: #fafafa;
  /* ... */
}
```

## 🧩 UI Компоненты

### Готовые компоненты Volt UI
Все компоненты поддерживают темы и следуют дизайн-системе:

#### DataTable
```vue
<DataTable :value="data" paginator :rows="10">
  <Column field="name" header="Название" sortable />
  <Column field="status" header="Статус" />
</DataTable>
```

#### Dialog
```vue
<Dialog v-model:visible="visible" modal header="Заголовок">
  <p>Содержимое диалога</p>
  <template #footer>
    <Button label="Сохранить" />
  </template>
</Dialog>
```

#### Toast уведомления
```typescript
import { useToast } from '@/composables/useToast'

const toast = useToast()

// Базовые методы
toast.success('Успешно!', 'Операция выполнена')
toast.error('Ошибка!', 'Что-то пошло не так')
toast.warn('Внимание!', 'Проверьте данные')
toast.info('Информация', 'Полезная информация')

// Удобные методы
toast.showSaveSuccess('Пользователь')
toast.showDeleteError('Запись')
toast.showValidationError()
```

#### ConfirmDialog
```typescript
import { useConfirm } from '@/composables/useConfirm'

const confirm = useConfirm()

// Подтверждение удаления
confirm.confirmDelete('пользователя', () => {
  // Логика удаления
})

// Подтверждение сохранения
confirm.confirmSave('Сохранить изменения?', () => {
  // Логика сохранения
})
```

#### Breadcrumb
```vue
<Breadcrumb :model="breadcrumbItems" />

<script setup>
const breadcrumbItems = [
  { label: 'Главная', url: '/' },
  { label: 'Каталог', url: '/catalog' },
  { label: 'Детали' }
]
</script>
```

### Архитектура компонентов

#### Vue Islands
Все интерактивные компоненты используют директиву `client:load`:
```astro
<AdminToolbar client:load />
<AdminSidebar client:load />
<UIDemo client:load />
```

#### Volt UI обертки
Каждый компонент PrimeVue обернут в Volt UI стили:
```vue
<template>
  <DataTable
    unstyled
    :pt="theme"
    :ptOptions="{ mergeProps: ptViewMerge }"
  >
    <!-- слоты -->
  </DataTable>
</template>
```

## 🚀 Быстрый старт

### 1. Добавление новой страницы
```astro
---
import AdminLayout from '@/layouts/AdminLayout.astro'
import MyComponent from '@/components/MyComponent.vue'
---

<AdminLayout title="Моя страница">
  <MyComponent client:load />
</AdminLayout>
```

### 2. Использование в компоненте
```vue
<template>
  <div class="space-y-6">
    <!-- Заголовок с breadcrumb -->
    <div>
      <Breadcrumb :model="breadcrumbs" />
      <h1 class="text-2xl font-bold text-surface-900 mt-4">
        Управление пользователями
      </h1>
    </div>

    <!-- Таблица данных -->
    <DataTable :value="users" paginator :rows="10">
      <Column field="name" header="Имя" sortable />
      <Column field="email" header="Email" sortable />
      <Column header="Действия">
        <template #body="{ data }">
          <SecondaryButton 
            icon="pi pi-pencil" 
            @click="editUser(data)" 
          />
          <SecondaryButton 
            icon="pi pi-trash" 
            class="text-red-500"
            @click="deleteUser(data)" 
          />
        </template>
      </Column>
    </DataTable>

    <!-- Диалог редактирования -->
    <Dialog v-model:visible="editDialog" header="Редактирование">
      <!-- Форма -->
    </Dialog>
  </div>

  <!-- Сервисы -->
  <Toast />
  <ConfirmDialog />
</template>

<script setup lang="ts">
import { useToast } from '@/composables/useToast'
import { useConfirm } from '@/composables/useConfirm'

const toast = useToast()
const confirm = useConfirm()

const deleteUser = (user) => {
  confirm.confirmDelete(`пользователя ${user.name}`, () => {
    // API вызов удаления
    toast.showDeleteSuccess('Пользователь')
  })
}
</script>
```

## 📁 Структура файлов

```
frontend/src/
├── components/
│   ├── ui/
│   │   └── ThemeToggle.vue      # Переключатель тем
│   └── admin/
│       ├── AdminLayout.astro    # Обновленный layout
│       ├── AdminToolbar.vue     # Toolbar с темами
│       └── UIDemo.vue           # Демо компонентов
├── composables/
│   ├── useTheme.ts             # Управление темами
│   ├── useToast.ts             # Уведомления
│   └── useConfirm.ts           # Подтверждения
├── volt/
│   ├── DataTable.vue           # Таблица данных
│   ├── Dialog.vue              # Диалоговое окно
│   ├── Toast.vue               # Уведомления
│   ├── ConfirmDialog.vue       # Подтверждения
│   └── Breadcrumb.vue          # Хлебные крошки
└── styles/
    └── global.css              # CSS переменные тем
```

## 🎯 Следующие шаги

1. **Тестирование** - проверить работу всех компонентов в разных темах
2. **Оптимизация** - добавить lazy loading для больших таблиц
3. **Расширение** - добавить больше вариантов тем (например, высокий контраст)
4. **Документация** - создать Storybook для компонентов
5. **Тесты** - написать unit тесты для composables
