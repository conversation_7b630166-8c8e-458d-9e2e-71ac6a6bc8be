## 📚 Документация

- [Frontend README](./README.md) - Подробная документация по frontend
- [API README](./api/README.md) - Документация по backend API

## 🤝 Разработка

Проект готов к разработке! Все основные технологии настроены и интегрированы.

## UI Библиотека
- [Volt UI](https://volt.primevue.org/overview) - Система компонентов

## Основные паттерны

- Следуй кристально чистой архитектуре проекта.
- Главный паттерн Vue Islands с client:load директивой.
- Volt UI как библиотека компонентов.
- МАКСИМАЛЬНО ПРОСТОЙ КОД, без лишнего функционала.


- Изучи внимательно ZenStack схему и приступай.
@/home/<USER>/Dev/parttec3/api/catalog_schema.zmodel 

- Если потребуется, изучи сгенерированные Zod схемы. 

- Перед началом работы, ТЩАТЕЛЬНО ИЗУЧИ структуру схемы бд и документацию. Не торопись, делай все максимально качественно. 
- ГЛАВНОЕ ПОЛУЧИТЬ РАБОЧИЙ КОД, А НЕ МНОГО КОДА.

- Мы делаем максимально гибкую систему, которая будет легко расширяться и поддерживаться. 
- По возможности реализовывай автогенерацию форм на базе Zod схем.
- Мы не создаем демо код, тестовый код! Мы сразу приступаем к реальному функционалу!
- Учти что категорически запрещено дублировать функционал, везде и всегда должны быть единые компоненты! 
- Категорически запрещено нарушать принцип DRY 

