/**
 * Composable для работы с диалогами подтверждения
 * Предоставляет удобные методы для показа различных типов подтверждений
 */

import { useConfirm as usePrimeConfirm } from 'primevue/useconfirm'

export interface ConfirmOptions {
  message?: string
  header?: string
  icon?: string
  acceptLabel?: string
  rejectLabel?: string
  acceptClass?: string
  rejectClass?: string
  accept?: () => void
  reject?: () => void
}

export const useConfirm = () => {
  const confirm = usePrimeConfirm()

  // Базовый метод для показа диалога подтверждения
  const show = (options: ConfirmOptions) => {
    confirm.require({
      message: options.message || 'Вы уверены?',
      header: options.header || 'Подтверждение',
      icon: options.icon || 'pi pi-exclamation-triangle',
      acceptLabel: options.acceptLabel || 'Да',
      rejectLabel: options.rejectLabel || 'Отмена',
      acceptClass: options.acceptClass,
      rejectClass: options.rejectClass,
      accept: options.accept,
      reject: options.reject
    })
  }

  // Подтверждение удаления
  const confirmDelete = (
    entityName = 'запись',
    onConfirm?: () => void,
    onReject?: () => void
  ) => {
    show({
      message: `Вы действительно хотите удалить эту ${entityName}? Это действие нельзя отменить.`,
      header: 'Подтверждение удаления',
      icon: 'pi pi-trash',
      acceptLabel: 'Удалить',
      rejectLabel: 'Отмена',
      acceptClass: 'bg-red-500 hover:bg-red-600',
      accept: onConfirm,
      reject: onReject
    })
  }

  // Подтверждение сохранения
  const confirmSave = (
    message = 'Сохранить изменения?',
    onConfirm?: () => void,
    onReject?: () => void
  ) => {
    show({
      message,
      header: 'Сохранение',
      icon: 'pi pi-save',
      acceptLabel: 'Сохранить',
      rejectLabel: 'Отмена',
      acceptClass: 'bg-primary-500 hover:bg-primary-600',
      accept: onConfirm,
      reject: onReject
    })
  }

  // Подтверждение отмены изменений
  const confirmCancel = (
    message = 'У вас есть несохраненные изменения. Вы действительно хотите выйти без сохранения?',
    onConfirm?: () => void,
    onReject?: () => void
  ) => {
    show({
      message,
      header: 'Несохраненные изменения',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Выйти без сохранения',
      rejectLabel: 'Остаться',
      acceptClass: 'bg-orange-500 hover:bg-orange-600',
      accept: onConfirm,
      reject: onReject
    })
  }

  // Подтверждение публикации
  const confirmPublish = (
    entityName = 'запись',
    onConfirm?: () => void,
    onReject?: () => void
  ) => {
    show({
      message: `Опубликовать эту ${entityName}? После публикации она станет доступна всем пользователям.`,
      header: 'Подтверждение публикации',
      icon: 'pi pi-globe',
      acceptLabel: 'Опубликовать',
      rejectLabel: 'Отмена',
      acceptClass: 'bg-green-500 hover:bg-green-600',
      accept: onConfirm,
      reject: onReject
    })
  }

  // Подтверждение архивирования
  const confirmArchive = (
    entityName = 'запись',
    onConfirm?: () => void,
    onReject?: () => void
  ) => {
    show({
      message: `Архивировать эту ${entityName}? Архивированные записи не отображаются в основном списке.`,
      header: 'Подтверждение архивирования',
      icon: 'pi pi-archive',
      acceptLabel: 'Архивировать',
      rejectLabel: 'Отмена',
      acceptClass: 'bg-gray-500 hover:bg-gray-600',
      accept: onConfirm,
      reject: onReject
    })
  }

  // Подтверждение восстановления
  const confirmRestore = (
    entityName = 'запись',
    onConfirm?: () => void,
    onReject?: () => void
  ) => {
    show({
      message: `Восстановить эту ${entityName}? Она снова станет доступна в основном списке.`,
      header: 'Подтверждение восстановления',
      icon: 'pi pi-refresh',
      acceptLabel: 'Восстановить',
      rejectLabel: 'Отмена',
      acceptClass: 'bg-blue-500 hover:bg-blue-600',
      accept: onConfirm,
      reject: onReject
    })
  }

  // Подтверждение массового действия
  const confirmBulkAction = (
    action: string,
    count: number,
    onConfirm?: () => void,
    onReject?: () => void
  ) => {
    show({
      message: `Выполнить действие "${action}" для ${count} записей?`,
      header: 'Массовое действие',
      icon: 'pi pi-list',
      acceptLabel: 'Выполнить',
      rejectLabel: 'Отмена',
      acceptClass: 'bg-primary-500 hover:bg-primary-600',
      accept: onConfirm,
      reject: onReject
    })
  }

  return {
    // Базовый метод
    show,

    // Специализированные методы
    confirmDelete,
    confirmSave,
    confirmCancel,
    confirmPublish,
    confirmArchive,
    confirmRestore,
    confirmBulkAction
  }
}
