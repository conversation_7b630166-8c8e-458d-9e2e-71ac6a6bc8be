import { ref, computed } from 'vue';
import { trpc } from '@/lib/trpc';
import type { AppRouter } from '@/lib/trpc';

// Типы для удобства
type TRPCClient = typeof trpc;

// Composable для работы с tRPC
export function useTrpc() {
  const loading = ref(false);
  const error = ref<string | null>(null);

  // Функция для обработки ошибок
  const handleError = (err: any) => {
    console.error('tRPC Error:', err);
    error.value = err.message || 'Произошла ошибка при выполнении запроса';
  };

  // Функция для очистки ошибок
  const clearError = () => {
    error.value = null;
  };

  // Wrapper для выполнения запросов с обработкой состояния
  const execute = async <T>(operation: () => Promise<T>): Promise<T | null> => {
    try {
      loading.value = true;
      clearError();
      const result = await operation();
      return result;
    } catch (err) {
      handleError(err);
      return null;
    } finally {
      loading.value = false;
    }
  };

  return {
    // Состояние
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    
    // Методы
    clearError,
    execute,
    
    // tRPC клиент
    client: trpc,
    
    // Удобные методы для работы с основными сущностями
    parts: {
      // Получить все части
      findMany: (input?: any) => execute(() => trpc.crud.part.findMany.query(input)),
      
      // Получить часть по ID
      findUnique: (input: { where: { id: number } }) => 
        execute(() => trpc.crud.part.findUnique.query(input)),
      
      // Создать новую часть
      create: (input: any) => execute(() => trpc.crud.part.create.mutate(input)),
      
      // Обновить часть
      update: (input: any) => execute(() => trpc.crud.part.update.mutate(input)),
      
      // Удалить часть
      delete: (input: { where: { id: number } }) => 
        execute(() => trpc.crud.part.delete.mutate(input)),
    },
    
    catalogItems: {
      // Получить все каталожные позиции
      findMany: (input?: any) => execute(() => trpc.crud.catalogItem.findMany.query(input)),
      
      // Получить каталожную позицию по ID
      findUnique: (input: { where: { id: number } }) => 
        execute(() => trpc.crud.catalogItem.findUnique.query(input)),
      
      // Создать новую каталожную позицию
      create: (input: any) => execute(() => trpc.crud.catalogItem.create.mutate(input)),
      
      // Обновить каталожную позицию
      update: (input: any) => execute(() => trpc.crud.catalogItem.update.mutate(input)),
      
      // Удалить каталожную позицию
      delete: (input: { where: { id: number } }) => 
        execute(() => trpc.crud.catalogItem.delete.mutate(input)),
    },
    
    brands: {
      // Получить все бренды
      findMany: (input?: any) => execute(() => trpc.crud.brand.findMany.query(input)),
      
      // Создать новый бренд
      create: (input: any) => execute(() => trpc.crud.brand.create.mutate(input)),
    },
    
    partCategories: {
      // Получить все категории
      findMany: (input?: any) => execute(() => trpc.crud.partCategory.findMany.query(input)),

      // Создать новую категорию
      create: (input: any) => execute(() => trpc.crud.partCategory.create.mutate(input)),
    },

    equipmentModels: {
      // Получить все модели техники
      findMany: (input?: any) => execute(() => trpc.crud.equipmentModel.findMany.query(input)),

      // Получить модель техники по ID
      findUnique: (input: { where: { id: string } }) =>
        execute(() => trpc.crud.equipmentModel.findUnique.query(input)),

      // Создать новую модель техники
      create: (input: any) => execute(() => trpc.crud.equipmentModel.create.mutate(input)),

      // Обновить модель техники
      update: (input: any) => execute(() => trpc.crud.equipmentModel.update.mutate(input)),

      // Удалить модель техники
      delete: (input: { where: { id: string } }) =>
        execute(() => trpc.crud.equipmentModel.delete.mutate(input)),
    },

    // Новые методы для работы с атрибутами на основе шаблонов
    partAttributes: {
      // Получить атрибуты запчасти
      findByPartId: (input: { partId: number }) =>
        execute(() => trpc.partAttributes.findByPartId.query(input)),

      // Создать атрибут запчасти
      create: (input: { partId: number; templateId: number; value: string }) =>
        execute(() => trpc.partAttributes.create.mutate(input)),

      // Обновить атрибут запчасти
      update: (input: { id: number; value: string }) =>
        execute(() => trpc.partAttributes.update.mutate(input)),

      // Удалить атрибут запчасти
      delete: (input: { id: number }) =>
        execute(() => trpc.partAttributes.delete.mutate(input)),

      // Массовое создание атрибутов
      bulkCreate: (input: { partId: number; attributes: Array<{ templateId: number; value: string }> }) =>
        execute(() => trpc.partAttributes.bulkCreate.mutate(input)),
    },

    // Методы для работы с шаблонами атрибутов
    attributeTemplates: {
      // Получить все шаблоны
      findMany: (input?: {
        groupId?: number;
        search?: string;
        dataType?: string;
        limit?: number;
        offset?: number
      }) => execute(() => trpc.attributeTemplates.findMany.query(input)),

      // Получить шаблон по ID
      findById: (input: { id: number }) =>
        execute(() => trpc.attributeTemplates.findById.query(input)),

      // Создать шаблон
      create: (input: any) =>
        execute(() => trpc.attributeTemplates.create.mutate(input)),

      // Обновить шаблон
      update: (input: any) =>
        execute(() => trpc.attributeTemplates.update.mutate(input)),

      // Удалить шаблон
      delete: (input: { id: number }) =>
        execute(() => trpc.attributeTemplates.delete.mutate(input)),

      // Получить все группы
      findAllGroups: () =>
        execute(() => trpc.attributeTemplates.findAllGroups.query()),

      // Создать группу
      createGroup: (input: { name: string; description?: string }) =>
        execute(() => trpc.attributeTemplates.createGroup.mutate(input)),

      // Обновить группу
      updateGroup: (input: { id: number; name?: string; description?: string }) =>
        execute(() => trpc.attributeTemplates.updateGroup.mutate(input)),

      // Удалить группу
      deleteGroup: (input: { id: number }) =>
        execute(() => trpc.attributeTemplates.deleteGroup.mutate(input)),
    },

    partApplicability: {
      upsert: (input: any) => 
        execute(() => trpc.crud.partApplicability.upsert.mutate(input)),
      findMany: (input?: any) => 
        execute(() => trpc.crud.partApplicability.findMany.query(input)),
      create: (input: any) =>
        execute(() => trpc.crud.partApplicability.create.mutate(input)),
      update: (input: any) =>
        execute(() => trpc.crud.partApplicability.update.mutate(input)),
      findFirst: (input: any) =>
        execute(() => trpc.crud.partApplicability.findFirst.query(input)),
    },

    equipmentApplicability: {
      upsert: (input: any) =>
        execute(() => trpc.crud.equipmentApplicability.upsert.mutate(input)),
      findMany: (input?: any) =>
        execute(() => trpc.crud.equipmentApplicability.findMany.query(input)),
      create: (input: any) =>
        execute(() => trpc.crud.equipmentApplicability.create.mutate(input)),
      update: (input: any) =>
        execute(() => trpc.crud.equipmentApplicability.update.mutate(input)),
      findFirst: (input: any) =>
        execute(() => trpc.crud.equipmentApplicability.findFirst.query(input)),
    }
  };
}

// Экспортируем типы
export type { AppRouter };
