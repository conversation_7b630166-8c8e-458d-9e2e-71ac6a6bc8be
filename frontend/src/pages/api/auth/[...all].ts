/**
 * Better Auth API handler для Astro
 * Обрабатывает все запросы к /api/auth/*
 *
 * ВАЖНО: Этот файл проксирует запросы к отдельному API серверу
 * Для полноценной работы better-auth рекомендуется запускать отдельный API сервер
 */

import type { APIRoute } from "astro";

// URL API сервера (где запущен better-auth)
const API_SERVER_URL = import.meta.env.API_SERVER_URL || "http://localhost:3000";

// Функция для проксирования запросов к API серверу
async function proxyToApiServer(request: Request): Promise<Response> {
  const url = new URL(request.url);
  const apiUrl = `${API_SERVER_URL}${url.pathname}${url.search}`;

  try {
    const response = await fetch(apiUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body,
    });

    return response;
  } catch (error) {
    console.error('Proxy error:', error);
    return new Response(
      JSON.stringify({ error: 'API server unavailable' }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export const GET: APIRoute = async (ctx) => {
  return proxyToApiServer(ctx.request);
};

export const POST: APIRoute = async (ctx) => {
  return proxyToApiServer(ctx.request);
};

export const PUT: APIRoute = async (ctx) => {
  return proxyToApiServer(ctx.request);
};

export const PATCH: APIRoute = async (ctx) => {
  return proxyToApiServer(ctx.request);
};

export const DELETE: APIRoute = async (ctx) => {
  return proxyToApiServer(ctx.request);
};
