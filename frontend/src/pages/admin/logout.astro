---
/**
 * Страница выхода из системы
 * Выполняет выход и перенаправляет на страницу входа
 */

import AdminLayout from "../../layouts/AdminLayout.astro";
---

<AdminLayout title="Выход из системы - PartTec" showSidebar={false}>
  <div class="min-h-[60vh] flex items-center justify-center">
    <div class="max-w-md w-full text-center">
      <!-- Иконка загрузки -->
      <div class="mx-auto flex items-center justify-center h-24 w-24 rounded-full bg-primary-100 mb-6">
        <svg class="animate-spin h-12 w-12 text-primary-600" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>

      <!-- Сообщение -->
      <h1 class="text-2xl font-bold text-surface-900 mb-4">
        Выход из системы...
      </h1>
      <p class="text-surface-600 mb-8">
        Пожалуйста, подождите, мы завершаем вашу сессию.
      </p>

      <!-- Ссылка на вход (на случай, если JS не работает) -->
      <a 
        href="/admin/login" 
        class="text-primary-600 hover:text-primary-500 text-sm"
      >
        Перейти на страницу входа
      </a>
    </div>
  </div>
</AdminLayout>

<script>
  // Выполняем выход из системы при загрузке страницы
  import { navigate } from 'astro:transitions/client';
import { authClient } from '../../lib/auth-client';

  async function performLogout() {
    try {
      console.log('🔄 Выполняется выход из системы...');
      
      const result = await authClient.signOut();
      
      if (result.error) {
        console.error('❌ Ошибка при выходе:', result.error);
      } else {
        console.log('✅ Успешный выход из системы');
      }
    } catch (error) {
      console.error('❌ Ошибка при выходе:', error);
    } finally {
      // В любом случае перенаправляем на страницу входа
      setTimeout(() => {
        navigate('/admin/login');
      }, 1000);
    }
  }

  // Запускаем выход при загрузке страницы
  if (typeof window !== 'undefined') {
    performLogout();
  }
</script>
