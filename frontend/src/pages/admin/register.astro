---
/**
 * Страница регистрации в админ панель
 * Использует Vue Island для формы регистрации
 */

import RegisterForm from "../../components/auth/RegisterForm.vue";
import AdminLayout from "../../layouts/AdminLayout.astro";

// Проверяем, не авторизован ли уже пользователь
// Если да, перенаправляем на дашборд
const user = Astro.locals.user;
if (user) {
  return Astro.redirect('/admin');
}
---

<AdminLayout title="Регистрация - PartTec" showSidebar={false}>
  <RegisterForm client:load />
</AdminLayout>
