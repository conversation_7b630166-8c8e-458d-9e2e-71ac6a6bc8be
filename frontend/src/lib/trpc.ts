import { createTRPCClient, httpBatchLink } from '@trpc/client';
import type { AppRouter } from '../../../api/router';

// Получаем базовый URL API из переменных окружения или используем дефолтный
const getBaseUrl = () => {
  if (typeof window !== 'undefined') {
    // В браузере используем localhost:3000
    return 'http://localhost';
  }
  // На сервере используем полный URL
  return process.env.API_URL || 'http://localhost';
};

// Создаем tRPC клиент с правильной типизацией
export const trpc = createTRPCClient<AppRouter>({
  links: [
    httpBatchLink({
      url: `${getBaseUrl()}:3000/trpc`,
      // Включаем передачу cookies для авторизации
      fetch: (url, options) => {
        return fetch(url, {
          ...options,
          credentials: 'include', // Важно для передачи cookies
        });
      },
    }),
  ],
});

// Экспортируем типы для использования в компонентах
export type { AppRouter } from '../../../api/router';
