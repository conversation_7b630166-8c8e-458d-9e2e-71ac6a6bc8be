<template>
    <Chips
        unstyled
        :pt="theme"
        :ptOptions="{
            mergeProps: ptViewMerge
        }"
    >
        <template #removeicon>
            <TimesIcon />
        </template>
        <template v-for="(_, slotName) in $slots" #[slotName]="slotProps">
            <slot :name="slotName" v-bind="slotProps ?? {}" />
        </template>
    </Chips>
</template>

<script setup lang="ts">
import TimesIcon from '@primevue/icons/times';
import Chips, { type ChipsPassThroughOptions, type ChipsProps } from 'primevue/chips';
import { ref } from 'vue';
import { ptViewMerge } from './utils';

interface Props extends /* @vue-ignore */ ChipsProps {}
defineProps<Props>();

const theme = ref<ChipsPassThroughOptions>({
    root: `flex`,
    container: `m-0 py-1.5 px-3 list-none cursor-text overflow-hidden flex items-center flex-wrap
        w-full text-surface-900 dark:text-surface-0 bg-surface-0 dark:bg-surface-950 
        border border-surface-300 dark:border-surface-600 rounded-md 
        transition-colors duration-200 appearance-none
        hover:border-surface-400 dark:hover:border-surface-500
        focus-within:outline-none focus-within:outline-offset-0 focus-within:ring-1 focus-within:ring-primary-500 focus-within:border-primary-500
        p-invalid:border-red-500 p-invalid:focus-within:ring-red-500 p-invalid:focus-within:border-red-500`,
    token: `py-1 px-2 mr-2 bg-surface-200 dark:bg-surface-700 text-surface-700 dark:text-surface-300 rounded-md 
        inline-flex items-center`,
    label: `leading-none`,
    removeIcon: `ml-2 w-4 h-4 cursor-pointer`,
    inputToken: `py-1 px-0 ml-0 flex-1 inline-flex`,
    input: `border-0 outline-none bg-transparent m-0 p-0 shadow-none rounded-none w-full
        text-surface-700 dark:text-surface-200 placeholder:text-surface-400 dark:placeholder:text-surface-500`
});
</script>
