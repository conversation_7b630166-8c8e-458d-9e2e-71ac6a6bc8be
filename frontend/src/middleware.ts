/**
 * Astro middleware для авторизации
 * Обеспечивает серверную проверку авторизации для защищенных роутов
 */

import { defineMiddleware } from 'astro:middleware'
import { auth } from '../../api/auth'

// Определяем защищенные роуты
const PROTECTED_ROUTES = [
  '/admin',
  '/admin/',
] as const

const ADMIN_ONLY_ROUTES = [
  '/admin/users',
  '/admin/settings',
] as const

const SHOP_ROUTES = [
  '/admin/catalog',
  '/admin/parts',
] as const

// Роуты, доступные только неавторизованным пользователям
const GUEST_ONLY_ROUTES = [
  '/admin/login',
  '/admin/register',
] as const

/**
 * Проверяет, является ли роут защищенным
 */
function isProtectedRoute(pathname: string): boolean {
  return PROTECTED_ROUTES.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  )
}

/**
 * Проверяет, требует ли роут админские права
 */
function isAdminOnlyRoute(pathname: string): boolean {
  return ADMIN_ONLY_ROUTES.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  )
}

/**
 * Проверяет, является ли роут доступным только для магазинов
 */
function isShopRoute(pathname: string): boolean {
  return SHOP_ROUTES.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  )
}

/**
 * Проверяет, доступен ли роут только для неавторизованных пользователей
 */
function isGuestOnlyRoute(pathname: string): boolean {
  return GUEST_ONLY_ROUTES.some(route => 
    pathname === route || pathname.startsWith(route + '/')
  )
}

/**
 * Проверяет права доступа пользователя
 */
function hasAccess(user: any, pathname: string): boolean {
  if (!user) return false

  // Проверяем админские роуты
  if (isAdminOnlyRoute(pathname)) {
    return user.role === 'ADMIN'
  }

  // Проверяем роуты для магазинов
  if (isShopRoute(pathname)) {
    return user.role === 'SHOP' || user.role === 'ADMIN'
  }

  // Для остальных защищенных роутов проверяем общий доступ к админ панели
  return user.role === 'ADMIN' || user.role === 'SHOP'
}

export const onRequest = defineMiddleware(async (context, next) => {
  const { url, request, redirect } = context
  const pathname = url.pathname

  try {
    // Получаем сессию пользователя
    const session = await auth.api.getSession({
      headers: request.headers
    })

    const user = session?.user
    const isAuthenticated = !!user

    // Логируем для отладки в development
    if (import.meta.env.DEV) {
      console.log(`🔍 Auth middleware: ${pathname}`, {
        isAuthenticated,
        userRole: user?.role,
        userEmail: user?.email
      })
    }

    // Обрабатываем роуты только для гостей
    if (isGuestOnlyRoute(pathname)) {
      if (isAuthenticated && hasAccess(user, '/admin')) {
        console.log(`🔄 Redirecting authenticated user from ${pathname} to /admin`)
        return redirect('/admin')
      }
      // Пользователь не авторизован или не имеет доступа к админ панели - разрешаем доступ
      return next()
    }

    // Обрабатываем защищенные роуты
    if (isProtectedRoute(pathname)) {
      // Проверяем авторизацию
      if (!isAuthenticated) {
        console.log(`🔒 Unauthorized access to ${pathname}, redirecting to login`)
        return redirect('/admin/login')
      }

      // Проверяем права доступа
      if (!hasAccess(user, pathname)) {
        console.log(`🚫 Access denied for user ${user.email} to ${pathname}`)
        
        // Если у пользователя есть базовый доступ к админ панели, но не к конкретному роуту
        if (user.role === 'SHOP' || user.role === 'ADMIN') {
          return redirect('/admin/forbidden')
        } else {
          // Если у пользователя нет доступа к админ панели вообще
          return redirect('/admin/login')
        }
      }

      // Добавляем пользователя в locals для использования в компонентах
      context.locals.user = user
      context.locals.isAuthenticated = true
    }

    // Продолжаем обработку запроса
    return next()

  } catch (error) {
    console.error('❌ Auth middleware error:', error)
    
    // В случае ошибки авторизации для защищенных роутов перенаправляем на логин
    if (isProtectedRoute(pathname)) {
      return redirect('/admin/login')
    }
    
    // Для остальных роутов продолжаем без авторизации
    return next()
  }
})
