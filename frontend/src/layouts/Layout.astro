---
import { ClientRouter } from 'astro:transitions'

export interface Props {
  title: string
}

const { title } = Astro.props
---

<!doctype html>
<html lang='ru' class="h-full">
  <head>
    <meta charset='UTF-8' />
    <meta name='description' content='PartTec - система управления каталогом взаимозаменяемых запчастей' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <link rel='icon' type='image/svg+xml' href='/favicon.svg' />
    <ClientRouter />

    <title>{title}</title>

    <!-- Скрипт для предотвращения мигания темы -->
    <script is:inline src="/theme-init.js"></script>
  </head>
  <body class="h-full">
    <slot />
  </body>
</html>

<style>
  @import '../styles/global.css';
</style>
