---
/**
 * Admin Layout - Основной layout для административной панели
 * Включает навигацию, header и основную структуру
 */

import '../styles/global.css'
import AdminToolbar from '../components/admin/AdminToolbar.vue'
import AdminSidebar from '../components/admin/AdminSidebar.vue'
import { ClientRouter } from 'astro:transitions'

export interface Props {
  title?: string
  description?: string
  showSidebar?: boolean
}

const {
  title = "Админ панель - PartTec",
  description = "Система управления каталогом взаимозаменяемых запчастей",
  showSidebar = true
} = Astro.props
---

<!DOCTYPE html>
<html lang="ru" class="h-full">
<head>
  <meta charset="UTF-8" />
  <meta name="description" content={description} />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <ClientRouter/>
  <title>{title}</title>

  <!-- Скрипт для предотвращения мигания темы -->
  <script is:inline src="/theme-init.js"></script>
</head>

<body class="h-full">
  <div class="min-h-screen bg-surface-50 dark:bg-surface-900">
    <!-- Header с Volt Toolbar -->
    <AdminToolbar client:load />

    <div class="flex bg-surface-50 dark:bg-surface-900 min-h-[calc(100vh-4rem)]">
      <!-- Sidebar с Volt компонентами -->
      {showSidebar && (
        <AdminSidebar client:load />
      )}

      <!-- Основной контент -->
      <main class="flex-1 bg-surface-50 dark:bg-surface-900">
        <div class="py-6">
          <div class="mx-auto flex justify-center py-2 px-5">
            <slot />
          </div>
        </div>
      </main>
    </div>
  </div>
</body>
</html>


