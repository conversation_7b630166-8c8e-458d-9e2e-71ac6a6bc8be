<template>
  <div class="bg-surface-0 dark:bg-surface-900 rounded-lg border border-surface-200 dark:border-surface-700 p-6">
    <h3 class="text-lg font-bold mb-4 text-surface-900 dark:text-surface-0">tRPC Connection Test</h3>
    
    <div class="space-y-4">
      <div class="flex gap-3">
        <VButton
          @click="testBrands"
          :disabled="loading"
          severity="info"
        >
          Test Brands
        </VButton>

        <VButton
          @click="testCategories"
          :disabled="loading"
          severity="success"
        >
          Test Categories
        </VButton>
      </div>
      
      <div v-if="loading" class="text-blue-600 font-medium">
        Loading...
      </div>
      
      <VMessage v-if="error" severity="error">
        <strong>Error:</strong> {{ error }}
      </VMessage>

      <VMessage v-if="result" severity="success">
        <strong>Success:</strong> Loaded {{ Array.isArray(result) ? result.length : 1 }} item(s)
        <details class="mt-2">
          <summary class="cursor-pointer font-medium">View Data</summary>
          <pre class="mt-2 text-xs overflow-auto">{{ JSON.stringify(result, null, 2) }}</pre>
        </details>
      </VMessage>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { trpc } from '@/lib/trpc';
import VButton from '@/volt/Button.vue';
import VMessage from '@/volt/Message.vue';

const loading = ref(false);
const error = ref<string | null>(null);
const result = ref<any>(null);

const testBrands = async () => {
  loading.value = true;
  error.value = null;
  result.value = null;
  
  try {
    console.log('Testing brands...');
    const brands = await trpc.crud.brand.findMany.query();
    console.log('Brands result:', brands);
    result.value = brands;
  } catch (err: any) {
    console.error('Brands error:', err);
    error.value = err.message || 'Unknown error';
  } finally {
    loading.value = false;
  }
};

const testCategories = async () => {
  loading.value = true;
  error.value = null;
  result.value = null;
  
  try {
    console.log('Testing categories...');
    const categories = await trpc.crud.partCategory.findMany.query();
    console.log('Categories result:', categories);
    result.value = categories;
  } catch (err: any) {
    console.error('Categories error:', err);
    error.value = err.message || 'Unknown error';
  } finally {
    loading.value = false;
  }
};
</script>
