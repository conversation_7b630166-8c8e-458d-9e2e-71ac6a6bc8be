<template>
  <div class="catalog-item-wizard-editor">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
        Каталожные позиции
      </h3>
      <VButton @click="addCatalogItem" size="small" icon="pi pi-plus">
        Добавить позицию
      </VButton>
    </div>

    <div v-if="modelValue.length === 0" class="text-center py-8 text-surface-500">
      Добавьте хотя бы одну каталожную позицию
    </div>

    <div v-else class="space-y-4">
      <VCard 
        v-for="(item, index) in modelValue" 
        :key="index"
        class="border border-surface-200 dark:border-surface-700"
      >
        <template #content>
          <div class="p-4">
            <!-- Заголовок карточки -->
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center gap-3">
                <VTag 
                  :value="`Позиция ${index + 1}`" 
                  severity="secondary" 
                  size="small" 
                />
                <VTag 
                  v-if="item.isExisting" 
                  value="Существующая" 
                  severity="info" 
                  size="small" 
                />
                <VTag 
                  v-else 
                  value="Новая" 
                  severity="success" 
                  size="small" 
                />
              </div>
              <VButton
                @click="removeCatalogItem(index)"
                severity="danger"
                size="small"
                text
                icon="pi pi-trash"
              />
            </div>

            <!-- Переключатель типа позиции -->
            <div class="flex items-center gap-4 mb-4">
              <VRadioButton
                v-model="item.isExisting"
                :value="false"
                :input-id="`new-${index}`"
                name="itemType"
              />
              <label :for="`new-${index}`" class="text-sm font-medium">
                Создать новую позицию
              </label>
              
              <VRadioButton
                v-model="item.isExisting"
                :value="true"
                :input-id="`existing-${index}`"
                name="itemType"
              />
              <label :for="`existing-${index}`" class="text-sm font-medium">
                Выбрать существующую
              </label>
            </div>

            <!-- Поиск существующей позиции -->
            <div v-if="item.isExisting" class="mb-4">
              <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                Поиск каталожной позиции *
              </label>
              <VAutoComplete
                :model-value="getDisplayLabel(item.existingCatalogItem)"
                @update:model-value="onItemSelect(index, $event)"
                :suggestions="catalogItemSuggestions"
                @complete="searchCatalogItems"
                field="displayLabel"
                placeholder="Поиск по артикулу или бренду..."
                class="w-full"
                dropdown
              >
                <template #option="{ option }">
                  <div class="flex items-center gap-2">
                    <span class="font-mono font-medium">{{ option.sku }}</span>
                    <VTag :value="option.brand?.name" severity="secondary" size="small" />
                  </div>
                </template>
              </VAutoComplete>
            </div>

            <!-- Создание новой позиции -->
            <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Артикул (SKU) *
                </label>
                <VInputText
                  v-model="item.sku"
                  placeholder="Например: 12345-ABC"
                  class="w-full"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Бренд *
                </label>
                <div class="flex gap-2">
                  <VAutoComplete
                    v-model="item.selectedBrand"
                    :suggestions="brandSuggestions"
                    @complete="searchBrands"
                    option-label="name"
                    placeholder="Поиск бренда..."
                    class="flex-1"
                    dropdown
                  />
                  <VButton
                    @click="showCreateBrand = true"
                    severity="secondary"
                    outlined
                    size="small"
                    icon="pi pi-plus"
                    v-tooltip="'Создать новый бренд'"
                  />
                </div>
              </div>

              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Описание
                </label>
                <VInputText
                  v-model="item.description"
                  placeholder="Описание каталожной позиции..."
                  class="w-full"
                />
              </div>
            </div>

            <!-- Точность применимости -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Точность применимости *
                </label>
                <VSelect
                  v-model="item.accuracy"
                  :options="accuracyOptions"
                  option-label="label"
                  option-value="value"
                  placeholder="Выберите точность"
                  class="w-full"
                />
              </div>

              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Примечания
                </label>
                <VInputText
                  v-model="item.notes"
                  placeholder="Дополнительные примечания..."
                  class="w-full"
                />
              </div>
            </div>
          </div>
        </template>
      </VCard>
    </div>

    <!-- Диалог создания бренда -->
    <QuickCreateBrand
      v-model:visible="showCreateBrand"
      @created="onBrandCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useTrpc } from '@/composables/useTrpc'
import VCard from '@/volt/Card.vue'
import VButton from '@/volt/Button.vue'
import VTag from '@/volt/Tag.vue'
import VRadioButton from 'primevue/radiobutton'
import VAutoComplete from '@/volt/AutoComplete.vue'
import VInputText from '@/volt/InputText.vue'
import VSelect from '@/volt/Select.vue'
import QuickCreateBrand from '../parts/QuickCreateBrand.vue'

// Props
interface Props {
  modelValue: any[]
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'update:modelValue', value: any[]): void
}

const emit = defineEmits<Emits>()

// Composables
const { catalogItems, brands } = useTrpc()

// Состояние
const catalogItemSuggestions = ref<any[]>([])
const brandSuggestions = ref<any[]>([])
const showCreateBrand = ref(false)

// Опции точности применимости
const accuracyOptions = [
  { label: 'Точное совпадение', value: 'EXACT_MATCH' },
  { label: 'Совпадение с примечаниями', value: 'MATCH_WITH_NOTES' },
  { label: 'Требует доработки', value: 'REQUIRES_MODIFICATION' },
  { label: 'Частичное совпадение', value: 'PARTIAL_MATCH' }
]

// Методы
const addCatalogItem = () => {
  const newItem = {
    // Для создания новой позиции
    sku: '',
    brandId: '',
    selectedBrand: null,
    description: '',
    
    // Для поиска существующей позиции
    isExisting: false,
    existingCatalogItem: null,
    
    // Уровень точности применимости
    accuracy: 'EXACT_MATCH',
    notes: ''
  }
  
  const updatedValue = [...props.modelValue, newItem]
  emit('update:modelValue', updatedValue)
}

const removeCatalogItem = (index: number) => {
  const updatedValue = props.modelValue.filter((_, i) => i !== index)
  emit('update:modelValue', updatedValue)
}

const getDisplayLabel = (item: any) => {
  if (!item) return '';
  return typeof item === 'object' ? `${item.sku} (${item.brand?.name || 'Без бренда'})` : item;
};

const onItemSelect = (index: number, selectedItem: any) => {
  const updatedValue = [...props.modelValue];
  if (typeof selectedItem === 'object') {
    updatedValue[index].existingCatalogItem = selectedItem;
  }
  emit('update:modelValue', updatedValue);
};

const searchCatalogItems = async (event: any) => {
  try {
    const query = event.query.toLowerCase()
    const result = await catalogItems.findMany({
      where: {
        OR: [
          { sku: { contains: query, mode: 'insensitive' } },
          { brand: { name: { contains: query, mode: 'insensitive' } } }
        ]
      },
      include: {
        brand: true
      },
      take: 10
    })
    
    if (result) {
      catalogItemSuggestions.value = result.map((item: any) => ({
        ...item,
        displayLabel: `${item.sku} (${item.brand?.name || 'Без бренда'})`
      }))
    }
  } catch (err) {
    console.error('Ошибка поиска каталожных позиций:', err)
  }
}

const searchBrands = async (event: any) => {
  try {
    const query = event.query.toLowerCase()
    const result = await brands.findMany({
      where: {
        name: { contains: query, mode: 'insensitive' }
      },
      take: 10
    })
    if (result) {
      brandSuggestions.value = result
    }
  } catch (err) {
    console.error('Ошибка поиска брендов:', err)
  }
}

const onBrandCreated = (brand: any) => {
  brandSuggestions.value = [brand, ...brandSuggestions.value]
}
</script>
