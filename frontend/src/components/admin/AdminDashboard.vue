<template>
  <div class="space-y-8">
    <!-- Заголовок страницы -->
    <div>
      <h1 class="text-2xl font-bold text-surface-900">Дашборд</h1>
      <p class="mt-1 text-sm text-surface-600">
        Обзор системы управления каталогом запчастей
      </p>
    </div>

    <!-- Статистические карточки -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <!-- Общее количество пользователей -->
      <Card>
        <template #content>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="pi pi-users text-2xl text-primary-600"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-surface-500 truncate">
                  Пользователи
                </dt>
                <dd class="text-lg font-medium text-surface-900">
                  1,234
                </dd>
              </dl>
            </div>
          </div>
        </template>
      </Card>

      <!-- Количество магазинов -->
      <Card>
        <template #content>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="pi pi-building text-2xl text-primary-600"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-surface-500 truncate">
                  Магазины
                </dt>
                <dd class="text-lg font-medium text-surface-900">
                  89
                </dd>
              </dl>
            </div>
          </div>
        </template>
      </Card>

      <!-- Количество запчастей -->
      <Card>
        <template #content>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="pi pi-box text-2xl text-primary-600"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-surface-500 truncate">
                  Запчасти
                </dt>
                <dd class="text-lg font-medium text-surface-900">
                  45,678
                </dd>
              </dl>
            </div>
          </div>
        </template>
      </Card>

      <!-- Активные сессии -->
      <Card>
        <template #content>
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <i class="pi pi-bolt text-2xl text-primary-600"></i>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-surface-500 truncate">
                  Активные сессии
                </dt>
                <dd class="text-lg font-medium text-surface-900">
                  156
                </dd>
              </dl>
            </div>
          </div>
        </template>
      </Card>
    </div>

    <!-- Основной контент -->
    <div class="grid grid-cols-1 gap-8">
      <!-- Последние действия -->
      <Card>
        <template #header>
          <h3 class="text-lg leading-6 font-medium text-surface-900">
            Последние действия
          </h3>
        </template>
        <template #content>
          <div class="space-y-4">
            <div class="flex items-center p-4 bg-surface-50 dark:bg-surface-800 rounded-lg">
              <div class="flex-shrink-0">
                <i class="pi pi-plus-circle text-green-600 text-xl"></i>
              </div>
              <div class="ml-4 flex-1">
                <div class="text-sm font-medium text-surface-900 dark:text-surface-0">
                  Создан новый шаблон атрибута "Мощность двигателя"
                </div>
                <div class="text-sm text-surface-500 dark:text-surface-400">
                  2 часа назад
                </div>
              </div>
            </div>

            <div class="flex items-center p-4 bg-surface-50 dark:bg-surface-800 rounded-lg">
              <div class="flex-shrink-0">
                <i class="pi pi-pencil text-blue-600 text-xl"></i>
              </div>
              <div class="ml-4 flex-1">
                <div class="text-sm font-medium text-surface-900 dark:text-surface-0">
                  Обновлена группа атрибутов "Двигатель"
                </div>
                <div class="text-sm text-surface-500 dark:text-surface-400">
                  5 часов назад
                </div>
              </div>
            </div>

            <div class="flex items-center p-4 bg-surface-50 dark:bg-surface-800 rounded-lg">
              <div class="flex-shrink-0">
                <i class="pi pi-cog text-orange-600 text-xl"></i>
              </div>
              <div class="ml-4 flex-1">
                <div class="text-sm font-medium text-surface-900 dark:text-surface-0">
                  Добавлена новая запчасть с 8 атрибутами
                </div>
                <div class="text-sm text-surface-500 dark:text-surface-400">
                  1 день назад
                </div>
              </div>
            </div>
          </div>
        </template>
      </Card>
    </div>

    <!-- Графики и аналитика -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- График регистраций -->
      <Card class="lg:col-span-2">
        <template #header>
          <h3 class="text-lg leading-6 font-medium text-surface-900">
            Регистрации пользователей
          </h3>
        </template>
        <template #content>
          <div class="h-64 flex items-center justify-center text-surface-500">
            <div class="text-center">
              <i class="pi pi-chart-line text-4xl mb-4"></i>
              <p>График будет добавлен позже</p>
            </div>
          </div>
        </template>
      </Card>

      <!-- Топ категорий -->
      <Card>
        <template #header>
          <h3 class="text-lg leading-6 font-medium text-surface-900">
            Популярные категории
          </h3>
        </template>
        <template #content>
          <div class="space-y-3">
            <div v-for="category in topCategories" :key="category.name" class="flex justify-between items-center">
              <span class="text-sm font-medium text-surface-900">{{ category.name }}</span>
              <span class="text-sm text-surface-500">{{ category.count }}</span>
            </div>
          </div>
        </template>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Card from '@/volt/Card.vue'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import { navigate } from 'astro:transitions/client'



// Данные для топ категорий
const topCategories = ref([
  { name: 'Двигатель', count: '1,234' },
  { name: 'Трансмиссия', count: '987' },
  { name: 'Подвеска', count: '756' },
  { name: 'Тормоза', count: '543' },
  { name: 'Электрика', count: '432' }
])

// Методы
const navigateTo = (path: string) => {
  // window.location.href = path
  navigate(path)

}
</script>

