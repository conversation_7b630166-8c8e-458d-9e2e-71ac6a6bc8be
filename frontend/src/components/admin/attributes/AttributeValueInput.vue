<template>
  <div class="attribute-value-input">
    <!-- Строковый тип -->
    <div v-if="template.dataType === 'STRING'">
      <!-- Если есть допустимые значения - показываем autocomplete -->
      <VAutoComplete
        v-if="template.allowedValues && template.allowedValues.length > 0"
        v-model="localValue"
        :suggestions="allowedValueSuggestions"
        @complete="filterAllowedValues"
        :placeholder="placeholder"
        :class="inputClass"
        :invalid="!!error"
        show-clear
        dropdown
        @update:model-value="handleDropdownChange"
      />
      <!-- Иначе обычный текстовый input -->
      <VInputText
        v-else
        v-model="localValue"
        :placeholder="placeholder"
        :class="inputClass"
        :invalid="!!error"
        @update:model-value="handleStringChange"
      />
    </div>

    <!-- Числовой тип -->
    <VInputNumber
      v-else-if="template.dataType === 'NUMBER'"
      v-model="localValue"
      :placeholder="placeholder"
      :class="inputClass"
      :invalid="!!error"
      :min="template.minValue || undefined"
      :max="template.maxValue || undefined"
      :use-grouping="false"
      :fraction-digits="getFractionDigits()"
      @update:model-value="handleNumberChange"
    />

    <!-- Логический тип -->
    <div v-else-if="template.dataType === 'BOOLEAN'" class="flex items-center gap-2">
      <VCheckbox
        v-model="localValue"
        :input-id="`checkbox-${template.id}`"
        binary
        :class="{ 'p-invalid': !!error }"
        @update:model-value="handleBooleanChange"
      />
      <label :for="`checkbox-${template.id}`" class="text-sm text-surface-700 dark:text-surface-300">
        {{ template.title }}
      </label>
    </div>

    <!-- Дата -->
    <DatePicker
      v-else-if="template.dataType === 'DATE'"
      v-model="localValue"
      :placeholder="placeholder"
      :class="inputClass"
      :invalid="!!error"
      date-format="dd.mm.yy"
      show-icon
      @date-select="handleChange"
    />

    <!-- JSON тип -->
    <VTextarea
      v-else-if="template.dataType === 'JSON'"
      v-model="localValue"
      :placeholder="placeholder || 'Введите JSON...'"
      :class="inputClass"
      :invalid="!!error"
      rows="3"
      @input="handleChange"
    />

    <!-- Единица измерения (если есть) -->
    <div v-if="template.unit && template.dataType !== 'BOOLEAN'" class="mt-1">
      <small class="text-surface-500 dark:text-surface-400">
        Единица измерения: {{ getUnitLabel(template.unit) }}
      </small>
    </div>

    <!-- Ошибка валидации -->
    <small v-if="error" class="p-error block mt-1">
      {{ error }}
    </small>

    <!-- Описание шаблона -->
    <small v-if="template.description && !error" class="text-surface-500 dark:text-surface-400 block mt-1">
      {{ template.description }}
    </small>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import VInputText from '@/volt/InputText.vue';
import VInputNumber from '@/volt/InputNumber.vue';
import VCheckbox from '@/volt/Checkbox.vue';
import VTextarea from '@/volt/Textarea.vue';
import DatePicker from 'primevue/datepicker';
import VAutoComplete from '@/volt/AutoComplete.vue';

// Интерфейс шаблона атрибута
interface AttributeTemplate {
  id: number;
  name: string;
  title: string;
  description?: string;
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
  unit?: string;
  isRequired: boolean;
  minValue?: number;
  maxValue?: number;
  allowedValues?: string[];
}

// Props
interface Props {
  modelValue: any;
  template: AttributeTemplate;
  placeholder?: string;
  disabled?: boolean;
  size?: 'small' | 'normal' | 'large';
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '',
  disabled: false,
  size: 'normal'
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: any];
  'change': [value: any];
  'error': [error: string | null];
}>();

// Локальное состояние
const localValue = ref(props.modelValue);
const error = ref<string | null>(null);

// Вычисляемые свойства
const inputClass = computed(() => {
  const classes = ['w-full'];
  
  // Базовые стили для всех инпутов
  classes.push('border border-surface-300 dark:border-surface-600 rounded-lg bg-surface-0 dark:bg-surface-800 text-surface-900 dark:text-surface-0');
  
  if (props.size === 'small') {
    classes.push('p-2 text-sm');
  } else if (props.size === 'large') {
    classes.push('p-4 text-lg');
  } else {
    classes.push('p-3 text-base');
  }
  
  if (props.disabled) {
    classes.push('opacity-50 cursor-not-allowed bg-surface-100 dark:bg-surface-700');
  }
  
  // Стили для состояния ошибки
  if (error.value) {
    classes.push('border-red-500 dark:border-red-400');
  }
  
  // Стили для фокуса
  classes.push('focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary');
  
  return classes.join(' ');
});

// Удалено неиспользуемое computed свойство allowedValueOptions

// Данные для автокомплита
const allowedValueSuggestions = ref<string[]>([]);

// Фильтрация допустимых значений для AutoComplete
const filterAllowedValues = (event: any) => {
  const query = event.query.toLowerCase();
  if (!props.template.allowedValues) {
    allowedValueSuggestions.value = [];
    return;
  }
  
  // Если запрос пустой - показываем все значения
  if (!query) {
    allowedValueSuggestions.value = props.template.allowedValues;
    return;
  }
  
  // Фильтруем по вхождению подстроки
  allowedValueSuggestions.value = props.template.allowedValues.filter(value =>
    value.toLowerCase().includes(query)
  );
};

// Инициализация предзагруженных данных
const initializeAllowedValues = () => {
  if (props.template.allowedValues) {
    allowedValueSuggestions.value = props.template.allowedValues;
  }
};

// Инициализация при монтировании
onMounted(() => {
  initializeAllowedValues();
});

// Отслеживание изменений шаблона
watch(() => props.template.allowedValues, () => {
  initializeAllowedValues();
});

// Вспомогательные функции
const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    'MM': 'мм', 'INCH': 'дюймы', 'FT': 'футы',
    'G': 'г', 'KG': 'кг', 'T': 'т', 'LB': 'фунты',
    'ML': 'мл', 'L': 'л', 'GAL': 'галлоны',
    'PCS': 'шт', 'SET': 'комплект', 'PAIR': 'пара',
    'BAR': 'бар', 'PSI': 'PSI',
    'KW': 'кВт', 'HP': 'л.с.',
    'NM': 'Н⋅м', 'RPM': 'об/мин',
    'C': '°C', 'F': '°F',
    'PERCENT': '%'
  };
  return labels[unit] || unit;
};

const getFractionDigits = () => {
  // Определяем количество знаков после запятой на основе единицы измерения
  if (props.template.unit === 'MM') return 2;
  if (props.template.unit === 'INCH') return 3;
  if (props.template.unit === 'PERCENT') return 1;
  return 2; // По умолчанию
};

// Валидация
const validateValue = (value: any): string | null => {
  // Проверка обязательности
  if (props.template.isRequired && (value === null || value === undefined || value === '')) {
    return `Поле "${props.template.title}" обязательно для заполнения`;
  }

  // Если значение пустое и поле не обязательное - валидация пройдена
  if (value === null || value === undefined || value === '') {
    return null;
  }

  // Валидация по типу данных
  switch (props.template.dataType) {
    case 'STRING':
      if (typeof value !== 'string') {
        return 'Значение должно быть строкой';
      }
      
      // Проверка допустимых значений
      if (props.template.allowedValues && props.template.allowedValues.length > 0) {
        if (!props.template.allowedValues.includes(value)) {
          return `Значение должно быть одним из: ${props.template.allowedValues.join(', ')}`;
        }
      }
      break;

    case 'NUMBER':
      // Пустое значение допустимо, если поле необязательное
      if (value === '' || value === null || value === undefined) {
        return null;
      }

      /*
       * PrimeVue InputNumber во время ввода может кратковременно отдавать строку
       * с символами «-», «,», «.»  или пустую строку. Такие промежуточные
       * состояния не считаем ошибкой, чтобы не отвлекать пользователя.
       */
      if (typeof value === 'string') {
        const sanitized = value.replace(/\s+/g, '');
        if (sanitized === '-' || sanitized === ',' || sanitized === '.' || sanitized === '-,' || sanitized === '-.' ) {
          return null;
        }
      }

      const numValue = Number(value);
      if (Number.isNaN(numValue)) {
        return 'Значение должно быть числом';
      }
       
      // Проверка диапазона
      if (props.template.minValue !== null && props.template.minValue !== undefined && numValue < props.template.minValue) {
        return `Значение не может быть меньше ${props.template.minValue}`;
      }
      
      if (props.template.maxValue !== null && props.template.maxValue !== undefined && numValue > props.template.maxValue) {
        return `Значение не может быть больше ${props.template.maxValue}`;
      }
      break;

    case 'BOOLEAN':
      if (typeof value !== 'boolean') {
        return 'Значение должно быть логическим (true/false)';
      }
      break;

    case 'DATE':
      if (!(value instanceof Date) && typeof value !== 'string') {
        return 'Значение должно быть датой';
      }
      break;

    case 'JSON':
      if (typeof value === 'string') {
        try {
          JSON.parse(value);
        } catch {
          return 'Значение должно быть корректным JSON';
        }
      }
      break;
  }

  return null;
};

// Обработчики событий
const handleChange = () => {
  // Валидация
  const validationError = validateValue(localValue.value);
  error.value = validationError;
  
  // Эмитим события
  emit('update:modelValue', localValue.value);
  emit('change', localValue.value);
  emit('error', validationError);
};

// Обработчик для числовых значений
const handleNumberChange = (value: any) => {
  // VInputNumber может возвращать null или пустую строку
  if (value === null || value === '') {
    localValue.value = '';
  } else {
    localValue.value = value;
  }
  handleChange();
};

// Обработчик для булевых значений
const handleBooleanChange = (value: any) => {
  localValue.value = value;
  handleChange();
};

// Обработчик для строковых значений
const handleStringChange = (value: any) => {
  localValue.value = value;
  handleChange();
};

// Обработчик для dropdown
const handleDropdownChange = (value: any) => {
  localValue.value = value;
  handleChange();
};

// Watchers
watch(() => props.modelValue, (newValue) => {
  // Для числовых значений правильно обрабатываем null/undefined
  if (props.template.dataType === 'NUMBER' && (newValue === null || newValue === undefined)) {
    localValue.value = null;
  } else {
    localValue.value = newValue;
  }
  
  // Валидируем новое значение
  const validationError = validateValue(localValue.value);
  error.value = validationError;
  emit('error', validationError);
}, { immediate: true });

// Экспорт функции валидации для внешнего использования
defineExpose({
  validate: () => {
    const validationError = validateValue(localValue.value);
    error.value = validationError;
    emit('error', validationError);
    return validationError === null;
  },
  hasError: () => error.value !== null,
  getError: () => error.value
});
</script>
