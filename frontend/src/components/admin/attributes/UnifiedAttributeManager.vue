<template>
  <div class="unified-attribute-manager">
    <!-- Заголовок -->
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
            Управление атрибутами
          </h1>
          <p class="text-surface-600 dark:text-surface-400 mt-2">
            Единая система управления атрибутами запчастей
          </p>
        </div>
        <div class="flex gap-3">
          <VButton 
            @click="loadData" 
            :loading="loading" 
            severity="secondary" 
            outlined 
            label="Обновить" 
            icon="pi pi-refresh" 
          />
        </div>
      </div>
    </div>

    <!-- Вкладки -->
    <VTabs :value="activeTab">
      <VTabList>
        <VTab value="templates">Шаблоны атрибутов</VTab>
        <VTab value="groups">Группы</VTab>
        <VTab value="parts">Атрибуты запчастей</VTab>
      </VTabList>

      <VTabPanels class="mt-4">
        <!-- Вкладка: Шаблоны атрибутов -->
        <VTabPanel value="templates">
        <div class="space-y-6">
          <!-- Поиск и фильтры -->
          <div class="flex gap-4 items-center">
            <div class="flex-1">
              <VInputText
                v-model="templateSearch"
                placeholder="Поиск шаблонов..."
                class="w-full"
                @input="filterTemplates"
              />
            </div>
            <VDropdown
              v-model="selectedGroupFilter"
              :options="groups"
              option-label="name"
              option-value="id"
              placeholder="Все группы"
              class="w-48"
              @change="filterTemplates"
              show-clear
            />
            <VButton
              @click="showCreateTemplateDialog = true"
              label="Создать шаблон"
              icon="pi pi-plus"
            />
          </div>

          <!-- Таблица шаблонов -->
          <VDataTable
            :value="filteredTemplates"
            :loading="loading"
            paginator
            :rows="20"
            :rows-per-page-options="[10, 20, 50]"
            class="p-datatable-sm"
          >
            <VColumn field="title" header="Название" sortable>
              <template #body="{ data }">
                <div>
                  <div class="font-medium">{{ data.title }}</div>
                  <div class="text-sm text-surface-500">{{ data.name }}</div>
                </div>
              </template>
            </VColumn>
            
            <VColumn field="group.name" header="Группа" sortable>
              <template #body="{ data }">
                <VTag v-if="data.group" :value="data.group.name" severity="secondary" />
                <span v-else class="text-surface-400">Без группы</span>
              </template>
            </VColumn>
            
            <VColumn field="dataType" header="Тип" sortable>
              <template #body="{ data }">
                <VTag :value="getDataTypeLabel(data.dataType)" :severity="getDataTypeSeverity(data.dataType)" />
              </template>
            </VColumn>
            
            <VColumn field="unit" header="Единица">
              <template #body="{ data }">
                <span v-if="data.unit">{{ getUnitLabel(data.unit) }}</span>
                <span v-else class="text-surface-400">—</span>
              </template>
            </VColumn>
            
            <VColumn field="isRequired" header="Обязательный">
              <template #body="{ data }">
                <i :class="data.isRequired ? 'pi pi-check text-green-500' : 'pi pi-times text-surface-400'"></i>
              </template>
            </VColumn>
            
            <VColumn header="Действия" class="w-32">
              <template #body="{ data }">
                <div class="flex gap-2">
                  <VButton
                    @click="editTemplate(data)"
                    icon="pi pi-pencil"
                    size="small"
                    text
                    severity="secondary"
                    title="Редактировать"
                  />
                  <VButton
                    @click="deleteTemplate(data)"
                    icon="pi pi-trash"
                    size="small"
                    text
                    severity="danger"
                    title="Удалить"
                  />
                </div>
              </template>
            </VColumn>
          </VDataTable>
        </div>
        </VTabPanel>

        <!-- Вкладка: Группы -->
        <VTabPanel value="groups">
        <div class="space-y-6">
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-medium">Группы атрибутов</h3>
            <VButton
              @click="showCreateGroupDialog = true"
              label="Создать группу"
              icon="pi pi-plus"
            />
          </div>

          <VDataTable
            :value="groups"
            :loading="loading"
            class="p-datatable-sm"
          >
            <VColumn field="name" header="Название" sortable />
            <VColumn field="description" header="Описание" />
            <VColumn field="_count.templates" header="Шаблонов" sortable>
              <template #body="{ data }">
                {{ data._count?.templates || 0 }}
              </template>
            </VColumn>
            <VColumn header="Действия" class="w-32">
              <template #body="{ data }">
                <div class="flex gap-2">
                  <VButton
                    @click="editGroup(data)"
                    icon="pi pi-pencil"
                    size="small"
                    text
                    severity="secondary"
                    title="Редактировать"
                  />
                  <VButton
                    @click="deleteGroup(data)"
                    icon="pi pi-trash"
                    size="small"
                    text
                    severity="danger"
                    title="Удалить"
                    :disabled="data._count?.templates > 0"
                  />
                </div>
              </template>
            </VColumn>
          </VDataTable>
        </div>
        </VTabPanel>

        <!-- Вкладка: Атрибуты запчастей -->
        <VTabPanel value="parts">
        <div class="space-y-6">
          <!-- Поиск запчасти -->
          <div class="flex gap-4 items-center">
            <div class="flex-1">
              <VAutoComplete
                v-model="selectedPart"
                :suggestions="partSuggestions"
                @complete="searchParts"
                option-label="name"
                placeholder="Найти запчасть..."
                class="w-full"
                dropdown
              >
                <template #option="{ option }">
                  <div>
                    <div class="font-medium">{{ option.name }}</div>
                    <div class="text-sm text-surface-500">{{ option.category?.name }}</div>
                  </div>
                </template>
              </VAutoComplete>
            </div>
          </div>

          <!-- Атрибуты выбранной запчасти -->
          <div v-if="selectedPart">
            <div class="mb-4">
              <h3 class="text-lg font-medium">Атрибуты запчасти: {{ selectedPart.name }}</h3>
            </div>
            
            <SimpleAttributeManager 
              v-model="partAttributes" 
              @update:model-value="savePartAttributes"
            />
          </div>
          
          <div v-else class="text-center py-8 text-surface-500">
            <i class="pi pi-search text-3xl mb-2 block"></i>
            <p>Выберите запчасть для управления её атрибутами</p>
          </div>
        </div>
        </VTabPanel>
      </VTabPanels>
    </VTabs>

    <!-- Диалоги создания/редактирования будут добавлены позже -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import VTabs from '@/volt/Tabs.vue';
import VTabList from '@/volt/TabList.vue';
import VTab from '@/volt/Tab.vue';
import VTabPanels from '@/volt/TabPanels.vue';
import VTabPanel from '@/volt/TabPanel.vue';
import VButton from '@/volt/Button.vue';
import VInputText from '@/volt/InputText.vue';
import VDropdown from '@/volt/Dropdown.vue';
import VDataTable from '@/volt/DataTable.vue';
import VColumn from 'primevue/column';
import VTag from '@/volt/Tag.vue';
import VAutoComplete from '@/volt/AutoComplete.vue';
import SimpleAttributeManager from '@/components/admin/parts/SimpleAttributeManager.vue';

// Состояние
const loading = ref(false);
const activeTab = ref('templates');
const templates = ref<any[]>([]);
const groups = ref<any[]>([]);
const templateSearch = ref('');
const selectedGroupFilter = ref(null);
const selectedPart = ref<any>(null);
const partSuggestions = ref<any[]>([]);
const partAttributes = ref<any[]>([]);

// Диалоги
const showCreateTemplateDialog = ref(false);
const showCreateGroupDialog = ref(false);

// tRPC клиент
const { client } = useTrpc();

// Фильтрованные шаблоны
const filteredTemplates = computed(() => {
  let result = templates.value;
  
  if (templateSearch.value) {
    const search = templateSearch.value.toLowerCase();
    result = result.filter(t => 
      t.title.toLowerCase().includes(search) ||
      t.name.toLowerCase().includes(search) ||
      t.description?.toLowerCase().includes(search)
    );
  }
  
  if (selectedGroupFilter.value) {
    result = result.filter(t => t.groupId === selectedGroupFilter.value);
  }
  
  return result;
});

// Загрузка данных
const loadData = async () => {
  loading.value = true;
  try {
    const [templatesData, groupsData] = await Promise.all([
      client.attributeTemplates.findMany(),
      client.attributeTemplates.findAllGroups()
    ]);
    
    templates.value = templatesData || [];
    groups.value = groupsData || [];
  } catch (error) {
    console.error('Ошибка загрузки данных:', error);
  } finally {
    loading.value = false;
  }
};

// Поиск запчастей
const searchParts = async (event: any) => {
  try {
    const parts = await client.parts.findMany({
      where: {
        name: {
          contains: event.query,
          mode: 'insensitive'
        }
      },
      include: {
        category: true
      },
      take: 10
    });
    partSuggestions.value = parts || [];
  } catch (error) {
    console.error('Ошибка поиска запчастей:', error);
    partSuggestions.value = [];
  }
};

// Загрузка атрибутов запчасти
const loadPartAttributes = async () => {
  if (!selectedPart.value) return;
  
  try {
    const attributes = await client.partAttributes.findByPartId({ 
      partId: selectedPart.value.id 
    });
    
    partAttributes.value = (attributes || []).map((attr: any) => ({
      id: attr.id,
      templateId: attr.templateId,
      value: attr.value,
      template: attr.template,
      templateTitle: attr.template?.title,
      templateDataType: attr.template?.dataType,
      templateUnit: attr.template?.unit,
      templateGroup: attr.template?.group?.name,
      templateDescription: attr.template?.description,
    }));
  } catch (error) {
    console.error('Ошибка загрузки атрибутов запчасти:', error);
  }
};

// Сохранение атрибутов запчасти
const savePartAttributes = (newAttributes: any[]) => {
  partAttributes.value = newAttributes;
  // Дополнительная логика сохранения если нужна
};

// Утилиты
const getDataTypeLabel = (dataType: string) => {
  const labels: Record<string, string> = {
    STRING: 'Строка',
    NUMBER: 'Число',
    BOOLEAN: 'Логическое',
    DATE: 'Дата',
    JSON: 'JSON'
  };
  return labels[dataType] || dataType;
};

const getDataTypeSeverity = (dataType: string) => {
  const severities: Record<string, string> = {
    STRING: 'info',
    NUMBER: 'success',
    BOOLEAN: 'warning',
    DATE: 'secondary',
    JSON: 'danger'
  };
  return severities[dataType] || 'info';
};

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    MM: 'мм', INCH: 'дюймы', FT: 'футы',
    G: 'г', KG: 'кг', T: 'т', LB: 'фунты',
    ML: 'мл', L: 'л', GAL: 'галлоны',
    PCS: 'шт', SET: 'комплект', PAIR: 'пара',
    BAR: 'бар', PSI: 'PSI',
    KW: 'кВт', HP: 'л.с.',
    NM: 'Н⋅м', RPM: 'об/мин',
    C: '°C', F: '°F', PERCENT: '%'
  };
  return labels[unit] || unit;
};

// Методы действий
const filterTemplates = () => {
  // Фильтрация происходит через computed
};

const editTemplate = (template: any) => {
  console.log('Редактирование шаблона:', template);
  // TODO: Открыть диалог редактирования
};

const deleteTemplate = async (template: any) => {
  if (!confirm(`Вы уверены, что хотите удалить шаблон "${template.title}"?`)) {
    return;
  }

  try {
    await client.attributeTemplates.delete({ id: template.id });
    await loadData(); // Перезагружаем данные
    console.log('Шаблон удален:', template.title);
  } catch (error) {
    console.error('Ошибка удаления шаблона:', error);
    alert('Не удалось удалить шаблон. Возможно, он используется в атрибутах запчастей.');
  }
};

const editGroup = (group: any) => {
  console.log('Редактирование группы:', group);
  // TODO: Открыть диалог редактирования
};

const deleteGroup = async (group: any) => {
  if (group._count?.templates > 0) {
    alert('Нельзя удалить группу, в которой есть шаблоны. Сначала удалите или переместите все шаблоны.');
    return;
  }

  if (!confirm(`Вы уверены, что хотите удалить группу "${group.name}"?`)) {
    return;
  }

  try {
    await client.attributeTemplates.deleteGroup({ id: group.id });
    await loadData(); // Перезагружаем данные
    console.log('Группа удалена:', group.name);
  } catch (error) {
    console.error('Ошибка удаления группы:', error);
    alert('Не удалось удалить группу.');
  }
};

// Watchers
import { watch } from 'vue';

watch(selectedPart, (newPart) => {
  if (newPart) {
    loadPartAttributes();
  } else {
    partAttributes.value = [];
  }
});

// Инициализация
onMounted(() => {
  loadData();
});
</script>
