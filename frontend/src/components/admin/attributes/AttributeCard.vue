<template>
  <VCard
    class="attribute-card border border-surface-200 dark:border-surface-700 hover:border-primary transition-colors">
    <template #content>
      <div class="p-4">
        <!-- Компактный режим -->
        <div v-if="mode === 'compact'" class="flex items-center justify-between">
          <div class="flex items-center gap-3 flex-1">
            <i class="pi pi-tag text-blue-600"></i>
            <div class="flex-1">
              <div class="font-medium text-surface-900 dark:text-surface-0">
                {{ attribute.template?.title || attribute.templateTitle }}
              </div>
              <div class="text-sm text-surface-600 dark:text-surface-400">
                {{ formattedValue }}
              </div>
            </div>
            <div class="flex gap-1">
              <VTag :value="getDataTypeLabel(attribute.template?.dataType || attribute.templateDataType || 'STRING')"
                severity="info" size="small" />
              <VTag v-if="attribute.template?.unit || attribute.templateUnit"
                :value="getUnitLabel(attribute.template?.unit || attribute.templateUnit || '')" severity="success"
                size="small" />
            </div>
          </div>
          <div class="flex gap-2 ml-4">
            <VButton @click="$emit('edit', attribute)" severity="secondary" outlined size="small" icon="pi pi-pencil"
              label="Редактировать" />
            <VButton @click="$emit('remove', attribute)" severity="danger" outlined size="small" icon="pi pi-trash"
              label="Удалить" />
          </div>
        </div>

        <!-- Детальный режим -->
        <div v-else class="space-y-4">
          <div class="flex items-start justify-between">
            <div class="flex items-center gap-2">
              <i class="pi pi-tag text-blue-600"></i>
              <span class="font-medium text-surface-900 dark:text-surface-0">
                {{ attribute.template?.title || attribute.templateTitle }}
              </span>
              <VTag v-if="attribute.template?.isRequired" value="Обязательный" severity="danger" size="small" />
            </div>
            <VTag v-if="attribute.template?.group?.name || attribute.templateGroup"
              :value="attribute.template?.group?.name || attribute.templateGroup" severity="secondary" size="small" />
          </div>

          <!-- Системное имя -->
          <div class="text-xs text-surface-500 dark:text-surface-400 font-mono">
            {{ attribute.template?.name || attribute.templateName }}
          </div>

          <!-- Значение -->
          <div class="bg-surface-50 dark:bg-surface-900 rounded-lg p-3">
            <div class="text-lg font-semibold text-surface-900 dark:text-surface-0 mb-1">
              {{ formattedValue }}
            </div>
            <div class="flex items-center gap-4 text-sm text-surface-600 dark:text-surface-400">
              <div class="flex items-center gap-1">
                <i class="pi pi-info-circle"></i>
                <span>{{ getDataTypeLabel(attribute.template?.dataType || attribute.templateDataType || 'STRING') }}</span>
              </div>
              <div v-if="attribute.template?.unit || attribute.templateUnit" class="flex items-center gap-1">
                <i class="pi pi-calculator"></i>
                <span>{{ getUnitLabel(attribute.template?.unit || attribute.templateUnit || '') }}</span>
              </div>
            </div>
          </div>

          <!-- Описание -->
          <div v-if="attribute.template?.description || attribute.templateDescription"
            class="text-sm text-surface-500 dark:text-surface-400">
            {{ attribute.template?.description || attribute.templateDescription }}
          </div>

          <!-- Валидация (если есть ограничения) -->
          <div v-if="hasValidationRules" class="text-xs text-surface-500 dark:text-surface-400">
            <div class="flex items-center gap-1 mb-1">
              <i class="pi pi-shield-check"></i>
              <span>Правила валидации:</span>
            </div>
            <ul class="list-disc list-inside space-y-1 ml-4">
              <li v-if="attribute.template?.minValue !== null && attribute.template?.minValue !== undefined">
                Минимум: {{ attribute.template.minValue }}
              </li>
              <li v-if="attribute.template?.maxValue !== null && attribute.template?.maxValue !== undefined">
                Максимум: {{ attribute.template.maxValue }}
              </li>
              <li v-if="attribute.template?.allowedValues && attribute.template.allowedValues.length > 0">
                Допустимые значения: {{ attribute.template.allowedValues.join(', ') }}
              </li>
            </ul>
          </div>

          <!-- Действия -->
          <div class="flex justify-end gap-2 pt-2 border-t border-surface-200 dark:border-surface-700">
            <VButton @click="$emit('edit', attribute)" severity="secondary" outlined size="small" icon="pi pi-pencil"
              label="Редактировать" />
            <VButton @click="$emit('remove', attribute)" severity="danger" outlined size="small" icon="pi pi-trash"
              label="Удалить" />
          </div>
        </div>
      </div>
    </template>
  </VCard>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import VCard from '@/volt/Card.vue';
import VButton from '@/volt/Button.vue';
import VTag from '@/volt/Tag.vue';

// Интерфейс атрибута
interface AttributeData {
  id?: number;
  value: string;
  template?: {
    id: number;
    name: string;
    title: string;
    description?: string;
    dataType: string;
    unit?: string;
    isRequired: boolean;
    minValue?: number;
    maxValue?: number;
    allowedValues?: string[];
    group?: {
      id: number;
      name: string;
    };
  };
  // Альтернативные поля для совместимости
  templateTitle?: string;
  templateName?: string;
  templateDataType?: string;
  templateUnit?: string;
  templateGroup?: string;
  templateDescription?: string;
}

// Props
interface Props {
  attribute: AttributeData;
  mode?: 'compact' | 'detailed';
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'detailed'
});

// Emits
const emit = defineEmits<{
  'edit': [attribute: AttributeData];
  'remove': [attribute: AttributeData];
}>();

// Вычисляемые свойства
const formattedValue = computed(() => {
  const value = props.attribute.value;
  const dataType = props.attribute.template?.dataType || props.attribute.templateDataType;
  const unit = props.attribute.template?.unit || props.attribute.templateUnit;

  if (!value) return '—';

  switch (dataType) {
    case 'STRING':
      return value;

    case 'NUMBER':
      const numValue = Number(value);
      if (isNaN(numValue)) return value;
      
      let formatted = numValue.toLocaleString('ru-RU', {
        minimumFractionDigits: 0,
        maximumFractionDigits: getFractionDigits(unit)
      });
      
      if (unit) {
        formatted += ` ${getUnitLabel(unit)}`;
      }
      
      return formatted;

    case 'BOOLEAN':
      return value === 'true' ? 'Да' : 'Нет';

    case 'DATE':
      try {
        const date = new Date(value);
        return date.toLocaleDateString('ru-RU');
      } catch {
        return value;
      }

    case 'JSON':
      try {
        const parsed = JSON.parse(value);
        return JSON.stringify(parsed, null, 2);
      } catch {
        return value;
      }

    default:
      return value;
  }
});

const hasValidationRules = computed(() => {
  const template = props.attribute.template;
  if (!template) return false;
  
  return (
    (template.minValue !== null && template.minValue !== undefined) ||
    (template.maxValue !== null && template.maxValue !== undefined) ||
    (template.allowedValues && template.allowedValues.length > 0)
  );
});

// Вспомогательные функции
const getDataTypeLabel = (dataType: string) => {
  const labels: Record<string, string> = {
    'STRING': 'Строка',
    'NUMBER': 'Число',
    'BOOLEAN': 'Логическое',
    'DATE': 'Дата',
    'JSON': 'JSON'
  };
  return labels[dataType] || dataType;
};

const getUnitLabel = (unit: string) => {
  if (!unit) return '';
  
  const labels: Record<string, string> = {
    'MM': 'мм', 'INCH': 'дюймы', 'FT': 'футы',
    'G': 'г', 'KG': 'кг', 'T': 'т', 'LB': 'фунты',
    'ML': 'мл', 'L': 'л', 'GAL': 'галлоны',
    'PCS': 'шт', 'SET': 'комплект', 'PAIR': 'пара',
    'BAR': 'бар', 'PSI': 'PSI',
    'KW': 'кВт', 'HP': 'л.с.',
    'NM': 'Н⋅м', 'RPM': 'об/мин',
    'C': '°C', 'F': '°F',
    'PERCENT': '%'
  };
  return labels[unit] || unit;
};

const getFractionDigits = (unit?: string) => {
  if (!unit) return 2;
  
  if (unit === 'MM') return 2;
  if (unit === 'INCH') return 3;
  if (unit === 'PERCENT') return 1;
  if (unit === 'KG' || unit === 'G') return 3;
  
  return 2;
};
</script>


