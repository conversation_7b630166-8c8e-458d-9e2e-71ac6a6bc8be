<template>
  <div class="attribute-editor">
    <!-- Заголовок секции -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">
            {{ title || 'Атрибуты' }}
          </h3>
          <p class="text-sm text-surface-600 dark:text-surface-400 mt-1">
            {{ description || 'Управление атрибутами с использованием шаблонов' }}
          </p>
        </div>
        <div class="flex gap-2">
          <VButton
            v-if="showGroupSelector"
            @click="showGroupDialog = true"
            severity="secondary"
            outlined
            size="small"
            icon="pi pi-tags"   
            label="Добавить группу"
          />
          <VButton
            @click="showAddDialog = true"
            size="small"
            label="Добавить атрибут"
          />
        </div>
      </div>

      <!-- Быстрый выбор группы шаблонов -->
      <VCard v-if="showGroupSelector" class="mb-4">
        <template #content>
          <div class="p-4">
            <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
              Выбрать группу шаблонов
            </label>
            <VAutoComplete
              v-model="selectedTemplateGroup"
              :suggestions="groupSuggestions"
              @complete="filterGroups"
              option-label="name"
              option-value="id"
              placeholder="Поиск группы..."
              class="w-full"
              dropdown
            />
            <VButton
              @click="loadSelectedGroupTemplates"
              class="mt-3"
              size="small"
              outlined
              :disabled="!selectedTemplateGroup || loadingTemplates"
              :loading="loadingTemplates"
              label="Загрузить шаблоны группы"
            />
          </div>
        </template>
      </VCard>
    </div>

    <!-- Список атрибутов -->
    <div v-if="attributes.length > 0" class="space-y-4">
      <!-- Группировка по группам шаблонов -->
      <div v-if="groupByTemplate" v-for="(group, groupName) in groupedAttributes" :key="groupName" class="space-y-3">
        <div class="flex items-center gap-2 mb-3">
          <i class="pi pi-folder text-blue-600"></i>
          <h4 class="font-medium text-surface-900 dark:text-surface-0">
            {{ groupName === 'undefined' ? 'Без группы' : groupName }}
          </h4>
          <VTag :value="`${group.length} атр.`" severity="secondary" size="small" />
        </div>
        
        <div class="grid gap-3 ml-6">
          <AttributeCard
            v-for="(attribute, index) in group"
            :key="attribute.id || `new-${index}`"
            :attribute="attribute"
            :mode="cardMode"
            @edit="editAttribute"
            @remove="removeAttribute"
          />
        </div>
      </div>

      <!-- Обычный список без группировки -->
      <div v-else class="grid gap-4">
        <AttributeCard
          v-for="(attribute, index) in attributes"
          :key="attribute.id || `new-${index}`"
          :attribute="attribute"
          :mode="cardMode"
          @edit="editAttribute"
          @remove="removeAttribute"
        />
      </div>
    </div>

    <!-- Сообщение, если нет атрибутов -->
    <div v-else class="text-center py-8">
      <i class="pi pi-info-circle text-4xl text-surface-400 dark:text-surface-600 mb-4"></i>
      <p class="text-surface-600 dark:text-surface-400">
        Атрибуты не добавлены. Нажмите "Добавить атрибут" для начала работы.
      </p>
    </div>

    <!-- Диалог добавления/редактирования атрибута -->
    <VDialog
      v-model:visible="showAddDialog"
      modal
      :header="editingAttribute ? 'Редактировать атрибут' : 'Добавить атрибут'"
      :style="{ width: '50rem' }"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
    >
      <div class="space-y-4">
        <!-- Поиск шаблона (только при добавлении) -->
        <div v-if="!editingAttribute">
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Шаблон атрибута *
          </label>
          <VAutoComplete
            v-model="selectedTemplate"
            :suggestions="templateSuggestions"
            @complete="filterTemplates"
            option-label="title"
            placeholder="Поиск шаблона..."
            class="w-full"
            dropdown
          >
            <template #option="slotProps">
              <div class="flex items-center gap-3">
                <i class="pi pi-tag text-blue-600"></i>
                <div class="flex-1">
                  <div class="font-medium">{{ slotProps.option.title }}</div>
                  <div class="text-sm text-surface-600 dark:text-surface-400">
                    {{ slotProps.option.name }}
                    <span v-if="slotProps.option.group?.name" class="ml-2 px-2 py-1 bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 rounded text-xs">
                      {{ slotProps.option.group.name }}
                    </span>
                  </div>
                </div>
                <VTag :value="getDataTypeLabel(slotProps.option.dataType)" severity="info" size="small" />
              </div>
            </template>
          </VAutoComplete>
        </div>

        <!-- Информация о шаблоне при редактировании -->
        <div v-else class="p-4 bg-surface-50 dark:bg-surface-900 rounded-lg">
          <div class="flex items-center gap-2 mb-2">
            <i class="pi pi-tag text-blue-600"></i>
            <span class="font-medium">{{ editingAttribute.template?.title }}</span>
            <VTag :value="getDataTypeLabel(editingAttribute.template?.dataType)" severity="info" size="small" />
          </div>
          <div class="text-sm text-surface-600 dark:text-surface-400">
            {{ editingAttribute.template?.description }}
          </div>
        </div>

        <!-- Ввод значения -->
        <div v-if="selectedTemplate || editingAttribute">
          <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
            Значение *
          </label>
          <AttributeValueInput
            v-model="newAttributeValue"
            :template="selectedTemplate || editingAttribute?.template"
            ref="valueInputRef"
            @change="handleValueChange"
          />
        </div>
      </div>

      <template #footer>
        <VButton
          label="Отмена"
          severity="secondary"
          @click="closeAddDialog"
        />
        <VButton
          :label="editingAttribute ? 'Сохранить' : 'Добавить'"
          @click="saveAttribute"
          :disabled="!canSave"
        />
      </template>
    </VDialog>

    <!-- Диалог выбора группы шаблонов -->
    <VDialog
      v-model:visible="showGroupDialog"
      modal
      header="Выбор группы шаблонов"
      :style="{ width: '40rem' }"
    >
      <div class="space-y-4">
        <div v-for="group in templateGroups" :key="group.id" class="border border-surface-200 dark:border-surface-700 rounded-lg p-4">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="font-medium text-surface-900 dark:text-surface-0">{{ group.name }}</h4>
              <p class="text-sm text-surface-600 dark:text-surface-400">{{ group.description }}</p>
              <small class="text-surface-500">{{ group._count?.templates || 0 }} шаблонов</small>
            </div>
            <VButton
              @click="loadTemplatesByGroupId(group.id)"
              size="small"
              :loading="loadingTemplates"
            >
              Добавить все
            </VButton>
          </div>
        </div>
      </div>
    </VDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import VCard from '@/volt/Card.vue';
import VButton from '@/volt/Button.vue';
import VDialog from '@/volt/Dialog.vue';
import VTag from '@/volt/Tag.vue';
import VAutoComplete from '@/volt/AutoComplete.vue';
import AttributeValueInput from './AttributeValueInput.vue';
import AttributeCard from './AttributeCard.vue';

// Интерфейс атрибута
interface AttributeForm {
  id?: number;
  templateId: number;
  value: string;
  template?: {
    id: number;
    name: string;
    title: string;
    description?: string;
    dataType: string;
    unit?: string;
    isRequired: boolean;
    minValue?: number;
    maxValue?: number;
    allowedValues?: string[];
    group?: {
      id: number;
      name: string;
    };
  };
  // Альтернативные поля для совместимости с AttributeData
  templateTitle?: string;
  templateDataType?: string;
  templateUnit?: string;
  templateGroup?: string;
  templateDescription?: string;
}

// Props
interface Props {
  modelValue: AttributeForm[];
  title?: string;
  description?: string;
  showGroupSelector?: boolean;
  groupByTemplate?: boolean;
  cardMode?: 'compact' | 'detailed';
  entityId?: number; // ID сущности для загрузки существующих атрибутов
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  description: '',
  showGroupSelector: true,
  groupByTemplate: true,
  cardMode: 'detailed'
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: AttributeForm[]];
}>();

// Локальное состояние
const attributes = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// tRPC клиент
const { attributeTemplates, partAttributes, loading } = useTrpc();

// Состояние компонента
const showAddDialog = ref(false);
const showGroupDialog = ref(false);
const selectedTemplate = ref<any>(null);
const newAttributeValue = ref('');
const selectedTemplateGroup = ref<number | null>(null);
const loadingTemplates = ref(false);
const editingAttribute = ref<AttributeForm | null>(null);
const valueInputRef = ref<any>(null);

// Данные для автокомплита
const templateSuggestions = ref<any[]>([]);
const groupSuggestions = ref<any[]>([]);
const templateGroups = ref<any[]>([]);

// Вычисляемые свойства
const groupedAttributes = computed(() => {
  if (!props.groupByTemplate) return {};
  
  return attributes.value.reduce((groups: Record<string, AttributeForm[]>, attribute) => {
    const groupName = attribute.template?.group?.name || attribute.templateGroup || 'undefined';
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    groups[groupName].push(attribute);
    return groups;
  }, {});
});

const canSave = computed(() => {
  if (!selectedTemplate.value && !editingAttribute.value) return false;
  
  // Проверяем значение в зависимости от типа данных
  const template = selectedTemplate.value || editingAttribute.value?.template;
  if (!template) return false;
  
  const value = newAttributeValue.value;
  
  // Для булевых значений разрешаем false
  if (template.dataType === 'BOOLEAN') {
    return value !== null && value !== undefined;
  }
  
  // Для остальных типов проверяем, что значение не пустое
  if (value === null || value === undefined || value === '') return false;
  
  // Для строк проверяем, что не только пробелы
  if (template.dataType === 'STRING' && value.toString().trim() === '') return false;
  
  return true;
});

// Вспомогательные функции
const getDataTypeLabel = (dataType: string) => {
  const labels: Record<string, string> = {
    'STRING': 'Строка',
    'NUMBER': 'Число',
    'BOOLEAN': 'Логическое',
    'DATE': 'Дата',
    'JSON': 'JSON'
  };
  return labels[dataType] || dataType;
};

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    'MM': 'мм', 'INCH': 'дюймы', 'FT': 'футы',
    'G': 'г', 'KG': 'кг', 'T': 'т', 'LB': 'фунты',
    'ML': 'мл', 'L': 'л', 'GAL': 'галлоны',
    'PCS': 'шт', 'SET': 'комплект', 'PAIR': 'пара',
    'BAR': 'бар', 'PSI': 'PSI',
    'KW': 'кВт', 'HP': 'л.с.',
    'NM': 'Н⋅м', 'RPM': 'об/мин',
    'C': '°C', 'F': '°F',
    'PERCENT': '%'
  };
  return labels[unit] || unit;
};

// Основные функции
// Поиск шаблонов для автокомплита
const searchTemplates = async (event: any) => {
  try {
    const result = await attributeTemplates.findMany({
      search: event.query,
      limit: 20
    });

    if (result && typeof result === "object") {
      templateSuggestions.value = (result as any).templates || [];
    } else {
      templateSuggestions.value = [];
    }
  } catch (error) {
    console.error('Ошибка поиска шаблонов:', error);
    templateSuggestions.value = [];
  }
};

const filterTemplates = async (event: any) => {
  await searchTemplates(event);
};

const filterGroups = async (event: any) => {
  try {
    const groups = await attributeTemplates.findAllGroups();
    if (groups && Array.isArray(groups)) {
      const query = event.query.toLowerCase();
      groupSuggestions.value = groups.filter((group: any) => 
        group.name.toLowerCase().includes(query)
      );
    } else {
      groupSuggestions.value = [];
    }
  } catch (error) {
    console.error('Ошибка поиска групп:', error);
    groupSuggestions.value = [];
  }
};

const loadSelectedGroupTemplates = async () => {
  if (!selectedTemplateGroup.value) return;
  
  // Извлекаем ID из объекта, если selectedTemplateGroup содержит объект
  const groupId = typeof selectedTemplateGroup.value === 'object' 
    ? (selectedTemplateGroup.value as any).id 
    : selectedTemplateGroup.value;
    
  if (!groupId || typeof groupId !== 'number') {
    console.error('Неверный ID группы:', groupId);
    return;
  }
    
  await loadTemplatesByGroupId(groupId);
};

const loadTemplatesByGroupId = async (groupId: number) => {
  try {
    loadingTemplates.value = true;
    const result = await attributeTemplates.findMany({
      groupId: groupId,
      limit: 100
    });

    if (result && typeof result === "object") {
      // API возвращает объект { templates: [...], total: number }
      const templates = (result as any).templates || [];
      
      // Добавляем шаблоны из группы как новые атрибуты
      const newAttributes = templates
        .filter((template: any) => !attributes.value.some(attr => attr.templateId === template.id))
        .map((template: any) => ({
          templateId: template.id,
          value: '',
          template: template
        }));

      attributes.value = [...attributes.value, ...newAttributes];
      
      console.log(`Загружено ${newAttributes.length} новых шаблонов из группы`);
      
      // Закрываем диалог после успешной загрузки
      showGroupDialog.value = false;
    }
  } catch (error) {
    console.error('Ошибка загрузки шаблонов группы:', error);
  } finally {
    loadingTemplates.value = false;
  }
};

const editAttribute = (attribute: AttributeForm) => {
  editingAttribute.value = attribute;
  newAttributeValue.value = attribute.value || '';
  showAddDialog.value = true;
};

const removeAttribute = async (attribute: AttributeForm) => {
  // Если атрибут сохранен в базе данных, удаляем его оттуда
  if (attribute.id && props.entityId) {
    try {
      await partAttributes.delete({ id: attribute.id });
    } catch (error) {
      console.error('Ошибка удаления атрибута:', error);
      return;
    }
  }

  // Удаляем из локального состояния
  const newAttributes = attributes.value.filter(attr => {
    // Если у атрибута есть ID, сравниваем по ID
    if (attr.id && attribute.id) {
      return attr.id !== attribute.id;
    }
    // Иначе сравниваем по templateId (для новых атрибутов)
    return attr.templateId !== attribute.templateId;
  });

  attributes.value = newAttributes;
};

const saveAttribute = async () => {
  // Проверяем, что есть значение в зависимости от типа данных
  const template = selectedTemplate.value || editingAttribute.value?.template;
  if (!template) return;
  
  const value = newAttributeValue.value;
  
  // Для булевых значений разрешаем false
  if (template.dataType === 'BOOLEAN') {
    if (value === null || value === undefined) return;
  } else {
    // Для остальных типов проверяем, что значение не пустое
    if (value === null || value === undefined || value === '') return;
    
    // Для строк проверяем, что не только пробелы
    if (template.dataType === 'STRING' && value.toString().trim() === '') return;
  }

  // Валидация через компонент ввода
  if (valueInputRef.value && !valueInputRef.value.validate()) {
    return;
  }

  if (editingAttribute.value) {
    // Обновляем существующий атрибут
    if (editingAttribute.value.id && props.entityId) {
      // Сохраняем в базу данных
      try {
        await partAttributes.update({
          id: editingAttribute.value.id,
          value: newAttributeValue.value
        });
      } catch (error) {
        console.error('Ошибка обновления атрибута:', error);
        return;
      }
    }
    editingAttribute.value.value = newAttributeValue.value;
  } else if (selectedTemplate.value) {
    // Извлекаем ID из selectedTemplate
    const templateId = typeof selectedTemplate.value === 'object' 
      ? selectedTemplate.value.id 
      : selectedTemplate.value;
      
    // Проверяем, есть ли уже атрибут с таким шаблоном
    const existingAttribute = attributes.value.find(
      attr => attr.templateId === templateId
    );

    if (existingAttribute) {
      // Обновляем существующий атрибут
      if (existingAttribute.id && props.entityId) {
        try {
          await partAttributes.update({
            id: existingAttribute.id,
            value: newAttributeValue.value
          });
        } catch (error) {
          console.error('Ошибка обновления атрибута:', error);
          return;
        }
      }
      existingAttribute.value = newAttributeValue.value;
    } else {
      // Добавляем новый атрибут
      const newAttribute: AttributeForm = {
        templateId: templateId,
        value: newAttributeValue.value,
        template: selectedTemplate.value
      };

      // Сохраняем в базу данных если есть entityId
      if (props.entityId) {
        try {
          const savedAttribute = await partAttributes.create({
            partId: props.entityId,
            templateId: templateId,
            value: newAttributeValue.value
          });

          if (savedAttribute) {
            newAttribute.id = savedAttribute.id;
          }
        } catch (error) {
          console.error('Ошибка создания атрибута:', error);
          return;
        }
      }

      attributes.value = [...attributes.value, newAttribute];
    }
  }

  closeAddDialog();
};

const handleValueChange = (value: any) => {
  newAttributeValue.value = value;
};

const closeAddDialog = () => {
  showAddDialog.value = false;
  selectedTemplate.value = null;
  newAttributeValue.value = '';
  editingAttribute.value = null;
};

// Инициализация компонента
// Загрузка групп шаблонов
const loadTemplateGroups = async () => {
  try {
    const groups = await attributeTemplates.findAllGroups();
    if (groups && Array.isArray(groups)) {
      groupSuggestions.value = groups;
      templateGroups.value = groups;
    }
  } catch (error) {
    console.error('Ошибка загрузки групп шаблонов:', error);
  }
};

// Загрузка существующих атрибутов для сущности
const loadExistingAttributes = async () => {
  if (!props.entityId) return;

  try {
    const existingAttributes = await partAttributes.findByPartId({ partId: props.entityId });
    if (existingAttributes && Array.isArray(existingAttributes)) {
      // Преобразуем существующие атрибуты в формат компонента
      const formattedAttributes = existingAttributes.map((attr: any) => ({
        id: attr.id,
        templateId: attr.templateId,
        value: attr.value,
        template: attr.template
      }));

      // Объединяем с уже существующими атрибутами (если есть)
      const existingTemplateIds = new Set(attributes.value.map(a => a.templateId));
      const newAttributes = formattedAttributes.filter(attr => !existingTemplateIds.has(attr.templateId));

      attributes.value = [...attributes.value, ...newAttributes];
    }
  } catch (error) {
    console.error('Ошибка загрузки существующих атрибутов:', error);
  }
};

// Инициализация при монтировании
onMounted(() => {
  loadTemplateGroups();
  loadExistingAttributes();
});

// Перезагрузка атрибутов при изменении entityId
watch(() => props.entityId, () => {
  if (props.entityId) {
    loadExistingAttributes();
  }
});

// Сброс диалога при закрытии
watch(showAddDialog, (newValue) => {
  if (!newValue) {
    selectedTemplate.value = null;
    newAttributeValue.value = '';
    editingAttribute.value = null;
  }
});

// Отслеживаем изменения значения для обновления состояния кнопки
watch(newAttributeValue, (newValue) => {
  // Принудительно обновляем реактивность
  if (newValue !== undefined) {
    // Это заставит Vue пересчитать computed свойство canSave
  }
});
</script>
