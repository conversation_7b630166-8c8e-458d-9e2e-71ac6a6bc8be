<template>
  <div class="attribute-template-manager">
    <!-- Индикатор загрузки -->
    <div v-if="loading" class="text-center py-12">
      <i class="pi pi-spin pi-spinner text-4xl text-primary mb-4"></i>
      <p class="text-surface-600 dark:text-surface-400">Загрузка шаблонов атрибутов...</p>
    </div>

    <!-- Основной контент -->
    <div v-else>
      <!-- Заголовок и действия -->
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
            Шаблоны атрибутов
          </h2>
          <p class="text-surface-600 dark:text-surface-400 text-sm mt-1">
            Управление шаблонами атрибутов для запчастей, каталожных позиций и техники
          </p>
        </div>

        <div class="flex gap-3">
          <VButton @click="refreshData" :disabled="loading" severity="secondary" outlined label="Обновить"
            icon="pi pi-refresh" />

          <VButton @click="createNewTemplate" label="Создать шаблон" icon="pi pi-plus" />
        </div>
      </div>

      <!-- Фильтры и поиск -->
      <VCard class="mb-6">
        <template #content>
          <div class="p-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <!-- Поиск -->
              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Поиск
                </label>
                <VInputText v-model="searchQuery" placeholder="Поиск по названию, имени или описанию..." class="w-full"
                  @input="debouncedSearch" />
              </div>

              <!-- Группа -->
              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Группа
                </label>
                <VAutoComplete v-model="selectedGroup" :suggestions="groupSuggestions" @complete="filterGroups"
                  option-label="name" option-value="id" placeholder="Все группы" class="w-full" 
                  dropdown show-clear @change="loadTemplates" />
              </div>

              <!-- Тип данных -->
              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Тип данных
                </label>
                <VAutoComplete v-model="selectedDataType" :suggestions="dataTypeSuggestions" @complete="filterDataTypes"
                  option-label="label" option-value="value" placeholder="Все типы" class="w-full" 
                  dropdown show-clear @change="loadTemplates" />
              </div>

              <!-- Режим отображения -->
            </div>
          </div>
        </template>
      </VCard>

      <!-- Статистика -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="text-2xl font-bold text-primary mb-2">{{ totalCount }}</div>
              <div class="text-sm text-surface-600 dark:text-surface-400">Всего шаблонов</div>
            </div>
          </template>
        </VCard>

        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="text-2xl font-bold text-green-600 mb-2">{{ groups.length }}</div>
              <div class="text-sm text-surface-600 dark:text-surface-400">Групп</div>
            </div>
          </template>
        </VCard>

        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="text-2xl font-bold text-blue-600 mb-2">{{ usedTemplatesCount }}</div>
              <div class="text-sm text-surface-600 dark:text-surface-400">Используется</div>
            </div>
          </template>
        </VCard>

        <VCard>
          <template #content>
            <div class="p-4 text-center">
              <div class="text-2xl font-bold text-orange-600 mb-2">{{ unusedTemplatesCount }}</div>
              <div class="text-sm text-surface-600 dark:text-surface-400">Не используется</div>
            </div>
          </template>
        </VCard>
      </div>

      <!-- Табличный режим -->
      <VCard v-if="viewMode === 'table'">
        <template #content>
          <VDataTable :value="templates" :loading="tableLoading && templates.length === 0" paginator :rows="pageSize" :total-records="totalCount"
            :rows-per-page-options="[10, 25, 50]" lazy @page="onPageChange" table-style="min-width: 50rem"
            class="p-datatable-sm" striped-rows>
            <!-- ID -->
            <Column field="id" header="ID" sortable style="width: 80px">
              <template #body="{ data }">
                <span class="font-mono text-sm text-surface-700 dark:text-surface-300">#{{ data.id }}</span>
              </template>
            </Column>

            <!-- Название -->
            <Column field="title" header="Название" sortable>
              <template #body="{ data }">
                <div>
                  <div class="font-medium text-surface-900 dark:text-surface-0">
                    {{ data.title }}
                  </div>
                  <div class="text-sm text-surface-600 dark:text-surface-400 font-mono">
                    {{ data.name }}
                  </div>
                </div>
              </template>
            </Column>

            <!-- Группа -->
            <Column field="group.name" header="Группа" sortable>
              <template #body="{ data }">
                <VTag v-if="data.group" :value="data.group.name" severity="secondary" />
                <span v-else class="text-surface-400 dark:text-surface-600">—</span>
              </template>
            </Column>

            <!-- Тип данных -->
            <Column field="dataType" header="Тип" sortable>
              <template #body="{ data }">
                <VTag :value="getDataTypeLabel(data.dataType)" severity="info" />
              </template>
            </Column>

            <!-- Единица измерения -->
            <Column field="unit" header="Единица">
              <template #body="{ data }">
                <VTag v-if="data.unit" :value="getUnitLabel(data.unit)" severity="success" />
                <span v-else class="text-surface-400 dark:text-surface-600">—</span>
              </template>
            </Column>

            <!-- Использование -->
            <Column header="Использование" style="width: 120px">
              <template #body="{ data }">
                <div class="text-sm">
                  <div v-if="data._count">
                    <div class="text-surface-700 dark:text-surface-300">
                      {{ getTotalUsage(data._count) }} исп.
                    </div>
                    <div class="text-xs text-surface-500 dark:text-surface-400">
                      {{ getUsageDetails(data._count) }}
                    </div>
                  </div>
                  <span v-else class="text-surface-400 dark:text-surface-600">—</span>
                </div>
              </template>
            </Column>

            <!-- Действия -->
            <Column header="Действия" style="width: 120px">
              <template #body="{ data }">
                <div class="flex gap-2">
                  <VButton @click="editTemplate(data)" severity="secondary" outlined size="small" icon="pi pi-pencil"
                    label="Редактировать" />
                  <VButton @click="deleteTemplate(data)" severity="danger" outlined size="small" icon="pi pi-trash"
                    label="Удалить" :disabled="getTotalUsage(data._count) > 0" />
                </div>
              </template>
            </Column>
          </VDataTable>
        </template>
      </VCard>

      <!-- Карточный режим -->
      <div v-else-if="viewMode === 'cards'" class="grid gap-4">
        <VCard v-for="template in templates" :key="template.id"
          class="border border-surface-200 dark:border-surface-700 hover:border-primary transition-colors">
          <template #content>
            <div class="p-6">
              <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                  <div class="flex items-center gap-3 mb-2">
                    <i class="pi pi-tag text-blue-600 text-xl"></i>
                    <h3 class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                      {{ template.title }}
                    </h3>
                    <VTag v-if="template.isRequired" value="Обязательный" severity="danger" size="small" />
                  </div>
                  <div class="text-sm text-surface-600 dark:text-surface-400 font-mono mb-2">
                    {{ template.name }}
                  </div>
                  <p v-if="template.description" class="text-surface-600 dark:text-surface-400 mb-3">
                    {{ template.description }}
                  </p>
                </div>

                <div class="flex gap-2 ml-4">
                  <VButton @click="editTemplate(template)" severity="secondary" outlined size="small" icon="pi pi-pencil"
                    label="Редактировать" />
                  <VButton @click="deleteTemplate(template)" severity="danger" outlined size="small" icon="pi pi-trash"
                    label="Удалить" :disabled="getTotalUsage(template._count) > 0" />
                </div>
              </div>

              <div class="flex items-center gap-4 mb-4">
                <VTag :value="getDataTypeLabel(template.dataType)" severity="info" />
                <VTag v-if="template.unit" :value="getUnitLabel(template.unit)" severity="success" />
                <VTag v-if="template.group" :value="template.group.name" severity="secondary" />
              </div>

              <!-- Статистика использования -->
              <div v-if="template._count" class="border-t border-surface-200 dark:border-surface-700 pt-4">
                <div class="text-sm text-surface-600 dark:text-surface-400 mb-2">
                  Использование:
                </div>
                <div class="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                      {{ template._count.partAttributes || 0 }}
                    </div>
                    <div class="text-xs text-surface-500">Запчасти</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                      {{ template._count.catalogItemAttributes || 0 }}
                    </div>
                    <div class="text-xs text-surface-500">Каталог</div>
                  </div>
                  <div>
                    <div class="text-lg font-semibold text-surface-900 dark:text-surface-0">
                      {{ template._count.equipmentAttributes || 0 }}
                    </div>
                    <div class="text-xs text-surface-500">Техника</div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </VCard>

        <!-- Пагинация для карточного режима -->
        <VCard v-if="totalCount > pageSize">
          <template #content>
            <div class="p-4">
              <Paginator :rows="pageSize" :total-records="totalCount" :rows-per-page-options="[10, 25, 50]"
                @page="onPageChange" />
            </div>
          </template>
        </VCard>
      </div>

      <!-- Диалог создания/редактирования шаблона -->
      <VDialog v-model:visible="showCreateDialog" modal
        :header="editingTemplate ? 'Редактировать шаблон' : 'Создать шаблон'" :style="{ width: '50rem' }"
        :breakpoints="{ '1199px': '75vw', '575px': '90vw' }">
        <TemplateForm v-model="templateForm" :groups="groups" :loading="saving" @save="saveTemplate"
          @cancel="showCreateDialog = false" />
      </VDialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useTrpc } from '@/composables/useTrpc';

// Импорт компонентов
import VCard from '@/volt/Card.vue';
import VButton from '@/volt/Button.vue';
import VInputText from '@/volt/InputText.vue';
// VDropdown removed - replaced with VAutoComplete
import VDataTable from '@/volt/DataTable.vue';
import VTag from '@/volt/Tag.vue';
import VDialog from '@/volt/Dialog.vue';
import Column from 'primevue/column';
import Paginator from 'primevue/paginator';
import TemplateForm from '../TemplateForm.vue';
import VAutoComplete from '@/volt/AutoComplete.vue';

// Composables
const { attributeTemplates, loading } = useTrpc();

// Состояние компонента
const templates = ref<any[]>([]);
const groups = ref<any[]>([]);
const totalCount = ref(0);
const pageSize = ref(25);
const currentPage = ref(0);

// Локальный индикатор загрузки только для таблицы,
// чтобы не задействовать глобальный `loading` tRPC и избежать "моргания".
const tableLoading = ref(false);

// Счётчик для защиты от гонки запросов
let lastRequestId = 0;

// Фильтры
const searchQuery = ref('');
const selectedGroup = ref<number | null>(null);
const selectedDataType = ref<string | null>(null);
const viewMode = ref('table');

// Диалоги
const showCreateDialog = ref(false);
const editingTemplate = ref<any>(null);
const templateForm = ref<any>({});
const saving = ref(false);

// Опции для фильтров
const dataTypeOptions = [
  { label: 'Строка', value: 'STRING' },
  { label: 'Число', value: 'NUMBER' },
  { label: 'Логическое', value: 'BOOLEAN' },
  { label: 'Дата', value: 'DATE' },
  { label: 'JSON', value: 'JSON' }
];

const viewModeOptions = [
  { label: 'Таблица', value: 'table' },
  { label: 'Карточки', value: 'cards' }
];

// Данные для автокомплита
const groupSuggestions = ref<any[]>([]);
const dataTypeSuggestions = ref<any[]>([]);
const viewModeSuggestions = ref<any[]>([]);

// Фильтрация для автокомплита
const filterGroups = (event: any) => {
  const query = event.query.toLowerCase();
  if (!query) {
    groupSuggestions.value = groups.value;
  } else {
    groupSuggestions.value = groups.value.filter(group =>
      group.name.toLowerCase().includes(query)
    );
  }
};

const filterDataTypes = (event: any) => {
  const query = event.query.toLowerCase();
  if (!query) {
    dataTypeSuggestions.value = dataTypeOptions;
  } else {
    dataTypeSuggestions.value = dataTypeOptions.filter(option =>
      option.label.toLowerCase().includes(query)
    );
  }
};

const filterViewModes = (event: any) => {
  const query = event.query.toLowerCase();
  if (!query) {
    viewModeSuggestions.value = viewModeOptions;
  } else {
    viewModeSuggestions.value = viewModeOptions.filter(option =>
      option.label.toLowerCase().includes(query)
    );
  }
};

// Инициализация предзагруженных данных
const initializeAutocompleteData = () => {
  groupSuggestions.value = groups.value;
  dataTypeSuggestions.value = dataTypeOptions;
  viewModeSuggestions.value = viewModeOptions;
};

// Вычисляемые свойства
const usedTemplatesCount = computed(() => {
  return templates.value.filter(template => getTotalUsage(template._count) > 0).length;
});

const unusedTemplatesCount = computed(() => {
  return templates.value.filter(template => getTotalUsage(template._count) === 0).length;
});

// Вспомогательные функции
const getDataTypeLabel = (dataType: string) => {
  const option = dataTypeOptions.find(opt => opt.value === dataType);
  return option?.label || dataType;
};

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    'MM': 'мм', 'INCH': 'дюймы', 'FT': 'футы',
    'G': 'г', 'KG': 'кг', 'T': 'т', 'LB': 'фунты',
    'ML': 'мл', 'L': 'л', 'GAL': 'галлоны',
    'PCS': 'шт', 'SET': 'комплект', 'PAIR': 'пара',
    'BAR': 'бар', 'PSI': 'PSI',
    'KW': 'кВт', 'HP': 'л.с.',
    'NM': 'Н⋅м', 'RPM': 'об/мин',
    'C': '°C', 'F': '°F',
    'PERCENT': '%'
  };
  return labels[unit] || unit;
};

const getTotalUsage = (count: any) => {
  if (!count) return 0;
  return (count.partAttributes || 0) + (count.catalogItemAttributes || 0) + (count.equipmentAttributes || 0);
};

const getUsageDetails = (count: any) => {
  if (!count) return '';
  const parts = count.partAttributes || 0;
  const items = count.catalogItemAttributes || 0;
  const equipment = count.equipmentAttributes || 0;

  const details = [];
  if (parts > 0) details.push(`${parts} зап.`);
  if (items > 0) details.push(`${items} кат.`);
  if (equipment > 0) details.push(`${equipment} тех.`);

  return details.join(', ');
};

// Основные функции
const loadTemplates = async () => {
  const current = ++lastRequestId;
  tableLoading.value = true;
  try {
    // Извлекаем ID из выбранной группы
    const groupId = selectedGroup.value 
      ? (typeof selectedGroup.value === 'object' ? (selectedGroup.value as any).id : selectedGroup.value)
      : undefined;
      
    // Извлекаем значение из выбранного типа данных
    const dataType = selectedDataType.value 
      ? (typeof selectedDataType.value === 'object' ? (selectedDataType.value as any).value : selectedDataType.value)
      : undefined;
      
    const result = await attributeTemplates.findMany({
      groupId: groupId,
      search: searchQuery.value || undefined,
      dataType: dataType,
      limit: pageSize.value,
      offset: currentPage.value * pageSize.value
    });

    if (current === lastRequestId && result && typeof result === 'object') {
      templates.value = (result as any).templates || [];
      totalCount.value = (result as any).total || 0;
    }
  } catch (error) {
    console.error('Ошибка загрузки шаблонов:', error);
    console.error('Не удалось загрузить шаблоны');
  } finally {
    if (current === lastRequestId) {
      tableLoading.value = false;
    }
  }
};

const loadGroups = async () => {
  try {
    const result = await attributeTemplates.findAllGroups();
    if (result && Array.isArray(result)) {
      groups.value = result;
    }
  } catch (error) {
    console.error('Ошибка загрузки групп:', error);
  }
};

// Debounced search
let searchTimeout: NodeJS.Timeout;
const debouncedSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    currentPage.value = 0;
    loadTemplates();
  }, 500);
};

const refreshData = () => {
  loadTemplates();
  loadGroups();
};

// Обработчики событий
const onPageChange = (event: any) => {
  currentPage.value = event.page;
  pageSize.value = event.rows;
  loadTemplates();
};

const editTemplate = (template: any) => {
  editingTemplate.value = template;
  templateForm.value = { ...template };
  showCreateDialog.value = true;
};

const createNewTemplate = () => {
  editingTemplate.value = null;
  templateForm.value = {
    dataType: 'STRING',
    isRequired: false,
    allowedValues: []
  };
  showCreateDialog.value = true;
};

const deleteTemplate = async (template: any) => {
  const confirmed = window.confirm(`Вы уверены, что хотите удалить шаблон "${template.title}"?`);

  if (confirmed) {
    try {
      await attributeTemplates.delete({ id: template.id });
      console.log('Шаблон успешно удален');
      loadTemplates();
    } catch (error: any) {
      console.error('Ошибка удаления шаблона:', error);
      alert(error.message || 'Не удалось удалить шаблон');
    }
  }
};

const saveTemplate = async (formData: any) => {
  try {
    saving.value = true;

    if (editingTemplate.value) {
      await attributeTemplates.update({ id: editingTemplate.value.id, ...formData });
      console.log('Шаблон успешно обновлен');
    } else {
      await attributeTemplates.create(formData);
      console.log('Шаблон успешно создан');
    }

    showCreateDialog.value = false;
    editingTemplate.value = null;
    templateForm.value = {};
    loadTemplates();
    loadGroups(); // Обновляем группы на случай создания новой
  } catch (error: any) {
    console.error('Ошибка сохранения шаблона:', error);
    alert(error.message || 'Не удалось сохранить шаблон');
  } finally {
    saving.value = false;
  }
};

// Инициализация
onMounted(() => {
  loadTemplates();
  loadGroups();
  initializeAutocompleteData(); // Инициализируем автокомплит
});
</script>
