<template>
  <div class="attribute-display" :class="displayClass">
    <!-- Компактный режим (одна строка) -->
    <div v-if="mode === 'compact'" class="flex items-center gap-2">
      <span class="font-medium text-surface-900 dark:text-surface-0">
        {{ attribute.template?.title || attribute.templateTitle }}:
      </span>
      <span class="text-surface-700 dark:text-surface-300">
        {{ formattedValue }}
      </span>
      <VTag
        v-if="showGroup && (attribute.template?.group?.name || attribute.templateGroup)"
        :value="attribute.template?.group?.name || attribute.templateGroup"
        severity="secondary"
        size="small"
      />
    </div>

    <!-- Карточный режим -->
    <VCard v-else-if="mode === 'card'" class="border border-surface-200 dark:border-surface-700">
      <template #content>
        <div class="p-4">
          <div class="flex items-start justify-between mb-3">
            <div class="flex items-center gap-2">
              <i class="pi pi-tag text-blue-600"></i>
              <span class="font-medium text-surface-900 dark:text-surface-0">
                {{ attribute.template?.title || attribute.templateTitle }}
              </span>
              <VTag
                v-if="attribute.template?.isRequired"
                value="Обязательный"
                severity="danger"
                size="small"
              />
            </div>
            <VTag
              v-if="showGroup && (attribute.template?.group?.name || attribute.templateGroup)"
              :value="attribute.template?.group?.name || attribute.templateGroup"
              severity="secondary"
              size="small"
            />
          </div>

          <div class="mb-3">
            <div class="text-lg font-semibold text-surface-900 dark:text-surface-0">
              {{ formattedValue }}
            </div>
            <div class="text-sm text-surface-600 dark:text-surface-400">
              {{ getDataTypeLabel(attribute.template?.dataType || attribute.templateDataType) }}
              <span v-if="attribute.template?.unit || attribute.templateUnit" class="ml-2">
                • {{ getUnitLabel(attribute.template?.unit || attribute.templateUnit) }}
              </span>
            </div>
          </div>

          <div v-if="attribute.template?.description || attribute.templateDescription" class="text-sm text-surface-500 dark:text-surface-400">
            {{ attribute.template?.description || attribute.templateDescription }}
          </div>
        </div>
      </template>
    </VCard>

    <!-- Табличный режим -->
    <div v-else-if="mode === 'table'" class="grid grid-cols-3 gap-4 py-2">
      <div class="font-medium text-surface-900 dark:text-surface-0">
        {{ attribute.template?.title || attribute.templateTitle }}
      </div>
      <div class="text-surface-700 dark:text-surface-300">
        {{ formattedValue }}
      </div>
      <div class="flex items-center gap-2">
        <VTag
          :value="getDataTypeLabel(attribute.template?.dataType || attribute.templateDataType)"
          severity="info"
          size="small"
        />
        <VTag
          v-if="attribute.template?.unit || attribute.templateUnit"
          :value="getUnitLabel(attribute.template?.unit || attribute.templateUnit)"
          severity="success"
          size="small"
        />
      </div>
    </div>

    <!-- Детальный режим -->
    <div v-else class="space-y-3">
      <div class="flex items-center gap-2">
        <i class="pi pi-tag text-blue-600"></i>
        <span class="text-lg font-medium text-surface-900 dark:text-surface-0">
          {{ attribute.template?.title || attribute.templateTitle }}
        </span>
        <VTag
          v-if="attribute.template?.isRequired"
          value="Обязательный"
          severity="danger"
          size="small"
        />
        <VTag
          v-if="showGroup && (attribute.template?.group?.name || attribute.templateGroup)"
          :value="attribute.template?.group?.name || attribute.templateGroup"
          severity="secondary"
          size="small"
        />
      </div>

      <div class="pl-6">
        <div class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">
          {{ formattedValue }}
        </div>

        <div class="flex items-center gap-4 text-sm text-surface-600 dark:text-surface-400 mb-2">
          <div class="flex items-center gap-1">
            <i class="pi pi-info-circle"></i>
            <span>{{ getDataTypeLabel(attribute.template?.dataType || attribute.templateDataType) }}</span>
          </div>
          <div v-if="attribute.template?.unit || attribute.templateUnit" class="flex items-center gap-1">
            <i class="pi pi-calculator"></i>
            <span>{{ getUnitLabel(attribute.template?.unit || attribute.templateUnit) }}</span>
          </div>
          <div class="flex items-center gap-1">
            <i class="pi pi-code"></i>
            <span class="font-mono">{{ attribute.template?.name || attribute.templateName }}</span>
          </div>
        </div>

        <div v-if="attribute.template?.description || attribute.templateDescription" class="text-sm text-surface-500 dark:text-surface-400">
          {{ attribute.template?.description || attribute.templateDescription }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import VCard from '@/volt/Card.vue';
import VTag from '@/volt/Tag.vue';

// Интерфейс атрибута
interface AttributeData {
  id?: number;
  value: string;
  template?: {
    id: number;
    name: string;
    title: string;
    description?: string;
    dataType: string;
    unit?: string;
    isRequired: boolean;
    group?: {
      id: number;
      name: string;
    };
  };
  // Альтернативные поля для совместимости
  templateTitle?: string;
  templateName?: string;
  templateDataType?: string;
  templateUnit?: string;
  templateGroup?: string;
  templateDescription?: string;
}

// Props
interface Props {
  attribute: AttributeData;
  mode?: 'compact' | 'card' | 'table' | 'detailed';
  showGroup?: boolean;
  size?: 'small' | 'normal' | 'large';
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'compact',
  showGroup: true,
  size: 'normal'
});

// Вычисляемые свойства
const displayClass = computed(() => {
  const classes = ['attribute-display'];
  
  if (props.size === 'small') classes.push('text-sm');
  if (props.size === 'large') classes.push('text-lg');
  
  return classes.join(' ');
});

const formattedValue = computed(() => {
  const value = props.attribute.value;
  const dataType = props.attribute.template?.dataType || props.attribute.templateDataType;
  const unit = props.attribute.template?.unit || props.attribute.templateUnit;

  if (!value) return '—';

  switch (dataType) {
    case 'STRING':
      return value;

    case 'NUMBER':
      const numValue = Number(value);
      if (isNaN(numValue)) return value;
      
      // Форматируем число с учетом единицы измерения
      let formatted = numValue.toLocaleString('ru-RU', {
        minimumFractionDigits: 0,
        maximumFractionDigits: getFractionDigits(unit)
      });
      
      if (unit) {
        formatted += ` ${getUnitLabel(unit)}`;
      }
      
      return formatted;

    case 'BOOLEAN':
      return value === 'true' || value === true ? 'Да' : 'Нет';

    case 'DATE':
      try {
        const date = new Date(value);
        return date.toLocaleDateString('ru-RU');
      } catch {
        return value;
      }

    case 'JSON':
      try {
        const parsed = JSON.parse(value);
        return JSON.stringify(parsed, null, 2);
      } catch {
        return value;
      }

    default:
      return value;
  }
});

// Вспомогательные функции
const getDataTypeLabel = (dataType: string) => {
  const labels: Record<string, string> = {
    'STRING': 'Строка',
    'NUMBER': 'Число',
    'BOOLEAN': 'Логическое',
    'DATE': 'Дата',
    'JSON': 'JSON'
  };
  return labels[dataType] || dataType;
};

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    'MM': 'мм', 'INCH': 'дюймы', 'FT': 'футы',
    'G': 'г', 'KG': 'кг', 'T': 'т', 'LB': 'фунты',
    'ML': 'мл', 'L': 'л', 'GAL': 'галлоны',
    'PCS': 'шт', 'SET': 'комплект', 'PAIR': 'пара',
    'BAR': 'бар', 'PSI': 'PSI',
    'KW': 'кВт', 'HP': 'л.с.',
    'NM': 'Н⋅м', 'RPM': 'об/мин',
    'C': '°C', 'F': '°F',
    'PERCENT': '%'
  };
  return labels[unit] || unit;
};

const getFractionDigits = (unit?: string) => {
  if (!unit) return 2;
  
  // Определяем количество знаков после запятой на основе единицы измерения
  if (unit === 'MM') return 2;
  if (unit === 'INCH') return 3;
  if (unit === 'PERCENT') return 1;
  if (unit === 'KG' || unit === 'G') return 3;
  
  return 2; // По умолчанию
};
</script>

<style scoped>
.attribute-display {
  @apply transition-all duration-200;
}

.attribute-display:hover {
  @apply bg-surface-50 dark:bg-surface-900/20 rounded-lg p-2 -m-2;
}
</style>
