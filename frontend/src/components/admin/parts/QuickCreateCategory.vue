<template>
  <VDialog v-model:visible="visible" modal header="Создать новую категорию" class="w-96">
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Название категории *
        </label>
        <VInputText 
          v-model="formData.name" 
          placeholder="Например: Фильтры масляные"
          class="w-full"
          :class="{ 'p-invalid': errors.name }"
        />
        <small v-if="errors.name" class="text-red-500">{{ errors.name }}</small>
      </div>

      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Описание
        </label>
        <VTextarea 
          v-model="formData.description" 
          placeholder="Описание категории"
          rows="3"
          class="w-full"
        />
      </div>

      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Родительская категория
        </label>
        <VAutoComplete
          v-model="formData.parent"
          :suggestions="parentSuggestions"
          @complete="searchParents"
          option-label="name"
          placeholder="Поиск родительской категории"
          class="w-full"
        />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <VButton 
          @click="visible = false" 
          severity="secondary" 
          outlined
          :disabled="loading"
        >
          Отмена
        </VButton>
        <VButton 
          @click="createCategory" 
          :loading="loading"
          :disabled="!canCreate"
        >
          Создать
        </VButton>
      </div>
    </template>

    <VMessage v-if="error" severity="error" class="mt-4">
      {{ error }}
    </VMessage>
  </VDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import VDialog from '@/volt/Dialog.vue';
import VButton from '@/volt/Button.vue';
import VInputText from '@/volt/InputText.vue';
import VTextarea from '@/volt/Textarea.vue';
import VAutoComplete from '@/volt/AutoComplete.vue';
import VMessage from '@/volt/Message.vue';

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'created', category: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { loading, error, clearError, partCategories } = useTrpc();

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const formData = ref({
  name: '',
  description: '',
  parent: null as any
});

const errors = ref({
  name: ''
});

const parentSuggestions = ref<any[]>([]);

// Валидация
const canCreate = computed(() => {
  return formData.value.name.trim().length > 0 && !loading.value;
});

// Поиск родительских категорий
const searchParents = async (event: any) => {
  const query = event.query.toLowerCase();
  const categories = await partCategories.findMany({
    where: {
      name: {
        contains: query,
        mode: 'insensitive'
      }
    },
    take: 10
  });
  
  if (categories) {
    parentSuggestions.value = categories;
  }
};

// Создание категории
const createCategory = async () => {
  clearError();
  errors.value = { name: '' };

  // Валидация
  if (!formData.value.name.trim()) {
    errors.value.name = 'Название обязательно';
    return;
  }

  try {
    // Генерируем slug из названия
    const slug = formData.value.name
      .toLowerCase()
      .replace(/[^a-zа-я0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    const categoryData = {
      name: formData.value.name.trim(),
      slug: slug,
      description: formData.value.description?.trim() || undefined,
      level: formData.value.parent ? (formData.value.parent.level + 1) : 0,
      path: formData.value.parent 
        ? `${formData.value.parent.path}/${slug}`
        : `/${slug}`,
      parentId: formData.value.parent?.id || undefined
    };

    const result = await partCategories.create({
      data: categoryData
    });

    if (result) {
      emit('created', result);
      resetForm();
      visible.value = false;
    }
  } catch (err: any) {
    console.error('Ошибка создания категории:', err);
  }
};

// Сброс формы
const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    parent: null
  };
  errors.value = { name: '' };
  clearError();
};

// Сброс формы при закрытии
watch(visible, (newValue) => {
  if (!newValue) {
    resetForm();
  }
});
</script>
