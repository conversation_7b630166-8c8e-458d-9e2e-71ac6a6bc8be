<template>
  <div class="space-y-6">
    <!-- Заголовок и действия -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-xl font-semibold text-surface-900 dark:text-surface-0">
          Запчасти
        </h2>
        <p class="text-surface-600 dark:text-surface-400 text-sm mt-1">
          Управление группами взаимозаменяемости
        </p>
      </div>
      
      <div class="flex gap-3">
        <VButton
          @click="refreshData"
          :disabled="loading"
          severity="secondary"
          outlined
          icon="pi pi-refresh"
          label="Обновить"
        />
        
        <a href="/admin/parts/create">
          <VButton label="Создать запчасть" icon="pi pi-plus" />
        </a>
      </div>
    </div>

    <!-- Поиск и фильтры -->
    <VCard>
      <template #content>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Поиск по названию -->
            <div>
              <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                Поиск по названию
              </label>
              <VInputText
                v-model="searchQuery"
                placeholder="Введите название запчасти..."
                class="w-full"
                @input="debouncedSearch"
              />
            </div>

            <!-- Фильтр по категории -->
            <div class="flex-1">
              <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                Фильтр по категории
              </label>
              <VAutoComplete
                v-model="selectedCategory"
                :suggestions="categorySuggestions"
                @complete="filterCategories"
                option-label="name"
                option-value="id"
                placeholder="Поиск категории..."
                class="w-full"
                dropdown
                show-clear
                @change="applyFilters"
              />
            </div>

            <!-- Статистика -->
            <div class="flex items-end">
              <div class="text-sm text-surface-600 dark:text-surface-400">
                <div>Всего запчастей: <span class="font-medium text-surface-900 dark:text-surface-100">{{ totalCount }}</span></div>
                <div>Показано: <span class="font-medium text-surface-900 dark:text-surface-100">{{ parts.length }}</span></div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </VCard>

    <!-- Таблица запчастей -->
    <VCard>
      <template #content>
        <VDataTable
          :value="parts"
          :loading="loading"
          paginator
          :rows="pageSize"
          :total-records="totalCount"
          :rows-per-page-options="[10, 25, 50]"
          lazy
          @page="onPageChange"
          @sort="onSort"
          table-style="min-width: 50rem"
          class="p-datatable-sm"
          striped-rows
          v-model:expandedRows="expandedRows"
          @row-expand="onRowExpand"
        >
          <!-- Кнопка расширения -->
          <VColumn expander style="width: 50px" />

          <!-- ID -->
          <VColumn field="id" header="ID" sortable style="width: 80px">
            <template #body="{ data }">
              <span class="font-mono text-sm text-surface-700 dark:text-surface-300">#{{ data.id }}</span>
            </template>
          </VColumn>

          <!-- Название -->
          <VColumn field="name" header="Название" sortable style="width: 30%">
            <template #body="{ data }">
              <div>
                <div class="font-medium text-surface-900 dark:text-surface-100">
                  {{ data.name || 'Без названия' }}
                </div>
                <div class="text-sm text-surface-600 dark:text-surface-400 mt-1">
                  Уровень: {{ data.level }} | Путь: {{ data.path }}
                </div>
              </div>
            </template>
          </VColumn>

          <!-- Категория -->
          <VColumn field="partCategory.name" header="Категория" style="width: 20%">
            <template #body="{ data }">
              <VTag v-if="data.partCategory" severity="info" class="text-sm">
                {{ data.partCategory.name }}
              </VTag>
              <span v-else class="text-surface-500 dark:text-surface-400 text-sm">Не указана</span>
            </template>
          </VColumn>

          <!-- Краткая информация -->
          <VColumn header="Детали" style="width: 20%">
            <template #body="{ data }">
              <div class="flex items-center gap-2">
                <VTag severity="secondary" class="text-sm">
                  {{ partAttributes[data.id]?.length || data.attributes?.length || 0 }} атр.
                </VTag>
                <VTag severity="success" class="text-sm">
                  {{ data.applicabilities?.length || 0 }} поз.
                </VTag>
              </div>
            </template>
          </VColumn>

          <!-- Дата создания -->
          <VColumn field="createdAt" header="Создано" sortable style="width: 120px">
            <template #body="{ data }">
              <span class="text-sm text-surface-600 dark:text-surface-400">
                {{ formatDate(data.createdAt) }}
              </span>
            </template>
          </VColumn>

          <!-- Действия -->
          <VColumn header="Действия" style="width: 140px">
            <template #body="{ data }">
              <div class="flex gap-2">
                <VButton
                  icon="pi pi-pencil"
                  severity="secondary"
                  label="Ред."
                  @click="editPart(data)"
                >
                  
                </VButton>
                <VButton
                  icon="pi pi-trash"
                  size="small"
                  severity="danger"
                  outlined
                  @click="deletePart(data)"
                  label="Удалить"
                  class="!text-red-600 !border-red-600 hover:!bg-red-50 dark:!text-red-400 dark:!border-red-400 dark:hover:!bg-red-900/20"
                />
              </div>
            </template>
          </VColumn>

          <!-- Расширенное содержимое -->
          <template #expansion="{ data }">
            <div class="p-4 bg-surface-50 dark:bg-surface-800 border-t border-surface-200 dark:border-surface-700">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

                <!-- Атрибуты -->
                <div>
                  <h4 class="text-lg font-medium text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2">
                    <i class="pi pi-list text-blue-600"></i>
                    Атрибуты запчасти
                  </h4>

                  <div v-if="partAttributes[data.id]?.length > 0" class="space-y-2">
                    <div
                      v-for="attr in partAttributes[data.id]"
                      :key="attr.id"
                      class="flex justify-between items-start p-3 bg-surface-0 dark:bg-surface-900 rounded border border-surface-200 dark:border-surface-700"
                    >
                    
                      <div class="flex-1">
                        <div class="font-medium text-sm text-surface-900 dark:text-surface-100">
                          {{ attr.template?.title || 'Без названия' }}
                        </div>
                        <div v-if="attr.description" class="text-sm text-surface-500 dark:text-surface-400 mt-1">
                          {{ attr.description }}
                        </div>
                      </div>
                      <div class="text-right ml-3">
                        <div class="font-medium text-surface-700 dark:text-surface-300">
                          {{ attr.value || 'Не указано' }}
                        </div>
                        <div v-if="attr.unit" class="text-sm text-surface-500 dark:text-surface-400">
                          {{ getUnitLabel(attr.unit) }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div v-else class="text-center py-6 text-surface-500 dark:text-surface-400">
                    <i class="pi pi-info-circle text-2xl mb-2 block"></i>
                    Атрибуты не заданы
                  </div>
                </div>

                <!-- Каталожные позиции -->
                <div>
                  <h4 class="text-lg font-medium text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2">
                    <i class="pi pi-box text-green-600"></i>
                    Каталожные позиции
                  </h4>

                  <div v-if="data.applicabilities?.length > 0" class="space-y-2">
                    <div
                      v-for="applicability in data.applicabilities"
                      :key="applicability.id"
                      class="p-3 bg-surface-0 dark:bg-surface-900 rounded border border-surface-200 dark:border-surface-700"
                    >
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <div class="font-medium text-surface-900 dark:text-surface-100">
                            {{ applicability.catalogItem?.sku || 'N/A' }}
                          </div>
                          <div class="text-sm text-surface-600 dark:text-surface-400 mt-1">
                            {{ applicability.catalogItem?.brand?.name || 'Неизвестный бренд' }}
                          </div>
                          <div v-if="applicability.catalogItem?.description" class="text-sm text-surface-500 dark:text-surface-400 mt-1">
                            {{ applicability.catalogItem.description }}
                          </div>
                        </div>
                        <div class="ml-3">
                          <VTag
                            :severity="getAccuracySeverity(applicability.accuracy)"
                            class="text-sm"
                          >
                            {{ getAccuracyLabel(applicability.accuracy) }}
                          </VTag>
                        </div>
                      </div>
                      <div v-if="applicability.notes" class="mt-2 p-2 bg-surface-100 dark:bg-surface-800 rounded text-sm text-surface-600 dark:text-surface-400">
                        <i class="pi pi-info-circle mr-1"></i>
                        {{ applicability.notes }}
                      </div>
                    </div>
                  </div>

                  <div v-else class="text-center py-6 text-surface-500 dark:text-surface-400">
                    <i class="pi pi-info-circle text-2xl mb-2 block"></i>
                    Каталожные позиции не добавлены
                  </div>
                </div>

                <!-- Применимость к технике -->
                <div class="lg:col-span-2">
                  <h4 class="text-lg font-medium text-surface-900 dark:text-surface-100 mb-3 flex items-center gap-2">
                    Применимость к технике
                  </h4>

                  <div v-if="data.equipmentApplicabilities?.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div
                      v-for="equipmentApplicability in data.equipmentApplicabilities"
                      :key="equipmentApplicability.id"
                      class="p-3 bg-surface-0 dark:bg-surface-900 rounded border border-surface-200 dark:border-surface-700"
                    >
                      <div class="flex items-start justify-between">
                        <div class="flex-1">
                          <div class="font-medium text-surface-900 dark:text-surface-100">
                            {{ equipmentApplicability.equipmentModel?.name || 'N/A' }}
                          </div>
                          <div v-if="equipmentApplicability.equipmentModel?.brand" class="text-sm text-surface-600 dark:text-surface-400 mt-1">
                            {{ equipmentApplicability.equipmentModel.brand.name }}
                          </div>
                        </div>
                        <div class="ml-3">
                          <VTag severity="info" class="text-sm">
                            Техника
                          </VTag>
                        </div>
                      </div>
                      <div v-if="equipmentApplicability.notes" class="mt-2 p-2 bg-surface-100 dark:bg-surface-800 rounded text-sm text-surface-600 dark:text-surface-400">
                        <i class="pi pi-info-circle mr-1"></i>
                        {{ equipmentApplicability.notes }}
                      </div>
                    </div>
                  </div>

                  <div v-else class="text-center py-6 text-surface-500 dark:text-surface-400">
                    <i class="pi pi-info-circle text-2xl mb-2 block"></i>
                    Применимость к технике не указана
                  </div>
                </div>
              </div>
            </div>
          </template>
        </VDataTable>
      </template>
    </VCard>

    <!-- Сообщения об ошибках -->
    <VMessage v-if="error" severity="error" class="mt-4">
      {{ error }}
    </VMessage>



    <!-- Диалог редактирования запчасти -->
    <VDialog
      v-model:visible="editDialogVisible"
      modal
      header="Редактировать запчасть"
      class=""
    >
      <PartWizard
        v-if="selectedPartForEdit"
        :part="selectedPartForEdit"
        mode="edit"
        @updated="onPartSaved"
      />
    </VDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import { useConfirm } from '@/composables/useConfirm';
import { useToast } from '@/composables/useToast';
import { trpc } from '@/lib/trpc';

// Импорт компонентов
import VCard from '@/volt/Card.vue';
import VButton from '@/volt/Button.vue';
import VInputText from '@/volt/InputText.vue';
// VDropdown removed - replaced with VAutoComplete
import VDataTable from '@/volt/DataTable.vue';
import VColumn from 'primevue/column';
import VTag from '@/volt/Tag.vue';
import VMessage from '@/volt/Message.vue';
import VDialog from '@/volt/Dialog.vue';
import PartWizard from './PartWizard.vue';
import VAutoComplete from '@/volt/AutoComplete.vue';

// Composables
const { loading, error, clearError, parts: partsApi, partCategories } = useTrpc();
const confirm = useConfirm();
const toast = useToast();

// Состояние компонента
const parts = ref<any[]>([]);
const categories = ref<any[]>([]);
const totalCount = ref(0);
const pageSize = ref(25);
const currentPage = ref(0);
const expandedRows = ref<any[]>([]);
const partAttributes = ref<Record<number, any[]>>({});

// Фильтры и поиск
const searchQuery = ref('');
const selectedCategory = ref<number | null>(null);

// Данные для автокомплита категорий
const categorySuggestions = ref<any[]>([]);

// Диалоги
const editDialogVisible = ref(false);
const selectedPartForEdit = ref<any>(null);

// Сортировка
const sortField = ref<string>('createdAt');
const sortOrder = ref<number>(-1); // -1 для DESC, 1 для ASC

// Загрузка данных
const loadParts = async () => {
  try {
    clearError();
    
    const filters: any = {
      skip: currentPage.value * pageSize.value,
      take: pageSize.value,
      orderBy: {
        [sortField.value]: sortOrder.value === 1 ? 'asc' : 'desc'
      },
      include: {
        partCategory: true,
        attributes: true,
        applicabilities: {
          include: {
            catalogItem: {
              include: {
                brand: true
              }
            }
          }
        },
        equipmentApplicabilities: {
          include: {
            equipmentModel: {
              include: {
                brand: true
              }
            }
          }
        }
      }
    };

    // Добавляем поиск
    if (searchQuery.value.trim()) {
      filters.where = {
        ...filters.where,
        name: {
          contains: searchQuery.value.trim(),
          mode: 'insensitive'
        }
      };
    }

    // Добавляем фильтр по категории
    if (selectedCategory.value) {
      // Извлекаем ID из выбранной категории
      const categoryId = typeof selectedCategory.value === 'object' 
        ? (selectedCategory.value as any).id 
        : selectedCategory.value;
        
      filters.where = {
        ...filters.where,
        partCategoryId: categoryId
      };
    }

    const result = await partsApi.findMany(filters);
    if (result) {
      parts.value = result;
    }

    // Получаем общее количество для пагинации
    const countResult = await partsApi.findMany({
      where: filters.where,
      select: { id: true }
    });
    if (countResult) {
      totalCount.value = countResult.length;
    }

  } catch (err) {
    console.error('Ошибка загрузки запчастей:', err);
  }
};

// Загрузка категорий
const loadCategories = async () => {
  try {
    const result = await partCategories.findMany({
      orderBy: { name: 'asc' }
    });
    if (result) {
      categories.value = result;
    }
  } catch (err) {
    console.error('Ошибка загрузки категорий:', err);
  }
};

// Загрузка атрибутов для конкретной запчасти
const loadPartAttributes = async (partId: number) => {
  try {
    const result = await trpc.partAttributes.findByPartId.query({ partId });
    if (result) {
      partAttributes.value[partId] = result;
    }
  } catch (err) {
    console.error('Ошибка загрузки атрибутов запчасти:', err);
  }
};

// Обработчики событий
const onPageChange = (event: any) => {
  currentPage.value = event.page;
  pageSize.value = event.rows;
  loadParts();
};

const onSort = (event: any) => {
  sortField.value = event.sortField;
  sortOrder.value = event.sortOrder;
  loadParts();
};

const onRowExpand = (event: any) => {
  const partId = event.data.id;
  if (!partAttributes.value[partId]) {
    loadPartAttributes(partId);
  }
};

const refreshData = () => {
  loadParts();
};

const applyFilters = () => {
  currentPage.value = 0;
  loadParts();
};

// Debounced поиск
let searchTimeout: NodeJS.Timeout;
const debouncedSearch = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    currentPage.value = 0;
    loadParts();
  }, 500);
};

// Действия с запчастями
const editPart = (part: any) => {
  selectedPartForEdit.value = part;
  editDialogVisible.value = true;
};

const deletePart = async (part: any) => {
  const confirmed = await confirm.require({
    message: `Вы уверены, что хотите удалить запчасть "${part.name}"?`,
    header: 'Подтверждение удаления',
    icon: 'pi pi-exclamation-triangle',
    acceptClass: 'p-button-danger'
  });

  if (confirmed) {
    try {
      await partsApi.delete({ where: { id: part.id } });
      toast.add({
        severity: 'success',
        summary: 'Успешно',
        detail: 'Запчасть удалена',
        life: 3000
      });
      loadParts();
    } catch (err) {
      toast.add({
        severity: 'error',
        summary: 'Ошибка',
        detail: 'Не удалось удалить запчасть',
        life: 5000
      });
    }
  }
};

const onPartSaved = (part: any) => {
  // Обновляем список запчастей после сохранения
  loadParts();
  selectedPartForEdit.value = null;
  editDialogVisible.value = false;

  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: 'Запчасть обновлена',
    life: 3000
  });
};

// Утилиты
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('ru-RU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
};

const getUnitLabel = (unit: string) => {
  const labels: Record<string, string> = {
    'MM': 'мм', 'INCH': 'дюймы', 'FT': 'футы',
    'G': 'г', 'KG': 'кг', 'T': 'т', 'LB': 'фунты',
    'ML': 'мл', 'L': 'л', 'GAL': 'галлоны',
    'PCS': 'шт', 'SET': 'комплект', 'PAIR': 'пара',
    'BAR': 'бар', 'PSI': 'PSI',
    'KW': 'кВт', 'HP': 'л.с.',
    'NM': 'Н⋅м', 'RPM': 'об/мин',
    'C': '°C', 'F': '°F',
    'PERCENT': '%'
  };
  return labels[unit] || unit;
};

const getAccuracyLabel = (accuracy: string) => {
  const labels: Record<string, string> = {
    'EXACT_MATCH': 'Точное совпадение',
    'MATCH_WITH_NOTES': 'С примечаниями',
    'REQUIRES_MODIFICATION': 'Требует доработки',
    'PARTIAL_MATCH': 'Частичное совпадение'
  };
  return labels[accuracy] || accuracy;
};

const getAccuracySeverity = (accuracy: string) => {
  const severities: Record<string, string> = {
    'EXACT_MATCH': 'success',
    'MATCH_WITH_NOTES': 'info',
    'REQUIRES_MODIFICATION': 'warning',
    'PARTIAL_MATCH': 'secondary'
  };
  return severities[accuracy] || 'secondary';
};

// Данные для автокомплита категорий
const filterCategories = (event: any) => {
  const query = event.query.toLowerCase();
  if (!query) {
    categorySuggestions.value = categories.value;
  } else {
    categorySuggestions.value = categories.value.filter(category =>
      category.name.toLowerCase().includes(query)
    );
  }
};

// Инициализация автокомплита категорий
const initializeCategorySuggestions = () => {
  categorySuggestions.value = categories.value;
};

// Инициализация
onMounted(() => {
  loadCategories();
  loadParts();
  initializeCategorySuggestions();
});
</script>
