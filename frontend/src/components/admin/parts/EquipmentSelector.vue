<template>
  <div class="equipment-selector">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
        Применимость к технике
      </h3>
      <VButton @click="addEquipment" size="small">
        Добавить технику
      </VButton>
    </div>

    <div v-if="modelValue.length === 0" class="text-center py-8 text-surface-500">
      Добавьте модели техники, к которым применима эта запчасть
    </div>

    <div v-for="(item, index) in modelValue" :key="index" class="border border-surface-200 dark:border-surface-700 rounded-lg p-4 mb-4">
      <div class="flex items-center justify-between mb-4">
        <h4 class="font-medium text-surface-900 dark:text-surface-0">
          Техника {{ index + 1 }}
        </h4>
        <VButton
          @click="removeEquipment(index)"
          severity="danger"
          size="small"
          text
        >
          Удалить
        </VButton>
      </div>

      <!-- Выбор: поиск существующей или создание новой -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Тип добавления
        </label>
        <VSelectButton
          v-model="item.isExisting"
          :options="equipmentTypeOptions"
          option-label="label"
          option-value="value"
          class="w-full"
        />
      </div>

      <!-- Поиск существующей модели техники -->
      <div v-if="item.isExisting" class="mb-4">
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Поиск модели техники *
        </label>
        <VAutoComplete
          v-model="item.existingEquipmentModel"
          :suggestions="equipmentSuggestions"
          @complete="(event) => searchEquipmentModels(event, index)"
          option-label="name"
          placeholder="Введите название модели для поиска..."
          class="w-full"
          dropdown
        >
          <template #option="{ option }">
            <div class="flex items-center gap-2">
              <div class="flex-1">
                <div class="font-medium">{{ option.name }}</div>
                <div v-if="option.brand" class="text-sm text-surface-600">{{ option.brand.name }}</div>
              </div>
            </div>
          </template>
        </VAutoComplete>
      </div>

      <!-- Создание новой модели техники -->
      <div v-else class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
              Название модели *
            </label>
            <VInputText
              v-model="item.name"
              placeholder="Например: CAT 320D"
              class="w-full p-3"
            />
          </div>

          <div>
            <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
              Бренд
            </label>
            <VAutoComplete
              v-model="item.selectedBrand"
              :suggestions="brandSuggestions"
              @complete="searchBrands"
              option-label="name"
              placeholder="Поиск бренда..."
              class="w-full"
              dropdown
            />
          </div>
        </div>
      </div>

      <!-- Примечания -->
      <div class="mt-4">
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Примечания
        </label>
        <VInputText
          v-model="item.notes"
          placeholder="Дополнительная информация о применимости к данной технике"
          class="w-full p-3"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import VButton from '@/volt/Button.vue';
import VInputText from '@/volt/InputText.vue';
import VAutoComplete from '@/volt/AutoComplete.vue';
import VSelectButton from '@/volt/SelectButton.vue';

// Props
interface Props {
  modelValue: EquipmentApplicabilityForm[];
}

interface Emits {
  (e: 'update:modelValue', value: EquipmentApplicabilityForm[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// Интерфейсы
interface EquipmentApplicabilityForm {
  // Для создания новой модели
  name: string;
  selectedBrand: any;
  
  // Для поиска существующей модели
  isExisting?: boolean; // true если выбрана существующая модель
  existingEquipmentModel?: any; // выбранная существующая модель
  
  // Примечания
  notes?: string;
}

// tRPC клиент
const { equipmentModels, brands } = useTrpc();

// Опции для типа техники
const equipmentTypeOptions = [
  { label: "Создать новую модель", value: false },
  { label: "Найти существующую", value: true },
];

// Данные для автокомплита
const equipmentSuggestions = ref<any[]>([]);
const brandSuggestions = ref<any[]>([]);

// Computed для v-model
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// Поиск моделей техники
const searchEquipmentModels = async (event: any, _itemIndex: number) => {
  const query = event.query.toLowerCase();
  const equipmentResult = await equipmentModels.findMany({
    where: {
      name: {
        contains: query,
        mode: 'insensitive'
      }
    },
    include: {
      brand: true
    },
    take: 10
  });

  if (equipmentResult && Array.isArray(equipmentResult)) {
    equipmentSuggestions.value = equipmentResult;
  }
};

// Поиск брендов
const searchBrands = async (event: any) => {
  const query = event.query.toLowerCase();
  const brandsResult = await brands.findMany({
    where: {
      name: {
        contains: query,
        mode: 'insensitive'
      }
    },
    take: 10
  });

  if (brandsResult && Array.isArray(brandsResult)) {
    brandSuggestions.value = brandsResult;
  }
};

// Методы работы с техникой
const addEquipment = () => {
  const newEquipment: EquipmentApplicabilityForm = {
    name: '',
    selectedBrand: null,
    isExisting: false,
    existingEquipmentModel: null,
    notes: ''
  };
  
  modelValue.value = [...modelValue.value, newEquipment];
};

const removeEquipment = (index: number) => {
  const newValue = [...modelValue.value];
  newValue.splice(index, 1);
  modelValue.value = newValue;
};
</script>
