<template>
  <div class="attribute-manager">
    <!-- Используем новый универсальный компонент AttributeEditor -->
    <AttributeEditor
      v-model="attributes"
      title="Атрибуты запчасти"
      description="Выберите шаблон атрибута и укажите значение"
      :show-group-selector="true"
      :group-by-template="true"
      card-mode="detailed"
      :entity-id="partId"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import AttributeEditor from '@/components/admin/attributes/AttributeEditor.vue';

// Интерфейс атрибута
interface AttributeForm {
  id?: number;
  templateId: number;
  value: string;
  template?: any;
  templateTitle?: string;
  templateDataType?: string;
  templateUnit?: string;
  templateGroup?: string;
  templateDescription?: string;
}

// Props
interface Props {
  modelValue: AttributeForm[];
  partId?: number;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: AttributeForm[]];
}>();

// Локальное состояние
const attributes = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});
</script>
