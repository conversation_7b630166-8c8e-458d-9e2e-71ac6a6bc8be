<template>
  <VDialog v-model:visible="visible" modal header="Создать новый бренд" class="w-96">
    <div class="space-y-4">
      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Название бренда *
        </label>
        <VInputText 
          v-model="formData.name" 
          placeholder="Например: SKF"
          class="w-full"
          :class="{ 'p-invalid': errors.name }"
        />
        <small v-if="errors.name" class="text-red-500">{{ errors.name }}</small>
      </div>

      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Страна
        </label>
        <VInputText 
          v-model="formData.country" 
          placeholder="Например: Швеция"
          class="w-full"
        />
      </div>

      <div>
        <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
          Тип производителя
        </label>
        <div class="flex gap-4">
          <label class="flex items-center">
            <input 
              type="radio" 
              v-model="formData.isOem" 
              :value="false"
              class="mr-2"
            />
            Aftermarket
          </label>
          <label class="flex items-center">
            <input 
              type="radio" 
              v-model="formData.isOem" 
              :value="true"
              class="mr-2"
            />
            OEM
          </label>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <VButton 
          @click="visible = false" 
          severity="secondary" 
          outlined
          :disabled="loading"
        >
          Отмена
        </VButton>
        <VButton 
          @click="createBrand" 
          :loading="loading"
          :disabled="!canCreate"
        >
          Создать
        </VButton>
      </div>
    </template>

    <VMessage v-if="error" severity="error" class="mt-4">
      {{ error }}
    </VMessage>
  </VDialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import VDialog from '@/volt/Dialog.vue';
import VButton from '@/volt/Button.vue';
import VInputText from '@/volt/InputText.vue';
import VMessage from '@/volt/Message.vue';

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'created', brand: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { loading, error, clearError, brands } = useTrpc();

const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const formData = ref({
  name: '',
  country: '',
  isOem: false
});

const errors = ref({
  name: ''
});

// Валидация
const canCreate = computed(() => {
  return formData.value.name.trim().length > 0 && !loading.value;
});

// Создание бренда
const createBrand = async () => {
  clearError();
  errors.value = { name: '' };

  // Валидация
  if (!formData.value.name.trim()) {
    errors.value.name = 'Название обязательно';
    return;
  }

  try {
    // Генерируем slug из названия
    const slug = formData.value.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    const brandData = {
      name: formData.value.name.trim(),
      slug: slug,
      country: formData.value.country?.trim() || undefined,
      isOem: formData.value.isOem
    };

    const result = await brands.create({
      data: brandData
    });

    if (result) {
      emit('created', result);
      resetForm();
      visible.value = false;
    }
  } catch (err: any) {
    console.error('Ошибка создания бренда:', err);
  }
};

// Сброс формы
const resetForm = () => {
  formData.value = {
    name: '',
    country: '',
    isOem: false
  };
  errors.value = { name: '' };
  clearError();
};

// Сброс формы при закрытии
watch(visible, (newValue) => {
  if (!newValue) {
    resetForm();
  }
});
</script>
