<template>
  <div class="catalog-item-editor">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
        Каталожные позиции
      </h3>
      <VButton @click="addCatalogItem" size="small" icon="pi pi-plus">
        Добавить позицию
      </VButton>
    </div>

    <div v-if="modelValue.length === 0" class="text-center py-8 text-surface-500">
      Добавьте хотя бы одну каталожную позицию
    </div>

    <div v-else class="space-y-4">
      <VCard 
        v-for="(item, index) in modelValue" 
        :key="index"
        class="border border-surface-200 dark:border-surface-700"
      >
        <template #content>
          <div class="p-4">
            <!-- Заголовок карточки -->
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center gap-3">
                <VTag 
                  :value="`Позиция ${index + 1}`" 
                  severity="secondary" 
                  size="small" 
                />
                <VTag 
                  v-if="item.isExisting" 
                  value="Существующая" 
                  severity="info" 
                  size="small" 
                />
                <VTag 
                  v-else 
                  value="Новая" 
                  severity="success" 
                  size="small" 
                />
              </div>
              <VButton
                @click="removeCatalogItem(index)"
                severity="danger"
                size="small"
                text
                icon="pi pi-trash"
              />
            </div>

            <!-- Основная информация -->
            <div v-if="item.isExisting && item.existingCatalogItem" class="mb-4">
              <!-- Отображение существующей позиции -->
              <div class="bg-surface-50 dark:bg-surface-900 rounded-lg p-4">
                <div class="flex items-center gap-3 mb-2">
                  <span class="font-semibold text-lg">{{ item.existingCatalogItem.sku }}</span>
                  <VTag :value="item.existingCatalogItem.brand?.name" severity="secondary" />
                </div>
                <p v-if="item.existingCatalogItem.description" class="text-surface-600 dark:text-surface-400">
                  {{ item.existingCatalogItem.description }}
                </p>
                <VButton 
                  @click="switchToSearch(index)"
                  severity="secondary" 
                  outlined 
                  size="small" 
                  class="mt-3"
                  label="Изменить позицию"
                />
              </div>
            </div>

            <div v-else-if="!item.isExisting" class="mb-4">
              <!-- Создание новой позиции -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                    Артикул (SKU) *
                  </label>
                  <VInputText
                    v-model="item.sku"
                    placeholder="Например: 12345-ABC"
                    class="w-full"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                    Бренд *
                  </label>
                  <div class="flex gap-2">
                    <VAutoComplete
                      v-model="item.selectedBrand"
                      :suggestions="brandSuggestions"
                      @complete="searchBrands"
                      @change="onBrandSelected(index, $event)"
                      option-label="name"
                      placeholder="Поиск бренда..."
                      class="flex-1"
                      dropdown
                    />
                    <VButton
                      @click="showCreateBrand = true"
                      severity="secondary"
                      outlined
                      size="small"
                      icon="pi pi-plus"
                    />
                  </div>
                </div>

                <div class="md:col-span-2">
                  <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                    Описание
                  </label>
                  <VInputText
                    v-model="item.description"
                    placeholder="Описание каталожной позиции"
                    class="w-full"
                  />
                </div>
              </div>
              
              <VButton 
                @click="switchToSearch(index)"
                severity="secondary" 
                outlined 
                size="small" 
                class="mt-3"
                label="Найти существующую"
              />
            </div>

            <div v-else class="mb-4">
              <!-- Поиск существующей позиции -->
              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Поиск каталожной позиции *
                </label>
                <VAutoComplete
                  v-model="item.existingCatalogItem"
                  :suggestions="catalogItemSuggestions"
                  @complete="(event) => searchCatalogItems(event, index)"
                  option-label="sku"
                  placeholder="Введите артикул для поиска..."
                  class="w-full"
                  dropdown
                >
                  <template #option="{ option }">
                    <div class="flex items-center gap-2">
                      <div class="flex-1">
                        <div class="font-medium">{{ option.sku }}</div>
                        <div class="text-sm text-surface-600">{{ option.brand?.name }}</div>
                        <div v-if="option.description" class="text-xs text-surface-500">
                          {{ option.description }}
                        </div>
                      </div>
                    </div>
                  </template>
                </VAutoComplete>
              </div>
              
              <VButton 
                @click="switchToCreate(index)"
                severity="secondary" 
                outlined 
                size="small" 
                class="mt-3"
                label="Создать новую"
              />
            </div>

            <!-- Уровень точности применимости -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Уровень точности *
                </label>
                <VAutoComplete
                  v-model="item.accuracy"
                  :suggestions="filteredAccuracyOptions"
                  @complete="filterAccuracyOptions"
                  option-label="label"
                  option-value="value"
                  placeholder="Выберите уровень точности"
                  class="w-full"
                  dropdown
                />
              </div>

              <div v-if="item.accuracy !== 'EXACT_MATCH'">
                <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                  Примечания
                </label>
                <VInputText
                  v-model="item.notes"
                  placeholder="Дополнительная информация о применимости"
                  class="w-full"
                />
              </div>
            </div>
          </div>
        </template>
      </VCard>
    </div>

    <!-- Диалог создания бренда -->
    <QuickCreateBrand
      v-model:visible="showCreateBrand"
      @created="onBrandCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import VCard from '@/volt/Card.vue';
import VButton from '@/volt/Button.vue';
import VInputText from '@/volt/InputText.vue';
import VAutoComplete from '@/volt/AutoComplete.vue';
import VTag from '@/volt/Tag.vue';
import QuickCreateBrand from './QuickCreateBrand.vue';

// Интерфейсы
interface CatalogItemForm {
  sku: string;
  brandId: number | "";
  selectedBrand: any;
  description: string;
  isExisting?: boolean;
  existingCatalogItem?: any;
  accuracy: "EXACT_MATCH" | "MATCH_WITH_NOTES" | "REQUIRES_MODIFICATION" | "PARTIAL_MATCH";
  notes?: string;
}

// Props
interface Props {
  modelValue: CatalogItemForm[];
}

interface Emits {
  (e: 'update:modelValue', value: CatalogItemForm[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// tRPC клиент
const { brands, catalogItems } = useTrpc();

// Локальное состояние
const brandSuggestions = ref<any[]>([]);
const catalogItemSuggestions = ref<any[]>([]);
const showCreateBrand = ref(false);

// Опции для уровня точности
const accuracyOptions = [
  { label: "Точное совпадение", value: "EXACT_MATCH" },
  { label: "Совпадение с примечаниями", value: "MATCH_WITH_NOTES" },
  { label: "Требует доработки", value: "REQUIRES_MODIFICATION" },
  { label: "Частичное совпадение", value: "PARTIAL_MATCH" },
];

const filteredAccuracyOptions = ref(accuracyOptions);

// Computed для v-model
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// Методы
const addCatalogItem = () => {
  const newItem: CatalogItemForm = {
    sku: "",
    brandId: "",
    selectedBrand: null,
    description: "",
    isExisting: false,
    existingCatalogItem: null,
    accuracy: "EXACT_MATCH",
    notes: "",
  };
  
  modelValue.value = [...modelValue.value, newItem];
};

const removeCatalogItem = (index: number) => {
  const newValue = [...modelValue.value];
  newValue.splice(index, 1);
  modelValue.value = newValue;
};

const switchToSearch = (index: number) => {
  const newValue = [...modelValue.value];
  newValue[index].isExisting = true;
  newValue[index].existingCatalogItem = null;
  modelValue.value = newValue;
};

const switchToCreate = (index: number) => {
  const newValue = [...modelValue.value];
  newValue[index].isExisting = false;
  newValue[index].sku = "";
  newValue[index].selectedBrand = null;
  newValue[index].description = "";
  modelValue.value = newValue;
};

// Поиск брендов
const searchBrands = async (event: any) => {
  const query = event.query.toLowerCase();
  const brandsResult = await brands.findMany({
    where: {
      name: {
        contains: query,
        mode: 'insensitive'
      }
    },
    take: 10
  });

  if (brandsResult && Array.isArray(brandsResult)) {
    brandSuggestions.value = brandsResult;
  }
};

// Поиск каталожных позиций
const searchCatalogItems = async (event: any, _itemIndex: number) => {
  const query = event.query.toLowerCase();
  const catalogItemsResult = await catalogItems.findMany({
    where: {
      OR: [
        {
          sku: {
            contains: query,
            mode: 'insensitive'
          }
        },
        {
          description: {
            contains: query,
            mode: 'insensitive'
          }
        }
      ]
    },
    include: {
      brand: true
    },
    take: 10
  });

  if (catalogItemsResult && Array.isArray(catalogItemsResult)) {
    catalogItemSuggestions.value = catalogItemsResult;
  }
};

// Фильтрация опций точности
const filterAccuracyOptions = (event: any) => {
  const query = event.query.toLowerCase();
  if (!query) {
    filteredAccuracyOptions.value = accuracyOptions;
  } else {
    filteredAccuracyOptions.value = accuracyOptions.filter(option =>
      option.label.toLowerCase().includes(query)
    );
  }
};

// Обработчик выбора бренда
const onBrandSelected = (index: number, event: any) => {
  const newValue = [...modelValue.value];
  if (event.value && typeof event.value === 'object') {
    newValue[index].brandId = event.value.id;
  }
  modelValue.value = newValue;
};

// Обработчик создания бренда
const onBrandCreated = (brand: any) => {
  brandSuggestions.value = [brand, ...brandSuggestions.value];

  // Автоматически выбираем созданный бренд для текущей позиции
  // Находим последнюю добавленную позицию (которая создавала бренд)
  const lastIndex = modelValue.value.length - 1;
  if (lastIndex >= 0 && !modelValue.value[lastIndex].selectedBrand) {
    const newValue = [...modelValue.value];
    newValue[lastIndex].selectedBrand = brand;
    newValue[lastIndex].brandId = brand.id;
    modelValue.value = newValue;
  }
};
</script>
