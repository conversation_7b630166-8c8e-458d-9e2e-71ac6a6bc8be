<template>
  <div class="simple-attribute-manager">
    <div class="flex items-center justify-between mb-4">
      <div class="flex items-center gap-3">
        <h3 class="text-lg font-medium text-surface-900 dark:text-surface-0">
          Атрибуты запчасти
        </h3>
        <VTag
          v-if="modelValue.length > 0"
          :value="`${filledAttributesCount}/${modelValue.length} заполнено`"
          :severity="filledAttributesCount === modelValue.length ? 'success' : 'warn'"
          size="small"
        />
      </div>
    </div>

    <!-- Быстрое добавление атрибутов по группе -->
    <VCard class="mb-4">
      <template #content>
        <div class="p-4">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                Группа шаблонов
              </label>
              <VAutoComplete
                v-model="selectedTemplateGroup"
                :suggestions="groupSuggestions"
                @complete="filterGroups"
                option-label="name"
                placeholder="Поиск группы..."
                class="w-full"
                dropdown
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-surface-700 dark:text-surface-300 mb-2">
                Или отдельный шаблон
              </label>
              <VAutoComplete
                v-model="selectedTemplate"
                :suggestions="templateSuggestions"
                @complete="filterTemplates"
                option-label="title"
                placeholder="Поиск шаблона..."
                class="w-full"
                dropdown
              >
                <template #option="{ option }">
                  <div class="flex items-center gap-2">
                    <i :class="getDataTypeIcon(option.dataType)" class="text-primary"></i>
                    <div class="flex-1">
                      <div class="font-medium">{{ option.title }}</div>
                      <div class="text-sm text-surface-600">
                        {{ option.group?.name }} • {{ getDataTypeLabel(option.dataType) }}
                      </div>
                    </div>
                  </div>
                </template>
              </VAutoComplete>
            </div>
              <div class="flex items-end gap-2">
                <VButton
                  @click="loadSelectedGroupTemplates"
                  size="small"
                  outlined
                  :disabled="!selectedTemplateGroup || loadingTemplates"
                  :loading="loadingTemplates"
                  label="Добавить группу"
                  class="flex-1"
                />
                <VButton
                  @click="addSingleTemplate"
                  size="small"
                  outlined
                  :disabled="!selectedTemplate"
                  label="Добавить"
                  class="flex-1"
                />
              </div>
          </div>
        </div>
      </template>
    </VCard>

    <!-- Простой список атрибутов с inline редактированием -->
    <div v-if="modelValue.length > 0" class="space-y-3">
      <div v-for="(attribute, index) in modelValue" :key="attribute.id || `new-${index}`">
        <div class="flex items-center gap-3 p-4 border rounded-lg transition-all duration-200 hover:shadow-sm"
             :class="[
               attribute.value
                 ? 'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/10'
                 : 'border-orange-200 dark:border-orange-700 bg-orange-50 dark:bg-orange-900/10'
             ]">
          
          <!-- Иконка и статус -->
          <div class="flex-shrink-0 relative">
            <i :class="getDataTypeIcon(attribute.templateDataType)" class="text-lg text-primary"></i>
            <div class="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-surface-900"
                 :class="attribute.value ? 'bg-green-500' : 'bg-orange-500'"
                 :title="attribute.value ? 'Заполнено' : 'Не заполнено'">
            </div>
          </div>
          
          <!-- Название и группа -->
          <div class="flex-shrink-0 w-48">
            <div class="font-medium text-surface-900 dark:text-surface-0 text-sm">
              {{ attribute.templateTitle || 'Без названия' }}
            </div>
            <VTag 
              v-if="attribute.templateGroup" 
              :value="attribute.templateGroup" 
              severity="secondary" 
              size="small" 
              class="mt-1"
            />
          </div>
          
          <!-- Поле ввода значения -->
          <div class="flex-1">
            <AttributeValueInput
              :template="getTemplateForInput(attribute)"
              :model-value="attribute.value"
              @update:model-value="updateAttributeValue(index, $event)"
              class="w-full"
              :placeholder="getPlaceholder(attribute)"
            />
          </div>
          
          <!-- Единица измерения -->
          <div class="flex-shrink-0 w-16 text-center">
            <span v-if="attribute.templateUnit" class="text-sm text-surface-500 font-medium">
              {{ getUnitLabel(attribute.templateUnit) }}
            </span>
          </div>
          
          <!-- Кнопка удаления -->
          <div class="flex-shrink-0">
            <VButton
              @click="removeAttribute(index)"
              severity="danger"
              size="small"
              outlined
              icon="pi pi-trash"
              label="Удалить"
              class="hover:bg-red-50 dark:hover:bg-red-900/20"
            />
          </div>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-8 text-surface-500 border-2 border-dashed border-surface-200 dark:border-surface-700 rounded-lg">
      <i class="pi pi-plus-circle text-3xl mb-2 block"></i>
      <p>Добавьте атрибуты для описания характеристик запчасти</p>
      <p class="text-sm mt-1">Выберите группу шаблонов или отдельный шаблон выше</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useTrpc } from '@/composables/useTrpc';
import VCard from '@/volt/Card.vue';
import VButton from '@/volt/Button.vue';
import VAutoComplete from '@/volt/AutoComplete.vue';
import VTag from '@/volt/Tag.vue';
import AttributeValueInput from '@/components/admin/attributes/AttributeValueInput.vue';

// Интерфейсы
interface AttributeTemplate {
  id: number;
  name: string;
  title: string;
  description?: string;
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON';
  unit?: string;
  isRequired: boolean;
  minValue?: number;
  maxValue?: number;
  allowedValues?: string[];
  group?: {
    id: number;
    name: string;
  };
}
interface AttributeForm {
  id?: number;
  templateId: number;
  value: string;
  template?: any;
  templateTitle?: string;
  templateDataType?: string;
  templateUnit?: string;
  templateGroup?: string;
  templateDescription?: string;
}

// Props
interface Props {
  modelValue: AttributeForm[];
}

interface Emits {
  (e: 'update:modelValue', value: AttributeForm[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// tRPC клиент
const { client, partAttributes, attributeTemplates } = useTrpc();

// Локальное состояние
const selectedTemplateGroup = ref<any>(null);
const selectedTemplate = ref<any>(null);
const groupSuggestions = ref<any[]>([]);
const templateSuggestions = ref<any[]>([]);
const loadingTemplates = ref(false);

// Computed для v-model
const modelValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// Подсчет заполненных атрибутов
const filledAttributesCount = computed(() => {
  return modelValue.value.filter(attr => attr.value && String(attr.value).trim()).length;
});

// Методы
const updateAttributeValue = (index: number, value: any) => {
  const newValue = [...props.modelValue];
  newValue[index].value = value;
  emit('update:modelValue', newValue);
};

const removeAttribute = async (index: number) => {
  const attribute = props.modelValue[index];

  // Если атрибут уже сохранен в БД (имеет id), удаляем его через API
  if (attribute.id) {
    try {
      const result = await partAttributes.delete({ id: attribute.id });
      console.log('Атрибут успешно удален из БД:', attribute.id, result);
    } catch (error) {
      console.error('Ошибка удаления атрибута:', error);
      // Можно показать уведомление об ошибке
      return;
    }
  }

  // Удаляем из локального состояния
  const newValue = [...props.modelValue];
  newValue.splice(index, 1);
  emit('update:modelValue', newValue);
};

// Создает объект template для AttributeValueInput
const getTemplateForInput = (attribute: AttributeForm | null): AttributeTemplate => {
  if (!attribute) {
    return {
      id: 0,
      name: '',
      title: '',
      description: '',
      dataType: 'STRING',
      unit: undefined,
      isRequired: false,
      minValue: undefined,
      maxValue: undefined,
      allowedValues: [],
    };
  }

  return {
    id: attribute.templateId,
    name: attribute.template?.name || '',
    title: attribute.templateTitle || '',
    description: attribute.templateDescription || '',
    dataType: (attribute.templateDataType as 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE' | 'JSON') || 'STRING',
    unit: attribute.templateUnit,
    isRequired: attribute.template?.isRequired || false,
    minValue: attribute.template?.minValue,
    maxValue: attribute.template?.maxValue,
    allowedValues: attribute.template?.allowedValues || [],
    group: attribute.template?.group
  };
};

// Добавление одного шаблона
const addSingleTemplate = () => {
  if (!selectedTemplate.value) return;

  const template = selectedTemplate.value;
  const attribute: AttributeForm = {
    templateId: template.id,
    value: "",
    template: template,
    templateTitle: template.title,
    templateDataType: template.dataType,
    templateUnit: template.unit,
    templateGroup: template.group?.name,
    templateDescription: template.description,
  };

  emit('update:modelValue', [...props.modelValue, attribute]);
  selectedTemplate.value = null;
};

// Поиск групп шаблонов
const filterGroups = async (event: any) => {
  const query = event.query.toLowerCase();
  try {
    const groups = await attributeTemplates.findAllGroups();

    if (groups && Array.isArray(groups)) {
      // Фильтруем группы по запросу
      const filteredGroups = groups.filter((group: any) =>
        group.name.toLowerCase().includes(query)
      ).slice(0, 10);
      groupSuggestions.value = filteredGroups;
    }
  } catch (error) {
    console.error('Ошибка поиска групп:', error);
    groupSuggestions.value = [];
  }
};

// Загрузка шаблонов выбранной группы
const loadSelectedGroupTemplates = async () => {
  if (!selectedTemplateGroup.value) return;

  loadingTemplates.value = true;
  try {
    const groupId = selectedTemplateGroup.value.id || selectedTemplateGroup.value;
    const templates = await attributeTemplates.findMany({ groupId });

    if (templates && Array.isArray(templates)) {
      // Добавляем шаблоны как новые атрибуты
      const newAttributes = templates.map((template: any) => ({
        templateId: template.id,
        value: "",
        template: template,
        templateTitle: template.title,
        templateDataType: template.dataType,
        templateUnit: template.unit,
        templateGroup: template.group?.name,
        templateDescription: template.description,
      }));

      emit('update:modelValue', [...props.modelValue, ...newAttributes]);
      selectedTemplateGroup.value = null;
    }
  } catch (error) {
    console.error('Ошибка загрузки шаблонов:', error);
  } finally {
    loadingTemplates.value = false;
  }
};

// Поиск шаблонов
const filterTemplates = async (event: any) => {
  const query = event.query.toLowerCase();
  try {
    const result = await attributeTemplates.findMany({
      search: query,
      limit: 10
    });

    if (result && Array.isArray((result as any).templates)) {
      templateSuggestions.value = (result as any).templates;
    } else {
      templateSuggestions.value = [];
    }
  } catch (error) {
    console.error('Ошибка поиска шаблонов:', error);
    templateSuggestions.value = [];
  }
};

// Утилиты
const getDataTypeIcon = (dataType: string | undefined) => {
  const icons: Record<string, string> = {
    STRING: 'pi pi-font',
    NUMBER: 'pi pi-hashtag',
    BOOLEAN: 'pi pi-check-square',
    DATE: 'pi pi-calendar',
    JSON: 'pi pi-code',
  };
  return icons[dataType || ''] || 'pi pi-question';
};

const getDataTypeLabel = (dataType: string | undefined) => {
  if (!dataType) return "";
  const labels: Record<string, string> = {
    STRING: "Строка",
    NUMBER: "Число",
    BOOLEAN: "Логическое",
    DATE: "Дата",
    JSON: "JSON",
  };
  return labels[dataType] || dataType;
};

const getUnitLabel = (unit: string | undefined) => {
  if (!unit) return "";
  const labels: Record<string, string> = {
    MM: "мм",
    INCH: "дюймы",
    FT: "футы",
    G: "г",
    KG: "кг",
    T: "т",
    LB: "фунты",
    ML: "мл",
    L: "л",
    GAL: "галлоны",
    PCS: "шт",
    SET: "комплект",
    PAIR: "пара",
    BAR: "бар",
    PSI: "PSI",
    KW: "кВт",
    HP: "л.с.",
    NM: "Н⋅м",
    RPM: "об/мин",
    C: "°C",
    F: "°F",
    PERCENT: "%",
  };
  return labels[unit] || unit;
};

const getPlaceholder = (attribute: AttributeForm) => {
  const dataType = attribute.templateDataType;
  const unit = attribute.templateUnit;

  switch (dataType) {
    case 'STRING':
      return 'Введите текст...';
    case 'NUMBER':
      return unit ? `Введите число (${getUnitLabel(unit)})...` : 'Введите число...';
    case 'BOOLEAN':
      return 'Выберите значение...';
    case 'DATE':
      return 'Выберите дату...';
    default:
      return 'Введите значение...';
  }
};
</script>
