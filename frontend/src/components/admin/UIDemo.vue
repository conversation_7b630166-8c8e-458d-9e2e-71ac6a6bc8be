<template>
  <div class="space-y-8">
    <!-- Заголовок -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-surface-900 mb-2">UI Компоненты</h1>
      <p class="text-surface-600">
        Демонстрация всех доступных UI компонентов с поддержкой тем
      </p>
    </div>

    <!-- Breadcrumb -->
    <div class="bg-surface-section rounded-lg p-6 border border-surface-border">
      <h2 class="text-xl font-semibold text-surface-900 mb-4">Breadcrumb</h2>
      <Breadcrumb :model="breadcrumbItems" />
    </div>

    <!-- Кнопки и переключатель тем -->
    <div class="bg-surface-section rounded-lg p-6 border border-surface-border">
      <h2 class="text-xl font-semibold text-surface-900 mb-4">Кнопки и переключатель тем</h2>
      <div class="flex flex-wrap gap-4 items-center">
        <Button label="Основная кнопка" />
        <SecondaryButton label="Вторичная кнопка" />
        <SecondaryButton label="С иконкой" icon="pi pi-plus" />
        <ThemeToggle mode="buttons" show-label />
      </div>
    </div>

    <!-- Toast уведомления -->
    <div class="bg-surface-section rounded-lg p-6 border border-surface-border">
      <h2 class="text-xl font-semibold text-surface-900 mb-4">Toast уведомления</h2>
      <div class="flex flex-wrap gap-4">
        <SecondaryButton 
          label="Успех" 
          @click="showSuccessToast"
          class="bg-green-500 text-white hover:bg-green-600"
        />
        <SecondaryButton 
          label="Информация" 
          @click="showInfoToast"
          class="bg-blue-500 text-white hover:bg-blue-600"
        />
        <SecondaryButton 
          label="Предупреждение" 
          @click="showWarnToast"
          class="bg-yellow-500 text-white hover:bg-yellow-600"
        />
        <SecondaryButton 
          label="Ошибка" 
          @click="showErrorToast"
          class="bg-red-500 text-white hover:bg-red-600"
        />
      </div>
    </div>

    <!-- Диалоги -->
    <div class="bg-surface-section rounded-lg p-6 border border-surface-border">
      <h2 class="text-xl font-semibold text-surface-900 mb-4">Диалоги</h2>
      <div class="flex flex-wrap gap-4">
        <SecondaryButton 
          label="Обычный диалог" 
          @click="showDialog"
        />
        <SecondaryButton 
          label="Подтверждение удаления" 
          @click="showDeleteConfirm"
          class="bg-red-500 text-white hover:bg-red-600"
        />
        <SecondaryButton 
          label="Подтверждение сохранения" 
          @click="showSaveConfirm"
          class="bg-green-500 text-white hover:bg-green-600"
        />
      </div>
    </div>

    <!-- DataTable -->
    <div class="bg-surface-section rounded-lg p-6 border border-surface-border">
      <h2 class="text-xl font-semibold text-surface-900 mb-4">DataTable</h2>
      <DataTable 
        :value="tableData" 
        paginator 
        :rows="5"
        :rowsPerPageOptions="[5, 10, 20]"
        tableStyle="min-width: 50rem"
      >
        <Column field="id" header="ID" sortable style="width: 10%"></Column>
        <Column field="name" header="Название" sortable style="width: 30%"></Column>
        <Column field="category" header="Категория" sortable style="width: 25%"></Column>
        <Column field="status" header="Статус" style="width: 15%">
          <template #body="slotProps">
            <span 
              :class="{
                'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs': slotProps.data.status === 'Активен',
                'bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs': slotProps.data.status === 'Неактивен',
                'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs': slotProps.data.status === 'Ожидание'
              }"
            >
              {{ slotProps.data.status }}
            </span>
          </template>
        </Column>
        <Column header="Действия" style="width: 20%">
          <template #body="slotProps">
            <div class="flex gap-2">
              <SecondaryButton 
                icon="pi pi-pencil" 
                text 
                size="small"
                @click="editItem(slotProps.data)"
              />
              <SecondaryButton 
                icon="pi pi-trash" 
                text 
                size="small"
                class="text-red-500 hover:text-red-700"
                @click="deleteItem(slotProps.data)"
              />
            </div>
          </template>
        </Column>
      </DataTable>
    </div>

    <!-- Диалог -->
    <Dialog 
      v-model:visible="dialogVisible" 
      modal 
      header="Пример диалога"
      style="width: 25rem"
    >
      <p class="text-surface-600 mb-4">
        Это пример диалогового окна с поддержкой тем.
      </p>
      <div class="flex justify-end gap-2">
        <SecondaryButton 
          label="Отмена" 
          @click="dialogVisible = false"
        />
        <Button 
          label="Сохранить" 
          @click="dialogVisible = false"
        />
      </div>
    </Dialog>

    <!-- Toast контейнер -->
    <Toast />

    <!-- ConfirmDialog -->
    <ConfirmDialog />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useToast } from '@/composables/useToast'
import { useConfirm } from '@/composables/useConfirm'

// Импорт компонентов
import Button from '@/volt/Button.vue'
import SecondaryButton from '@/volt/SecondaryButton.vue'
import DataTable from '@/volt/DataTable.vue'
import Column from 'primevue/column'
import Dialog from '@/volt/Dialog.vue'
import Toast from '@/volt/Toast.vue'
import ConfirmDialog from '@/volt/ConfirmDialog.vue'
import Breadcrumb from '@/volt/Breadcrumb.vue'
import ThemeToggle from '@/components/ui/ThemeToggle.vue'

// Composables
const toast = useToast()
const confirm = useConfirm()

// Состояние
const dialogVisible = ref(false)

// Данные для breadcrumb
const breadcrumbItems = ref([
  { label: 'Админ панель', url: '/admin' },
  { label: 'UI Компоненты' }
])

// Данные для таблицы
const tableData = ref([
  { id: 1, name: 'Сальник коленвала', category: 'Двигатель', status: 'Активен' },
  { id: 2, name: 'Фильтр масляный', category: 'Система смазки', status: 'Активен' },
  { id: 3, name: 'Прокладка ГБЦ', category: 'Двигатель', status: 'Неактивен' },
  { id: 4, name: 'Тормозные колодки', category: 'Тормозная система', status: 'Ожидание' },
  { id: 5, name: 'Амортизатор передний', category: 'Подвеска', status: 'Активен' },
  { id: 6, name: 'Свеча зажигания', category: 'Система зажигания', status: 'Активен' },
  { id: 7, name: 'Ремень ГРМ', category: 'Двигатель', status: 'Неактивен' },
  { id: 8, name: 'Радиатор охлаждения', category: 'Система охлаждения', status: 'Активен' }
])

// Методы для Toast
const showSuccessToast = () => {
  toast.success('Успешно!', 'Операция выполнена успешно')
}

const showInfoToast = () => {
  toast.info('Информация', 'Это информационное сообщение')
}

const showWarnToast = () => {
  toast.warn('Внимание!', 'Это предупреждение')
}

const showErrorToast = () => {
  toast.error('Ошибка!', 'Произошла ошибка при выполнении операции')
}

// Методы для диалогов
const showDialog = () => {
  dialogVisible.value = true
}

const showDeleteConfirm = () => {
  confirm.confirmDelete('запись', () => {
    toast.success('Удалено', 'Запись успешно удалена')
  })
}

const showSaveConfirm = () => {
  confirm.confirmSave('Сохранить изменения?', () => {
    toast.success('Сохранено', 'Изменения успешно сохранены')
  })
}

// Методы для таблицы
const editItem = (item: any) => {
  toast.info('Редактирование', `Редактирование записи: ${item.name}`)
}

const deleteItem = (item: any) => {
  confirm.confirmDelete(`запись "${item.name}"`, () => {
    toast.success('Удалено', `Запись "${item.name}" успешно удалена`)
  })
}
</script>
