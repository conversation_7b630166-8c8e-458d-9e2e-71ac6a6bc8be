/**
 * Простой тест интеграции better-auth
 */

const API_BASE = 'http://localhost:3000';

async function testAuth() {
  console.log('🧪 Тестирование интеграции better-auth...\n');

  // 1. Проверяем сессию (должна быть null)
  console.log('1. Проверяем текущую сессию...');
  try {
    const sessionResponse = await fetch(`${API_BASE}/api/auth/session`);
    const sessionData = await sessionResponse.json();
    console.log('   Сессия:', sessionData);
  } catch (error) {
    console.error('   ❌ Ошибка получения сессии:', error.message);
    return;
  }

  // 2. Пробуем войти с тестовыми данными
  console.log('\n2. Пробуем войти как администратор...');
  try {
    const loginResponse = await fetch(`${API_BASE}/api/auth/sign-in/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    const loginData = await loginResponse.json();
    console.log('   Результат входа:', loginData);

    if (loginData.user) {
      console.log('   ✅ Успешный вход!');
      console.log('   👤 Пользователь:', loginData.user.name, `(${loginData.user.email})`);
      console.log('   🔑 Роль:', loginData.user.role);
    } else {
      console.log('   ❌ Ошибка входа:', loginData.error || 'Неизвестная ошибка');
    }
  } catch (error) {
    console.error('   ❌ Ошибка запроса входа:', error.message);
  }

  // 3. Проверяем сессию после входа
  console.log('\n3. Проверяем сессию после входа...');
  try {
    const sessionResponse = await fetch(`${API_BASE}/api/auth/session`);
    const sessionData = await sessionResponse.json();
    console.log('   Сессия после входа:', sessionData);
  } catch (error) {
    console.error('   ❌ Ошибка получения сессии:', error.message);
  }

  console.log('\n✅ Тест завершен!');
}

// Запускаем тест
testAuth().catch(console.error);
