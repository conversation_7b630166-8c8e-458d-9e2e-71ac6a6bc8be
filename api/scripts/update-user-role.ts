import { $prisma } from '../db'
import { auth } from '../auth'

async function createTestAdmin() {
  try {
    // Удаляем существующего пользователя если есть
    await $prisma.user.deleteMany({
      where: { email: '<EMAIL>' }
    })

    console.log('🔐 Создаем тестового администратора...')

    // Создаем нового пользователя через better-auth API
    const result = await auth.api.createUser({
      body: {
        email: '<EMAIL>',
        name: 'Тестовый Администратор',
        password: 'test123456',
        role: 'ADMIN'
      }
    })

    if (result.user) {
      console.log('✅ Тестовый администратор создан:')
      console.log('Email:', result.user.email)
      console.log('Роль:', result.user.role)
      console.log('ID:', result.user.id)
      console.log('Пароль: test123456')
    } else {
      console.log('❌ Ошибка создания пользователя:', result)
    }

  } catch (error) {
    console.error('❌ Ошибка:', error)
  } finally {
    await $prisma.$disconnect()
  }
}

createTestAdmin()
