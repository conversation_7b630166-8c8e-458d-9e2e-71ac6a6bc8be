import { getSystemDB } from '../db';

async function createTestTemplates() {
  const db = getSystemDB();
  
  console.log('Создание тестовых шаблонов атрибутов...');
  
  try {
    // Создаем группы атрибутов
    const dimensionsGroup = await db.attributeGroup.create({
      data: {
        name: 'Размеры',
        description: 'Размерные характеристики деталей'
      }
    });
    
    const materialGroup = await db.attributeGroup.create({
      data: {
        name: 'Материалы',
        description: 'Материалы и покрытия'
      }
    });
    
    const technicalGroup = await db.attributeGroup.create({
      data: {
        name: 'Технические характеристики',
        description: 'Технические параметры и характеристики'
      }
    });
    
    console.log('Группы созданы:', { dimensionsGroup: dimensionsGroup.id, materialGroup: materialGroup.id, technicalGroup: technicalGroup.id });
    
    // Создаем шаблоны атрибутов
    const templates = [
      // Размеры
      {
        name: 'inner_diameter',
        title: 'Внутренний диаметр',
        description: 'Внутренний диаметр детали в миллиметрах',
        dataType: 'NUMBER',
        unit: 'MM',
        groupId: dimensionsGroup.id,
        minValue: 0,
        maxValue: 1000
      },
      {
        name: 'outer_diameter',
        title: 'Наружный диаметр',
        description: 'Наружный диаметр детали в миллиметрах',
        dataType: 'NUMBER',
        unit: 'MM',
        groupId: dimensionsGroup.id,
        minValue: 0,
        maxValue: 1000
      },
      {
        name: 'length',
        title: 'Длина',
        description: 'Длина детали в миллиметрах',
        dataType: 'NUMBER',
        unit: 'MM',
        groupId: dimensionsGroup.id,
        minValue: 0,
        maxValue: 2000
      },
      {
        name: 'width',
        title: 'Ширина',
        description: 'Ширина детали в миллиметрах',
        dataType: 'NUMBER',
        unit: 'MM',
        groupId: dimensionsGroup.id,
        minValue: 0,
        maxValue: 2000
      },
      {
        name: 'height',
        title: 'Высота',
        description: 'Высота детали в миллиметрах',
        dataType: 'NUMBER',
        unit: 'MM',
        groupId: dimensionsGroup.id,
        minValue: 0,
        maxValue: 2000
      },
      {
        name: 'weight',
        title: 'Вес',
        description: 'Вес детали в граммах',
        dataType: 'NUMBER',
        unit: 'G',
        groupId: dimensionsGroup.id,
        minValue: 0,
        maxValue: 50000
      },
      
      // Материалы
      {
        name: 'material',
        title: 'Материал',
        description: 'Основной материал изготовления',
        dataType: 'STRING',
        groupId: materialGroup.id,
        allowedValues: ['steel', 'aluminum', 'plastic', 'rubber', 'bronze', 'brass', 'stainless_steel', 'carbon_steel']
      },
      {
        name: 'coating',
        title: 'Покрытие',
        description: 'Тип покрытия или обработки поверхности',
        dataType: 'STRING',
        groupId: materialGroup.id,
        allowedValues: ['zinc', 'chrome', 'nickel', 'anodized', 'painted', 'galvanized', 'none']
      },
      {
        name: 'hardness',
        title: 'Твердость',
        description: 'Твердость материала по шкале HRC',
        dataType: 'NUMBER',
        unit: 'PERCENT',
        groupId: materialGroup.id,
        minValue: 0,
        maxValue: 100
      },
      
      // Технические характеристики
      {
        name: 'max_pressure',
        title: 'Максимальное давление',
        description: 'Максимальное рабочее давление',
        dataType: 'NUMBER',
        unit: 'BAR',
        groupId: technicalGroup.id,
        minValue: 0,
        maxValue: 1000
      },
      {
        name: 'max_temperature',
        title: 'Максимальная температура',
        description: 'Максимальная рабочая температура',
        dataType: 'NUMBER',
        unit: 'C',
        groupId: technicalGroup.id,
        minValue: -50,
        maxValue: 500
      },
      {
        name: 'thread_type',
        title: 'Тип резьбы',
        description: 'Тип и размер резьбы',
        dataType: 'STRING',
        groupId: technicalGroup.id,
        allowedValues: ['M6', 'M8', 'M10', 'M12', 'M14', 'M16', 'M18', 'M20', 'M22', 'M24', 'M27', 'M30']
      },
      {
        name: 'voltage',
        title: 'Напряжение',
        description: 'Рабочее напряжение в вольтах',
        dataType: 'NUMBER',
        unit: 'PERCENT', // Используем PERCENT как заглушку для вольт
        groupId: technicalGroup.id,
        minValue: 0,
        maxValue: 1000
      },
      {
        name: 'power',
        title: 'Мощность',
        description: 'Номинальная мощность',
        dataType: 'NUMBER',
        unit: 'KW',
        groupId: technicalGroup.id,
        minValue: 0,
        maxValue: 1000
      },
      {
        name: 'is_waterproof',
        title: 'Водонепроницаемость',
        description: 'Является ли деталь водонепроницаемой',
        dataType: 'BOOLEAN',
        groupId: technicalGroup.id
      }
    ];
    
    // Создаем шаблоны
    for (const template of templates) {
      const created = await db.attributeTemplate.create({
        data: template as any
      });
      console.log(`Создан шаблон: ${created.title} (${created.name})`);
    }
    
    console.log(`\n✅ Успешно создано ${templates.length} шаблонов атрибутов в ${3} группах!`);
    
    // Выводим статистику
    const totalTemplates = await db.attributeTemplate.count();
    const totalGroups = await db.attributeGroup.count();
    
    console.log(`\n📊 Общая статистика:`);
    console.log(`   Всего групп: ${totalGroups}`);
    console.log(`   Всего шаблонов: ${totalTemplates}`);
    
  } catch (error) {
    console.error('Ошибка при создании тестовых шаблонов:', error);
  } finally {
    await db.$disconnect();
  }
}

// Запускаем скрипт
createTestTemplates();
