import { getSystemDB } from '../db';

async function seedTestData() {
  const db = getSystemDB();

  try {
    console.log('🌱 Создание тестовых данных...');

    // Создаем тестовые бренды
    const brands = await Promise.all([
      db.brand.upsert({
        where: { slug: 'caterpillar' },
        update: {},
        create: {
          name: 'Caterpillar',
          slug: 'caterpillar',
          country: 'США',
          isOem: true
        }
      }),
      db.brand.upsert({
        where: { slug: 'komatsu' },
        update: {},
        create: {
          name: '<PERSON><PERSON><PERSON>',
          slug: 'komatsu',
          country: 'Япония',
          isOem: true
        }
      }),
      db.brand.upsert({
        where: { slug: 'skf' },
        update: {},
        create: {
          name: 'SKF',
          slug: 'skf',
          country: 'Швеция',
          isOem: false
        }
      }),
      db.brand.upsert({
        where: { slug: 'nok' },
        update: {},
        create: {
          name: 'NOK',
          slug: 'nok',
          country: 'Япония',
          isOem: false
        }
      })
    ]);

    console.log(`✅ Создано ${brands.length} брендов`);

    // Создаем тестовые категории
    const categories = await Promise.all([
      db.partCategory.upsert({
        where: { slug: 'engine' },
        update: {},
        create: {
          name: 'Двигатель',
          slug: 'engine',
          description: 'Запчасти для двигателя',
          level: 0,
          path: '/engine'
        }
      }),
      db.partCategory.upsert({
        where: { slug: 'hydraulics' },
        update: {},
        create: {
          name: 'Гидравлика',
          slug: 'hydraulics',
          description: 'Гидравлические компоненты',
          level: 0,
          path: '/hydraulics'
        }
      }),
      db.partCategory.upsert({
        where: { slug: 'seals' },
        update: {},
        create: {
          name: 'Сальники и уплотнения',
          slug: 'seals',
          description: 'Уплотнительные элементы',
          level: 0,
          path: '/seals'
        }
      }),
      db.partCategory.upsert({
        where: { slug: 'filters' },
        update: {},
        create: {
          name: 'Фильтры',
          slug: 'filters',
          description: 'Фильтрующие элементы',
          level: 0,
          path: '/filters'
        }
      })
    ]);

    console.log(`✅ Создано ${categories.length} категорий`);

    console.log('🎉 Тестовые данные успешно созданы!');
    console.log('\nСозданные бренды:');
    brands.forEach(brand => console.log(`  - ${brand.name} (${brand.slug})`));
    
    console.log('\nСозданные категории:');
    categories.forEach(category => console.log(`  - ${category.name} (${category.slug})`));

  } catch (error) {
    console.error('❌ Ошибка при создании тестовых данных:', error);
    throw error;
  } finally {
    await db.$disconnect();
  }
}

// Запускаем скрипт
seedTestData()
  .then(() => {
    console.log('\n✨ Готово!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
