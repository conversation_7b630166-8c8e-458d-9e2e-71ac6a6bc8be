import { getSystemDB } from '../db';

async function testNewArchitecture() {
  const db = getSystemDB();
  
  console.log('🧪 Тестирование новой архитектуры атрибутов...\n');
  
  try {
    // 1. Тестируем получение шаблонов
    console.log('1️⃣ Тестируем получение шаблонов атрибутов...');
    const templates = await db.attributeTemplate.findMany({
      include: {
        group: true,
        _count: {
          select: {
            partAttributes: true,
            catalogItemAttributes: true,
            equipmentAttributes: true
          }
        }
      },
      take: 5
    });
    
    console.log(`   ✅ Найдено ${templates.length} шаблонов`);
    templates.forEach(template => {
      console.log(`   📋 ${template.title} (${template.name}) - Группа: ${template.group?.name || 'Без группы'}`);
    });
    
    // 2. Тестируем создание запчасти
    console.log('\n2️⃣ Тестируем создание запчасти...');
    
    // Сначала найдем или создадим категорию
    let category = await db.partCategory.findFirst({
      where: { name: 'Тестовая категория' }
    });
    
    if (!category) {
      category = await db.partCategory.create({
        data: {
          name: 'Тестовая категория',
          slug: 'test-category',
          level: 0,
          path: '/test'
        }
      });
    }
    
    // Создаем тестовую запчасть
    const testPart = await db.part.create({
      data: {
        name: 'Тестовая запчасть для новой архитектуры',
        partCategoryId: category.id,
        level: 0,
        path: '/test/part'
      }
    });
    
    console.log(`   ✅ Создана запчасть: ${testPart.name} (ID: ${testPart.id})`);
    
    // 3. Тестируем создание атрибутов запчасти
    console.log('\n3️⃣ Тестируем создание атрибутов запчасти...');
    
    // Найдем несколько шаблонов для тестирования
    const innerDiameterTemplate = await db.attributeTemplate.findFirst({
      where: { name: 'inner_diameter' }
    });
    
    const materialTemplate = await db.attributeTemplate.findFirst({
      where: { name: 'material' }
    });
    
    const waterproofTemplate = await db.attributeTemplate.findFirst({
      where: { name: 'is_waterproof' }
    });
    
    if (innerDiameterTemplate) {
      const attr1 = await db.partAttribute.create({
        data: {
          partId: testPart.id,
          templateId: innerDiameterTemplate.id,
          value: '25.5'
        }
      });
      console.log(`   ✅ Создан атрибут: ${innerDiameterTemplate.title} = ${attr1.value}`);
    }
    
    if (materialTemplate) {
      const attr2 = await db.partAttribute.create({
        data: {
          partId: testPart.id,
          templateId: materialTemplate.id,
          value: 'steel'
        }
      });
      console.log(`   ✅ Создан атрибут: ${materialTemplate.title} = ${attr2.value}`);
    }
    
    if (waterproofTemplate) {
      const attr3 = await db.partAttribute.create({
        data: {
          partId: testPart.id,
          templateId: waterproofTemplate.id,
          value: 'true'
        }
      });
      console.log(`   ✅ Создан атрибут: ${waterproofTemplate.title} = ${attr3.value}`);
    }
    
    // 4. Тестируем получение атрибутов запчасти с информацией о шаблонах
    console.log('\n4️⃣ Тестируем получение атрибутов запчасти...');
    
    const partWithAttributes = await db.part.findUnique({
      where: { id: testPart.id },
      include: {
        attributes: {
          include: {
            template: {
              include: {
                group: true
              }
            }
          }
        }
      }
    });
    
    if (partWithAttributes) {
      console.log(`   ✅ Запчасть "${partWithAttributes.name}" имеет ${partWithAttributes.attributes.length} атрибутов:`);
      partWithAttributes.attributes.forEach(attr => {
        const template = attr.template;
        console.log(`   📊 ${template.title}: ${attr.value} ${template.unit ? `(${template.unit})` : ''}`);
        console.log(`       Группа: ${template.group?.name || 'Без группы'}`);
        console.log(`       Тип: ${template.dataType}`);
      });
    }
    
    // 5. Тестируем поиск по атрибутам
    console.log('\n5️⃣ Тестируем поиск запчастей по атрибутам...');
    
    if (materialTemplate) {
      const partsWithSteelMaterial = await db.part.findMany({
        where: {
          attributes: {
            some: {
              templateId: materialTemplate.id,
              value: 'steel'
            }
          }
        },
        include: {
          attributes: {
            include: {
              template: true
            }
          }
        }
      });
      
      console.log(`   ✅ Найдено ${partsWithSteelMaterial.length} запчастей из стали`);
    }
    
    // 6. Тестируем статистику использования шаблонов
    console.log('\n6️⃣ Тестируем статистику использования шаблонов...');
    
    const templatesWithUsage = await db.attributeTemplate.findMany({
      include: {
        _count: {
          select: {
            partAttributes: true,
            catalogItemAttributes: true,
            equipmentAttributes: true
          }
        }
      },
      take: 5
    });
    
    console.log('   📈 Статистика использования шаблонов:');
    templatesWithUsage.forEach(template => {
      const totalUsage = template._count.partAttributes + 
                        template._count.catalogItemAttributes + 
                        template._count.equipmentAttributes;
      console.log(`   📋 ${template.title}: ${totalUsage} использований`);
      console.log(`       Запчасти: ${template._count.partAttributes}, Каталог: ${template._count.catalogItemAttributes}, Техника: ${template._count.equipmentAttributes}`);
    });
    
    console.log('\n✅ Все тесты пройдены успешно! Новая архитектура работает корректно.');
    
    // Очистка тестовых данных
    console.log('\n🧹 Очистка тестовых данных...');
    await db.partAttribute.deleteMany({
      where: { partId: testPart.id }
    });
    await db.part.delete({
      where: { id: testPart.id }
    });
    console.log('   ✅ Тестовые данные очищены');
    
  } catch (error) {
    console.error('❌ Ошибка при тестировании:', error);
  } finally {
    await db.$disconnect();
  }
}

// Запускаем тест
testNewArchitecture();
