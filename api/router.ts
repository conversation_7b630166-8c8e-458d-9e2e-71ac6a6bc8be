import { router } from './trpc';
// import { partsRouter } from './routers/parts'; // Временно закомментировано из-за отсутствующих типов
import { schemaEditorRouter } from './routers/schema-editor';
import { adminRouter } from './routers/admin';
import { uploadRouter } from './routers/upload';
import { partAttributesRouter } from './routers/part-attributes';
import { attributeTemplatesRouter } from './routers/attribute-templates';
import { createRouter as createCRUDRouter } from './generated/trpc/routers';

// Создаем автоматически сгенерированные CRUD роутеры
const crudRouter = createCRUDRouter();

export const appRouter = router({
  // Кастомные роутеры
  // parts: partsRouter, // Временно закомментировано
  schemaEditor: schemaEditorRouter,
  admin: adminRouter,
  upload: uploadRouter,
  partAttributes: partAttributesRouter,
  attributeTemplates: attributeTemplatesRouter,

  // Автоматически сгенерированные CRUD роутеры для всех моделей
  crud: crudRouter,
});

export type AppRouter = typeof appRouter;
