# Предложения по улучшению архитектуры атрибутов

## Проблемы текущей архитектуры

1. **Сложность полиморфизма** - ZenStack `@@delegate` создает проблемы с генерацией схем
2. **Проблемы с запросами** - сложно получить атрибуты в одном запросе с основной сущностью
3. **Избыточность** - дублирование данных между базовой и наследуемой моделями
4. **Сложность разработки** - требует глубокого понимания делегирования

## Вариант 1: Упрощенная модель с JSON

```prisma
// Простая модель атрибута без полиморфизма
model Attribute {
  id          Int               @id @default(autoincrement())
  name        String            // Системное имя
  title       String?           // Отображаемое название
  description String?
  dataType    AttributeDataType @default(STRING)
  unit        AttributeUnit?
  value       String            // Значение как строка
  
  // Метаданные
  isRequired  Boolean           @default(false)
  groupId     Int?
  group       AttributeGroup?   @relation(fields: [groupId], references: [id])
  
  // Полиморфная связь через enum
  ownerType   AttributeOwnerType // PART, CATALOG_ITEM, EQUIPMENT_MODEL
  ownerId     String            // ID владельца (может быть Int или String)
  
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  
  @@index([ownerType, ownerId])
}

// Добавляем JSON поля для гибких атрибутов
model Part {
  // ... существующие поля
  
  // Структурированные атрибуты через отдельную таблицу
  attributes     Attribute[]     @relation("PartAttributes")
  
  // Быстрые атрибуты в JSON для поиска (денормализация)
  attributesJson Json?           // { "diameter": 20, "material": "steel" }
  
  // Поисковые поля (материализованные из атрибутов)
  searchableAttributes String[] // ["diameter:20", "material:steel"]
}
```

**Плюсы:**
- Простота реализации
- Нет проблем с ZenStack
- Быстрый поиск через JSON и массивы
- Легко добавлять новые типы владельцев

**Минусы:**
- Менее строгая типизация
- Нужна логика синхронизации JSON с таблицей

## Вариант 2: Отдельные таблицы для каждого типа

```prisma
// Базовая модель атрибута (шаблон)
model AttributeTemplate {
  id          Int               @id @default(autoincrement())
  name        String            @unique
  title       String
  description String?
  dataType    AttributeDataType
  unit        AttributeUnit?
  isRequired  Boolean           @default(false)
  groupId     Int?
  group       AttributeGroup?   @relation(fields: [groupId], references: [id])
}

// Конкретные значения атрибутов для запчастей
model PartAttribute {
  id         Int               @id @default(autoincrement())
  partId     Int
  part       Part              @relation(fields: [partId], references: [id], onDelete: Cascade)
  
  // Копируем нужные поля из шаблона или ссылаемся на него
  templateId Int?
  template   AttributeTemplate? @relation(fields: [templateId], references: [id])
  
  // Или дублируем поля для производительности
  name       String
  title      String
  dataType   AttributeDataType
  unit       AttributeUnit?
  value      String
  
  @@unique([partId, name])
  @@index([partId])
}

// Аналогично для других сущностей
model CatalogItemAttribute {
  id            Int         @id @default(autoincrement())
  catalogItemId Int
  catalogItem   CatalogItem @relation(fields: [catalogItemId], references: [id], onDelete: Cascade)
  
  name          String
  title         String
  dataType      AttributeDataType
  unit          AttributeUnit?
  value         String
  
  @@unique([catalogItemId, name])
}
```

**Плюсы:**
- Простые запросы с include
- Хорошая производительность
- Нет проблем с ZenStack
- Четкая типизация

**Минусы:**
- Дублирование кода для каждого типа
- Больше таблиц

## Вариант 3: Гибридный подход

```prisma
// Шаблоны атрибутов (справочник)
model AttributeDefinition {
  id          Int               @id @default(autoincrement())
  name        String            @unique
  title       String
  description String?
  dataType    AttributeDataType
  unit        AttributeUnit?
  isRequired  Boolean           @default(false)
  
  // Валидация
  minValue    Float?
  maxValue    Float?
  allowedValues String[]        // JSON массив для enum значений
  
  groupId     Int?
  group       AttributeGroup?   @relation(fields: [groupId], references: [id])
}

// Универсальная таблица значений
model AttributeValue {
  id           Int                 @id @default(autoincrement())
  
  definitionId Int
  definition   AttributeDefinition @relation(fields: [definitionId], references: [id])
  
  // Полиморфная связь
  ownerType    AttributeOwnerType  // PART, CATALOG_ITEM, etc.
  ownerId      String              // Универсальный ID как строка
  
  // Типизированные значения для быстрого поиска
  valueString  String?
  valueNumber  Float?
  valueBoolean Boolean?
  valueDate    DateTime?
  
  createdAt    DateTime            @default(now())
  updatedAt    DateTime            @updatedAt
  
  @@unique([definitionId, ownerType, ownerId])
  @@index([ownerType, ownerId])
  @@index([definitionId, valueNumber]) // Для числовых поисков
  @@index([definitionId, valueString]) // Для строковых поисков
}

// В основных моделях добавляем computed поля
model Part {
  // ... существующие поля
  
  // Виртуальное поле для получения атрибутов
  // attributes будет вычисляться через запрос к AttributeValue
}
```

**Плюсы:**
- Максимальная гибкость
- Единая система валидации
- Эффективные поиски по типизированным полям
- Легко добавлять новые типы сущностей

**Минусы:**
- Сложность запросов
- Нужны дополнительные методы для работы с атрибутами

## Рекомендация

Я бы рекомендовал **Вариант 2** (отдельные таблицы) как оптимальный баланс между простотой и функциональностью:

1. **Простота разработки** - обычные Prisma запросы
2. **Производительность** - прямые связи без полиморфизма
3. **Типобезопасность** - четкие TypeScript типы
4. **Совместимость с ZenStack** - никаких проблем с генерацией

Хотите, чтобы я показал, как это будет выглядеть в коде?
