import "catalog_schema"

// =====================================================
// ИНТЕРАКТИВНЫЕ СХЕМЫ
// =====================================================

// СХЕМЫ АГРЕГАТОВ
model AggregateSchema {
    id          String             @id @default(uuid()) // Уникальный идентификатор схемы
    name        String             @length(1, 200) // Название схемы
    description String?            @length(1, 1000) // Описание схемы

    part        Part?              @relation(fields: [partId], references: [id])
    partId      Int?

    imageUrl    String?            @length(1, 500) // URL изображения схемы
    imageWidth  Int?       // Ширина изображения
    imageHeight Int?       // Высота изображения

    // Контент SVG (для интерактивных схем)
    svgContent  String?    // Содержимое SVG-схемы

    isActive    Boolean            @default(true) // Активна ли схема
    sortOrder   Int                @default(0) // Порядок сортировки

    positions   SchemaPosition[] // Позиции деталей на схеме
    annotations SchemaAnnotation[] // Аннотации на схеме
    // layers      SchemaLayer[]      // Слои на схеме // REMOVED

    createdAt   DateTime           @default(now()) // Дата создания
    updatedAt   DateTime           @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'ADMIN')
    @@index([isActive])
    @@map("aggregate_schema")
}

// ПОЗИЦИИ ДЕТАЛЕЙ НА СХЕМАХ
model SchemaPosition {
    id                String          @id @default(uuid()) // Уникальный идентификатор позиции

    schema            AggregateSchema @relation(fields: [schemaId], references: [id]) // Схема
    schemaId          String

    part              Part            @relation(fields: [partId], references: [id]) // Запчасть
    partId            Int

    positionNumber    String          @length(1, 10) // Номер позиции на схеме (1, 2, 3, ...)
    x                 Float      // Координата X
    y                 Float      // Координата Y
    width             Float?     // Ширина позиции
    height            Float?     // Высота позиции

    shape             String          @default("circle") // Форма позиции
    color             String?         @length(1, 20) // Цвет позиции
    label             String?         @length(1, 100) // Метка позиции

    quantity          Int             @default(1) @gte(1) // Количество деталей
    isRequired        Boolean         @default(true) // Обязательная ли деталь
    isHighlighted     Boolean         @default(false) // Выделена ли позиция
    installationOrder Int? // Порядок установки
    notes             String?         @length(1, 500) // Примечания

    isVisible         Boolean         @default(true) // Видима ли позиция
    sortOrder         Int             @default(0) // Порядок сортировки

    // layer       SchemaLayer? @relation(fields: [layerId], references: [id]) // REMOVED
    // layerId     String? // REMOVED

    createdAt         DateTime        @default(now()) // Дата создания
    updatedAt         DateTime        @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
    @@unique([schemaId, partId])
    @@unique([schemaId, positionNumber]) // Уникальный номер позиции в рамках схемы
    @@index([schemaId])
    @@index([partId])
    @@index([positionNumber])
    @@map("schema_position")
}

// АННОТАЦИИ НА СХЕМАХ
model SchemaAnnotation {
    id             String          @id @default(uuid()) // Уникальный идентификатор аннотации

    schema         AggregateSchema @relation(fields: [schemaId], references: [id]) // Схема
    schemaId       String

    x              Float      // Координата X
    y              Float      // Координата Y

    // Дополнительные размеры (для прямоугольников, выделений и т.д.)
    width          Float?     // Ширина области
    height         Float?     // Высота области

    text           String          @length(1, 500) // Текст аннотации
    annotationType String          @default("note") // Тип аннотации

    color          String?         @length(1, 20) // Цвет аннотации
    fontSize       Int?            @default(12) // Размер шрифта

    strokeWidth    Int?            @default(1) // Толщина обводки
    opacity        Float?          @default(1.0) // Прозрачность

    isVisible      Boolean         @default(true) // Видима ли аннотация
    sortOrder      Int             @default(0) // Порядок отображения

    // layer       SchemaLayer? @relation(fields: [layerId], references: [id]) // REMOVED
    // layerId     String? // REMOVED

    createdAt      DateTime        @default(now()) // Дата создания
    updatedAt      DateTime        @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
    @@index([schemaId])
    @@map("schema_annotation")
}