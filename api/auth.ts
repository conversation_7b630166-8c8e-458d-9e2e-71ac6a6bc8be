import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { $prisma } from "./db";
import { anonymous, phoneNumber, username, admin } from "better-auth/plugins";

export const auth = betterAuth({
  // Используем переменные окружения для URL
  baseURL: process.env.BETTER_AUTH_URL || "http://localhost:3000",

  // Расширенный список доверенных источников
  trustedOrigins: [
    "http://localhost:4321", // Astro dev server
    "http://localhost:4322",
    "http://localhost:4323",
    "http://localhost:4444",
    "http://localhost:3000", // API server
    // В продакшене добавить реальные домены
    ...(process.env.NODE_ENV === "production" ? [
      process.env.FRONTEND_URL,
      process.env.PRODUCTION_URL
    ].filter((url): url is string => <PERSON><PERSON><PERSON>(url)) : [])
  ],

  database: prismaAdapter($prisma, {
    provider: "postgresql",
  }),

  emailAndPassword: {
    enabled: true,
    requireEmailVerification: false, // Пока отключаем для разработки
    minPasswordLength: 8,
    maxPasswordLength: 128,
    autoSignIn: true, // Автоматический вход после регистрации
  },

  emailVerification: {
    sendOnSignUp: false, // Не отправляем автоматически при регистрации
    sendOnSignIn: false, // Не отправляем при входе
    autoSignInAfterVerification: true,
    expiresIn: 60 * 60 * 24, // 24 часа
    sendVerificationEmail: async ({ user, url, token }) => {
      // Пока что логируем в консоль для разработки
      // В продакшене здесь должна быть интеграция с email сервисом (SendGrid, Mailgun, etc.)
      console.log('📧 ОТПРАВКА EMAIL ВЕРИФИКАЦИИ:')
      console.log('👤 Пользователь:', user.email)
      console.log('🔗 URL верификации:', url)
      console.log('🎫 Токен:', token)

      // TODO: Интегрировать с реальным email сервисом
      // Пример для SendGrid:
      // await sendgrid.send({
      //   to: user.email,
      //   from: '<EMAIL>',
      //   subject: 'Подтвердите ваш email',
      //   html: `<p>Нажмите <a href="${url}">здесь</a> для подтверждения email</p>`
      // })

      console.log('✅ Email верификация "отправлена" (в режиме разработки)')
    }
  },

  user: {
    additionalFields: {
      role: {
        type: "string",
        defaultValue: "USER",
        required: false,
        returned: true, // Явно указываем, что поле должно возвращаться
      }
    }
  },

  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 дней
    updateAge: 60 * 60 * 24, // Обновлять каждый день
    cookieCache: {
      enabled: true,
      maxAge: 60 * 5, // 5 минут кеш
    },
  },

  // Конфигурация аккаунтов
  account: {
    accountLinking: {
      enabled: true,
      trustedProviders: ["email-password"], // Пока только email/password
    }
  },

  plugins: [
    phoneNumber({
      sendOTP: ({ phoneNumber, code }) => {
        console.log("📱 ОТПРАВКА SMS OTP:");
        console.log("📞 Номер телефона:", phoneNumber);
        console.log("🔢 Код:", code);
        // TODO: Интегрировать с SMS сервисом
      },
    }),
    anonymous(),
    username(),
    admin({
      adminRoles: ["ADMIN"], // Только ADMIN роль считается админской
      defaultRole: "USER", // Роль по умолчанию
      defaultBanReason: "Нарушение правил использования",
      defaultBanExpiresIn: 60 * 60 * 24 * 30, // 30 дней по умолчанию
      impersonationSessionDuration: 60 * 60, // 1 час для impersonation
      bannedUserMessage: "Ваш аккаунт заблокирован. Обратитесь в службу поддержки для получения дополнительной информации."
    }),
  ],
  // Настройки безопасности и производительности
  rateLimit: {
    enabled: process.env.NODE_ENV === "production", // Включаем в продакшене
    window: 10, // 10 секунд
    max: 100, // 100 запросов
    customRules: {
      "/sign-in/email": { window: 60, max: 5 }, // Ограничиваем попытки входа
      "/sign-up/email": { window: 60, max: 3 }, // Ограничиваем регистрации
    }
  },

  advanced: {
    // IP адрес для rate limiting
    ipAddress: {
      ipAddressHeaders: ["x-forwarded-for", "x-real-ip"],
      disableIpTracking: false,
    },

    // Настройки cookies
    crossSubDomainCookies: {
      enabled: false, // Отключаем для localhost
    },
    defaultCookieAttributes: {
      sameSite: "lax", // Более безопасно для localhost
      secure: process.env.NODE_ENV === "production", // Secure только в продакшене
      httpOnly: true, // Защита от XSS
      path: "/", // Доступно для всего сайта
    },

    // Настройки базы данных
    database: {
      generateId: () => {
        // Генерируем уникальный ID
        return crypto.randomUUID()
      }
    },

    // Отключаем CSRF проверку только для разработки
    disableCSRFCheck: process.env.NODE_ENV === "development",
  },

  // Логирование
  logger: {
    level: process.env.NODE_ENV === "production" ? "error" : "info",
    disabled: false,
  },
});
