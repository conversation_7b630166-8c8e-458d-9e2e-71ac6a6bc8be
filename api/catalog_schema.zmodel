import "aggregate_schemas_schema"

abstract model TimeStamped {
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt
}


// =====================================================
// ОСНОВНАЯ СХЕМА ДАННЫХ КАТАЛОГА
// =====================================================

// Перечисление для типов данных атрибута.
// Позволяет системе понимать, как обрабатывать и валидировать поле `value` в модели Attribute.
enum AttributeDataType {
    STRING  // Строковые значения (например, "NBR", "ABC123")
    NUMBER  // Числовые значения (целые или дробные, например, "10.000", "100")
    BOOLEAN // Логические значения (true/false)
    DATE    // Значения даты/времени
    JSON    // Для комплексных, неструктурированных данных (использовать осторожно)
}

// Перечисление для единиц измерения атрибутов.
// Стандартизирует единицы измерения по всему каталогу для удобства фильтрации и сравнения.
enum AttributeUnit {
    // Длина
    MM      // Миллиметры
    INCH    // Дюймы
    FT      // Футы

    // Вес
    G       // Граммы
    KG      // Килограммы
    T       // Тонны
    LB      // Фунты

    // Объем
    ML      // Миллилитры
    L       // Литры
    GAL     // Галлоны

    // Время
    SEC     // Секунды
    MIN     // Минуты
    H       // Часы

    // Количество
    PCS     // Штуки
    SET     // Комплекты
    PAIR    // Пары

    // Давление
    BAR     // Бары
    PSI     // Фунты на квадратный дюйм

    // Мощность
    KW      // Киловатты
    HP      // Лошадиные силы

    // Крутящий момент
    NM      // Ньютон-метры
    RPM     // Обороты в минуту

    // Температура
    C       // Градусы Цельсия
    F       // Градусы Фаренгейта

    // Относительные единицы
    PERCENT // Проценты
}

// Перечисление для меток точности применимости.
// Определяет, насколько точно CatalogItem соответствует группе взаимозаменяемости Part.
enum ApplicabilityAccuracy {
    EXACT_MATCH          // Полное совпадение: деталь является прямым аналогом.
    MATCH_WITH_NOTES     // Совпадение с нюансами: аналог, но есть условия, описанные в поле `notes`.
    REQUIRES_MODIFICATION // Требуется доработка: деталь подходит после внесения изменений.
    PARTIAL_MATCH        // Частичное совпадение: подходит только часть комплекта или не все функции.
}


// =====================================================
// АРХИТЕКТУРА АТРИБУТОВ (Простая + Шаблоны)
// =====================================================

// Группа атрибутов для организации шаблонов
model AttributeGroup {
    id          Int                 @id @default(autoincrement())
    name        String              @unique
    description String?

    // Связь с шаблонами атрибутов
    templates   AttributeTemplate[]

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// СПРАВОЧНИК ШАБЛОНОВ АТРИБУТОВ - источник правды
// Определяет, какие атрибуты могут существовать в системе, их тип, единицы измерения и т.д.
// Атрибутыв моем проекте это как характеристики товара, только динамические. 
// Например: ширина, длина, высота, материал.
model AttributeTemplate {
    id          Int               @id @default(autoincrement())
    name        String            @unique // Системное имя (например, "inner_diameter", "material")
    title       String            // Отображаемое название ("Внутренний диаметр", "Материал")
    description String?

    dataType    AttributeDataType @default(STRING)
    unit        AttributeUnit?    // Единица измерения по умолчанию
    isRequired  Boolean           @default(false)

    // Валидация (опционально)
    minValue      Float?   // Для числовых значений
    maxValue      Float?   // Для числовых значений
    allowedValues String[] // Для enum значений, например ["steel", "aluminum", "plastic"]

    // Группировка
    group       AttributeGroup?   @relation(fields: [groupId], references: [id])
    groupId     Int?

    // Обратные связи с конкретными экземплярами атрибутов
    partAttributes        PartAttribute[]
    catalogItemAttributes CatalogItemAttribute[]
    equipmentAttributes   EquipmentModelAttribute[]

    createdAt   DateTime          @default(now())
    updatedAt   DateTime          @updatedAt

    @@index([groupId])
    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// ----------------------------------------------------
// КОНКРЕТНЫЕ ЭКЗЕМПЛЯРЫ АТРИБУТОВ
// ----------------------------------------------------

// Атрибут для группы взаимозаменяемости (Part)
// Атрибутыв моем проекте это как характеристики товара, только динамические. 
// Например: ширина, длина, высота, материал.
model PartAttribute {
    id         Int      @id @default(autoincrement())
    value      String   // Конкретное значение атрибута, например "15.5" или "Сталь"

    // Связь с группой
    part       Part     @relation(fields: [partId], references: [id], onDelete: Cascade)
    partId     Int

    // Связь с шаблоном. `onDelete: Restrict` предотвращает удаление шаблона, если он используется.
    template   AttributeTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)
    templateId Int

    @@unique([partId, templateId]) // У одной детали не может быть два одинаковых атрибута
    @@index([partId])
    @@index([templateId])
    @@index([value]) // Для быстрого поиска по значению

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// Атрибут для каталожной позиции (CatalogItem)
model CatalogItemAttribute {
    id         Int      @id @default(autoincrement())
    value      String

    // Связь с позицией каталога
    catalogItem   CatalogItem @relation(fields: [catalogItemId], references: [id], onDelete: Cascade)
    catalogItemId Int

    // Связь с шаблоном
    template      AttributeTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)
    templateId    Int

    @@unique([catalogItemId, templateId])
    @@index([catalogItemId])
    @@index([templateId])
    @@index([value])

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// Атрибут для модели техники (EquipmentModel)
model EquipmentModelAttribute {
    id         Int      @id @default(autoincrement())
    value      String

    // Связь с моделью техники
    equipmentModel   EquipmentModel @relation(fields: [equipmentModelId], references: [id], onDelete: Cascade)
    equipmentModelId String

    // Связь с шаблоном
    template         AttributeTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)
    templateId       Int

    @@unique([equipmentModelId, templateId])
    @@index([equipmentModelId])
    @@index([templateId])
    @@index([value])

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}


// Применимость Детали к Модели Техники
// Связывает группу взаимозаменяемости (Part) с конкретной моделью техники (EquipmentModel)
model EquipmentApplicability {
    id               Int              @id @default(autoincrement())

    // Связь с группой взаимозаменяемости
    part             Part             @relation(fields: [partId], references: [id], onDelete: Cascade)
    partId           Int

    // Связь с моделью техники
    equipmentModel   EquipmentModel   @relation(fields: [equipmentModelId], references: [id], onDelete: Cascade)
    equipmentModelId String

    // Метаданные о применимости
    notes            String?          // Заметки, например, "Только для моделей выпущенных после 2021 года"

    @@unique([partId, equipmentModelId])
    @@index([equipmentModelId])

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}


// Группа взаимозаменяемости (деталь).
// Представляет собой абстрактную "деталь", которая объединяет несколько конкретных артикулов (CatalogItem) от разных производителей,
// являющихся полными или частичными аналогами друг друга.
model Part extends TimeStamped {
    id               Int                 @id @default(autoincrement()) // Уникальный идентификатор группы

    name             String?       // Наименование группы (например, "Сальник коленвала передний")

    // Иерархия для представления сложных узлов и агрегатов (например, двигатель -> блок цилиндров -> поршень)
    parent           Part?               @relation("PartHierarchy", fields: [parentId], references: [id]) // Родительская группа (узел)
    parentId         Int?          // Внешний ключ для связи с родительской группой
    children         Part[]              @relation("PartHierarchy") // Список дочерних групп (подузлов)
    level            Int                 @default(0) // Уровень вложенности в иерархии: 0 - корень, 1 - подузел и т.д.
    path             String        // Материализованный путь для быстрого выбора поддеревьев

    // ПРОСТАЯ СВЯЗЬ С АТРИБУТАМИ - магия Prisma снова с нами!
    attributes       PartAttribute[]

    // Связь с конкретными артикулами через промежуточную таблицу PartApplicability.
    // Позволяет хранить метаданные о связи (точность, примечания).
    applicabilities  PartApplicability[]
    equipmentApplicabilities EquipmentApplicability[]

    // Связь со схемами агрегатов
    aggregateSchemas AggregateSchema[]
    schemaPositions  SchemaPosition[]

    @@index([parentId])
    @@index([path])

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')

    partCategory PartCategory @relation(fields: [partCategoryId], references: [id])
    partCategoryId Int
}

model PartApplicability {
    id            Int                   @id @default(autoincrement()) // Уникальный идентификатор записи о применимости

    part          Part                  @relation(fields: [partId], references: [id]) // Ссылка на группу взаимозаменяемости
    partId        Int                   // Внешний ключ для Part

    catalogItem   CatalogItem           @relation(fields: [catalogItemId], references: [id]) // Ссылка на конкретный артикул
    catalogItemId Int                   // Внешний ключ для CatalogItem

    // Метаданные о связи
    accuracy      ApplicabilityAccuracy @default(EXACT_MATCH) // Метка точности из перечисления ApplicabilityAccuracy
    notes         String?               // Текстовое поле для описания нюансов и условий применимости

    // Уникальный ключ, чтобы один и тот же артикул не мог быть добавлен в одну группу дважды.
    @@unique([partId, catalogItemId])
    // Индекс для быстрого поиска всех групп, в которые входит один артикул.
    @@index([catalogItemId])

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// Каталожная позиция (артикул).
// Конкретная деталь от конкретного производителя с уникальным номером (SKU).
model CatalogItem {
    id              Int                    @id @default(autoincrement()) // Уникальный идентификатор каталожной позиции

    sku             String                 @trim @upper @length(1, 64) // Артикул (SKU) - уникальный номер детали у производителя.
    source          String? // Источник информации об этой записи (например, "Официальный каталог", "Данные клиента")

    description     String? // Описание детали от производителя

    brand           Brand                  @relation(fields: [brandId], references: [id]) // Производитель (бренд) детали
    brandId         Int     // Внешний ключ для связи с Brand

    isPublic        Boolean                @default(true) // Является ли запись публичной и видимой для всех пользователей

    // ПРОСТАЯ СВЯЗЬ С АТРИБУТАМИ
    attributes      CatalogItemAttribute[]

    // Связь с группами взаимозаменяемости через промежуточную таблицу PartApplicability.
    applicabilities PartApplicability[]

    @@unique([sku, brandId])
    @@index([brandId])

    // @@allow('read', true)
    @@allow('read,create,update,delete', auth() != null && auth().role == 'ADMIN')
}

/// Иерархическая категория запчастей  
/// Примеры: «Двигатель», «Система смазки», «Фильтры топлива» …
model PartCategory {
    id          Int                @id @default(autoincrement())
    name        String             @length(1, 150)                 // Отображаемое название
    slug        String             @unique @regex("^[a-z0-9-]+$")  // Для SEO-/URL
    description String?            @length(1, 1000)

    // Иерархия «родитель ↔ дети»
    parent      PartCategory?      @relation("CategoryHierarchy", fields: [parentId], references: [id])
    parentId    Int?
    children    PartCategory[]     @relation("CategoryHierarchy")

    level       Int                @default(0)       // Глубина в дереве
    path        String         // Материализованный путь: '01/02/15'
    icon        String?        // Опциональная пиктограмма

    // Связь с группами взаимозаменяемости (Part)
    parts       Part[]

    createdAt   DateTime           @default(now())
    updatedAt   DateTime           @updatedAt

    @@index([parentId])
    @@index([slug])
    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// Производитель (Бренд).
// Хранит информацию о производителях техники (OEM) и производителях запчастей.
model Brand {
    id             Int              @id @default(autoincrement()) // Уникальный идентификатор бренда

    name           String           @length(1, 100) // Название бренда: "Caterpillar", "Komatsu", "SKF"
    slug           String           @unique @regex("^[a-z0-9-]+$") @length(1, 100) @trim @lower // URL-совместимое имя для использования в ссылках (например, "caterpillar")
    country        String?          @length(2, 100) // Страна происхождения бренда
    isOem          Boolean          @default(false) // Флаг: true = производитель техники (OEM), false = производитель запчастей (Aftermarket)

    // Связанные сущности
    catalogItems   CatalogItem[]    // Список всех каталожных позиций этого бренда
    equipmentModel EquipmentModel[] // Список всех моделей техники этого бренда

    @@index([isOem]) // Индекс для ускорения фильтрации по типу производителя

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}

// Модель техники.
// Описывает конкретную модель техники, к которой могут применяться запчасти.
model EquipmentModel {
    id         String                    @id @default(uuid()) // Уникальный идентификатор модели техники

    createdAt  DateTime                  @default(now()) // Дата создания записи
    updatedAt  DateTime                  @updatedAt      // Дата последнего обновления

    name       String                    @length(1, 100) // Название модели: "Экскаватор CAT 320D"
    partApplicabilities EquipmentApplicability[]
    brand      Brand?                    @relation(fields: [brandId], references: [id]) // Связь с брендом-производителем техники
    brandId    Int?        // Внешний ключ для связи с Brand

    // ПРОСТАЯ СВЯЗЬ С АТРИБУТАМИ
    attributes EquipmentModelAttribute[]

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'ADMIN')
    @@index([brandId])
    @@map("equipment_model")

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().role == 'ADMIN')
}
