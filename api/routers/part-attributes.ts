import { z } from 'zod'
import { router, procedure } from '../trpc'
import { TRPCError } from '@trpc/server'
import { getSystemDB } from '../db'

// Новые схемы для работы с шаблонами атрибутов
const CreatePartAttributeInputSchema = z.object({
  partId: z.number(),
  templateId: z.number(), // ID шаблона из AttributeTemplate
  value: z.string().min(1) // Только значение, все остальное берется из шаблона
});

const UpdatePartAttributeInputSchema = z.object({
  id: z.number(),
  value: z.string().min(1)
});

const BulkCreatePartAttributesInputSchema = z.object({
  partId: z.number(),
  attributes: z.array(z.object({
    templateId: z.number(),
    value: z.string().min(1)
  }))
});

/**
 * Роутер для работы с атрибутами запчастей на основе шаблонов
 * Новая упрощенная архитектура: PartAttribute содержит только partId, templateId и value
 */
export const partAttributesRouter = router({

  /**
   * Создание атрибута запчасти на основе шаблона
   */
  create: procedure
    .input(CreatePartAttributeInputSchema)
    .mutation(async ({ input }: { input: z.infer<typeof CreatePartAttributeInputSchema> }) => {
      try {
        const db = getSystemDB();

        // Проверяем, что запчасть существует
        const part = await db.part.findUnique({
          where: { id: input.partId }
        });

        if (!part) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Запчасть не найдена'
          });
        }

        // Проверяем, что шаблон существует
        const template = await db.attributeTemplate.findUnique({
          where: { id: input.templateId }
        });

        if (!template) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Шаблон атрибута не найден'
          });
        }

        // Проверяем, не существует ли уже такой атрибут для этой запчасти
        const existingAttribute = await db.partAttribute.findUnique({
          where: {
            partId_templateId: {
              partId: input.partId,
              templateId: input.templateId
            }
          }
        });

        if (existingAttribute) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'Атрибут с таким шаблоном уже существует для данной запчасти'
          });
        }

        // Создаем атрибут запчасти
        const attribute = await db.partAttribute.create({
          data: {
            partId: input.partId,
            templateId: input.templateId,
            value: input.value
          },
          include: {
            template: true // Включаем информацию о шаблоне в ответ
          }
        });

        return attribute;

      } catch (error: any) {
        console.error('Ошибка создания атрибута запчасти:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось создать атрибут запчасти'
        });
      }
    }),

  /**
   * Получение атрибутов запчасти с информацией о шаблонах
   */
  findByPartId: procedure
    .input(z.object({
      partId: z.number()
    }))
    .query(async ({ input }: { input: { partId: number } }) => {
      try {
        const db = getSystemDB();

        const attributes = await db.partAttribute.findMany({
          where: {
            partId: input.partId
          },
          include: {
            template: {
              include: {
                group: true // Включаем информацию о группе шаблона
              }
            }
          },
          orderBy: [
            { template: { group: { name: 'asc' } } }, // Сортировка по группе
            { template: { name: 'asc' } } // Затем по имени шаблона
          ]
        });

        return attributes;

      } catch (error: any) {
        console.error('Ошибка получения атрибутов запчасти:', error);

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось получить атрибуты запчасти'
        });
      }
    }),

  /**
   * Обновление значения атрибута запчасти
   */
  update: procedure
    .input(UpdatePartAttributeInputSchema)
    .mutation(async ({ input }: { input: z.infer<typeof UpdatePartAttributeInputSchema> }) => {
      try {
        const db = getSystemDB();

        // Проверяем, что атрибут существует
        const existingAttribute = await db.partAttribute.findUnique({
          where: { id: input.id }
        });

        if (!existingAttribute) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Атрибут не найден'
          });
        }

        // Обновляем только значение
        const updatedAttribute = await db.partAttribute.update({
          where: { id: input.id },
          data: { value: input.value },
          include: {
            template: true
          }
        });

        return updatedAttribute;

      } catch (error: any) {
        console.error('Ошибка обновления атрибута запчасти:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось обновить атрибут запчасти'
        });
      }
    }),

  /**
   * Удаление атрибута запчасти
   */
  delete: procedure
    .input(z.object({
      id: z.number()
    }))
    .mutation(async ({ input }: { input: { id: number } }) => {
      try {
        const db = getSystemDB();

        // Проверяем, что атрибут существует
        const existingAttribute = await db.partAttribute.findUnique({
          where: { id: input.id }
        });

        if (!existingAttribute) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Атрибут не найден'
          });
        }

        // Удаляем атрибут
        await db.partAttribute.delete({
          where: { id: input.id }
        });

        return { success: true };

      } catch (error: any) {
        console.error('Ошибка удаления атрибута запчасти:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось удалить атрибут запчасти'
        });
      }
    }),

  /**
   * Массовое создание атрибутов для запчасти
   */
  bulkCreate: procedure
    .input(BulkCreatePartAttributesInputSchema)
    .mutation(async ({ input }: { input: z.infer<typeof BulkCreatePartAttributesInputSchema> }) => {
      try {
        const db = getSystemDB();

        // Проверяем, что запчасть существует
        const part = await db.part.findUnique({
          where: { id: input.partId }
        });

        if (!part) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Запчасть не найдена'
          });
        }

        // Проверяем, что все шаблоны существуют
        const templateIds = input.attributes.map(attr => attr.templateId);
        const templates = await db.attributeTemplate.findMany({
          where: { id: { in: templateIds } }
        });

        if (templates.length !== templateIds.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Один или несколько шаблонов атрибутов не найдены'
          });
        }

        // Создаем атрибуты в транзакции
        const createdAttributes = await db.$transaction(async (tx) => {
          const results = [];

          for (const attr of input.attributes) {
            // Проверяем, не существует ли уже такой атрибут
            const existing = await tx.partAttribute.findUnique({
              where: {
                partId_templateId: {
                  partId: input.partId,
                  templateId: attr.templateId
                }
              }
            });

            if (!existing) {
              const created = await tx.partAttribute.create({
                data: {
                  partId: input.partId,
                  templateId: attr.templateId,
                  value: attr.value
                },
                include: {
                  template: true
                }
              });
              results.push(created);
            }
          }

          return results;
        });

        return createdAttributes;

      } catch (error: any) {
        console.error('Ошибка массового создания атрибутов запчасти:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось создать атрибуты запчасти'
        });
      }
    })
});
