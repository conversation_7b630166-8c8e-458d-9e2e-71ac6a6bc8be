import { z } from 'zod'
import { router, publicProcedure, authProcedure } from '../trpc'
import { $prisma } from '../db'
import { TRPCError } from '@trpc/server'
import { CrudRouter } from '../lib/crud-router';

// Валидация SVG контента
const svgContentSchema = z.string().refine((value) => {
  if (!value) return true; // Пустое значение допустимо

  // Базовая проверка на SVG структуру
  const trimmed = value.trim();

  // Проверяем наличие SVG тега (может быть не в самом начале из-за XML декларации)
  if (!trimmed.includes('<svg') || !trimmed.includes('</svg>')) {
    return false;
  }

  // Проверка на потенциально опасные элементы
  const dangerousElements = ['script', 'object', 'embed', 'iframe'];
  const dangerousAttributes = ['onload', 'onclick', 'onerror', 'onmouseover', 'onmousedown', 'onmouseup'];

  for (const element of dangerousElements) {
    if (trimmed.includes(`<${element}`)) return false;
  }

  for (const attr of dangerousAttributes) {
    if (trimmed.includes(attr)) return false;
  }

  return true;
}, {
  message: "SVG содержит недопустимые элементы или атрибуты"
}).optional();

// Схемы валидации для аннотаций
const createAnnotationSchema = z.object({
  schemaId: z.string().min(1),
  annotationType: z.string().default('note'),
  x: z.number().min(0).max(100),
  y: z.number().min(0).max(100),
  width: z.number().min(0).max(100).optional(),
  height: z.number().min(0).max(100).optional(),
  text: z.string().min(1).optional(),
  description: z.string().optional(),
  color: z.string().default('#FF0000'),
  fontSize: z.number().int().min(8).max(72).default(12),
  strokeWidth: z.number().int().min(1).max(10).optional(),
  opacity: z.number().min(0).max(1).optional(),
  isVisible: z.boolean().default(true),
  sortOrder: z.number().int().default(0)
})

const updateAnnotationSchema = z.object({
  id: z.string().uuid(),
  annotationType: z.enum(['text', 'arrow', 'highlight', 'area', 'callout']).optional(),
  x: z.number().min(0).max(100).optional(),
  y: z.number().min(0).max(100).optional(),
  width: z.number().min(0).max(100).optional(),
  height: z.number().min(0).max(100).optional(),
  text: z.string().optional(),
  description: z.string().optional(),
  color: z.string().optional(),
  fontSize: z.number().int().min(8).max(72).optional(),
  strokeWidth: z.number().int().min(1).max(10).optional(),
  opacity: z.number().min(0).max(1).optional(),
  isVisible: z.boolean().optional(),
  displayOrder: z.number().int().optional()
})

// Схемы валидации для кросс-референсов
const createCrossReferenceSchema = z.object({
  sourcePartNumberId: z.string().uuid(),
  targetPartNumberId: z.string().uuid(),
  referenceType: z.enum(['DIRECT_REPLACEMENT', 'SUPERSEDED_BY', 'SUPERSEDES', 'EQUIVALENT', 'COMPATIBLE', 'ALTERNATIVE']),
  confidence: z.number().min(0).max(1).default(1.0),
  notes: z.string().optional(),
  restrictions: z.string().optional(),
  effectiveDate: z.date().optional(),
  source: z.string().optional()
})

export const schemaEditorRouter = router({
  // =====================================================
  // СХЕМЫ (CRUD)
  // =====================================================

  // Получить список схем (с пагинацией "cursor based")
  listSchemas: publicProcedure
    .input(
      z.object({
        limit: z.number().int().min(1).max(100).optional().default(50),
        cursor: z.string().uuid().optional(),
        unitId: z.string().uuid().optional(),
        searchQuery: z.string().optional(),
        orderBy: z.enum(['name', 'updatedAt', 'createdAt']).optional().default('updatedAt'),
        orderDirection: z.enum(['asc', 'desc']).optional().default('desc'),
      })
    )
    .query(async ({ input }) => {
      const take = input.limit ?? 50

      // Формируем условие where по переданным фильтрам
      const where: Record<string, any> = {}
      if (input.unitId) where.unitId = input.unitId
      if (input.searchQuery) {
        where.OR = [
          { name: { contains: input.searchQuery, mode: 'insensitive' } },
          { description: { contains: input.searchQuery, mode: 'insensitive' } },
        ]
      }

      const items = await $prisma.aggregateSchema.findMany({
        take: take + 1, // +1 для определения nextCursor
        skip: input.cursor ? 1 : 0,
        cursor: input.cursor ? { id: input.cursor } : undefined,
        where,
        orderBy: { [input.orderBy!]: input.orderDirection },
        include: {
          positions: {
            select: { id: true },
          },
          annotations: {
            select: { id: true },
          },
        },
      })

      let nextCursor: string | undefined = undefined
      if (items.length > take) {
        const nextItem = items.pop()!
        nextCursor = nextItem.id
      }

      return {
        items,
        nextCursor,
      }
    }),

  // Получить одну схему по ID (с позициями и аннотациями)
  getSchema: publicProcedure
    .input(z.object({ id: z.string().uuid() }))
    .query(async ({ input }) => {
      const schema = await $prisma.aggregateSchema.findUnique({
        where: { id: input.id },
        include: {
          positions: {
            include: {
              part: {
                include: {
                  partNumbers: {
                    include: { brand: true },
                  },
                },
              },
            },
          },
          annotations: true,
        },
      })

      if (!schema) {
        throw new TRPCError({ code: 'NOT_FOUND', message: 'Схема не найдена' })
      }

      return schema
    }),

  // Создать новую схему
  createSchema: authProcedure
    .input(
      z.object({
        name: z.string().min(1),
        unitId: z.string().uuid().optional(),
        description: z.string().optional(),
        imageUrl: z.string().url().optional(),
        imageWidth: z.number().int().optional(),
        imageHeight: z.number().int().optional(),
        svgContent: svgContentSchema,
        isActive: z.boolean().default(true).optional(),
        sortOrder: z.number().int().default(0).optional(),
      })
    )
    .mutation(async ({ input }) => {
      const schema = await $prisma.aggregateSchema.create({
        data: {
          name: input.name,
          unitId: input.unitId,
          description: input.description,
          imageUrl: input.imageUrl,
          imageWidth: input.imageWidth,
          imageHeight: input.imageHeight,
          svgContent: input.svgContent,
          isActive: input.isActive ?? true,
          sortOrder: input.sortOrder ?? 0,
        },
        include: {
          positions: true,
          annotations: true,
        },
      })

      return schema
    }),

  // Обновить схему
  updateSchema: authProcedure
    .input(
      z.object({
        id: z.string().uuid(),
        name: z.string().min(1).optional(),
        unitId: z.string().uuid().optional(),
        description: z.string().optional(),
        imageUrl: z.string().url().optional(),
        imageWidth: z.number().int().optional(),
        imageHeight: z.number().int().optional(),
        svgContent: svgContentSchema,
        isActive: z.boolean().optional(),
        sortOrder: z.number().int().optional(),
      })
    )
    .mutation(async ({ input }) => {
      const { id, ...data } = input

      const schema = await $prisma.aggregateSchema.update({
        where: { id },
        data,
        include: {
          positions: true,
          annotations: true,
        },
      })

      return schema
    }),

  // Удалить схему
  deleteSchema: authProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ input }) => {
      await $prisma.aggregateSchema.delete({
        where: { id: input.id }
      })

      return { success: true }
    }),

  // =====================================================
  // АННОТАЦИИ
  // =====================================================
  
  // Получить все аннотации для схемы
  getAnnotations: publicProcedure
    .input(z.object({
      schemaId: z.string().min(1)
    }))
    .query(async ({ input }) => {
      const annotations = await $prisma.schemaAnnotation.findMany({
        where: {
          schemaId: input.schemaId
        },
        orderBy: {
          sortOrder: 'asc'
        }
      })

      return annotations
    }),

  // Создать аннотацию
  createAnnotation: authProcedure
    .input(createAnnotationSchema)
    .mutation(async ({ input }) => {
      const annotation = await $prisma.schemaAnnotation.create({
        data: input
      })

      return annotation
    }),

  // Обновить аннотацию
  updateAnnotation: authProcedure
    .input(updateAnnotationSchema)
    .mutation(async ({ input }) => {
      const { id, ...data } = input
      
      const annotation = await $prisma.schemaAnnotation.update({
        where: { id },
        data
      })

      return annotation
    }),

  // Удалить аннотацию
  deleteAnnotation: authProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ input }) => {
      await $prisma.schemaAnnotation.delete({
        where: { id: input.id }
      })

      return { success: true }
    }),

  // Массовое обновление порядка аннотаций
  updateAnnotationsOrder: authProcedure
    .input(z.object({
      annotations: z.array(z.object({
        id: z.string().uuid(),
        displayOrder: z.number().int()
      }))
    }))
    .mutation(async ({ input }) => {
      const updates = input.annotations.map(annotation =>
        $prisma.schemaAnnotation.update({
          where: { id: annotation.id },
          data: { displayOrder: annotation.displayOrder }
        })
      )

      await $prisma.$transaction(updates)

      return { success: true }
    }),

  // =====================================================
  // КРОСС-РЕФЕРЕНСЫ
  // =====================================================

  // Получить кросс-референсы для номера детали
  getCrossReferences: publicProcedure
    .input(z.object({
      partNumberId: z.string().uuid(),
      includeIncoming: z.boolean().default(true),
      includeOutgoing: z.boolean().default(true)
    }))
    .query(async ({ input }) => {
      const crossReferences = []

      if (input.includeOutgoing) {
        const outgoing = await $prisma.partNumberCrossReference.findMany({
          where: { sourcePartNumberId: input.partNumberId },
          include: {
            targetPartNumber: {
              include: {
                brand: true,
                part: true
              }
            }
          }
        })
        crossReferences.push(...outgoing.map(ref => ({ ...ref, direction: 'outgoing' })))
      }

      if (input.includeIncoming) {
        const incoming = await $prisma.partNumberCrossReference.findMany({
          where: { targetPartNumberId: input.partNumberId },
          include: {
            sourcePartNumber: {
              include: {
                brand: true,
                part: true
              }
            }
          }
        })
        crossReferences.push(...incoming.map(ref => ({ ...ref, direction: 'incoming' })))
      }

      return crossReferences
    }),

  // Создать кросс-референс
  createCrossReference: authProcedure
    .input(createCrossReferenceSchema)
    .mutation(async ({ input }) => {
      // Проверяем, что не создаем дублирующий кросс-референс
      const existing = await $prisma.partNumberCrossReference.findFirst({
        where: {
          sourcePartNumberId: input.sourcePartNumberId,
          targetPartNumberId: input.targetPartNumberId,
          referenceType: input.referenceType
        }
      })

      if (existing) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Такой кросс-референс уже существует'
        })
      }

      const crossReference = await $prisma.partNumberCrossReference.create({
        data: input,
        include: {
          sourcePartNumber: {
            include: {
              brand: true,
              part: true
            }
          },
          targetPartNumber: {
            include: {
              brand: true,
              part: true
            }
          }
        }
      })

      return crossReference
    }),

  // Удалить кросс-референс
  deleteCrossReference: authProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ input }) => {
      await $prisma.partNumberCrossReference.delete({
        where: { id: input.id }
      })

      return { success: true }
    }),

  // =====================================================
  // ПРОВЕРКА СОВМЕСТИМОСТИ
  // =====================================================

  // Проверить совместимость детали с техникой
  checkPartCompatibility: publicProcedure
    .input(z.object({
      partId: z.string().uuid(),
      equipmentModelId: z.string().uuid().optional(),
      year: z.number().int().optional()
    }))
    .query(async ({ input }) => {
      const compatibility = await $prisma.partCompatibility.findMany({
        where: {
          partId: input.partId,
          ...(input.equipmentModelId && { equipmentModelId: input.equipmentModelId }),
          ...(input.year && {
            OR: [
              { yearFrom: null, yearTo: null },
              { yearFrom: { lte: input.year }, yearTo: null },
              { yearFrom: null, yearTo: { gte: input.year } },
              { yearFrom: { lte: input.year }, yearTo: { gte: input.year } }
            ]
          })
        },
        include: {
          equipmentModel: {
            include: {
              brand: true
            }
          }
        }
      })

      return compatibility
    }),

  // =====================================================
  // ПРОВЕРКА ДОСТУПНОСТИ
  // =====================================================

  // Проверить доступность деталей на схеме
  checkSchemaPartsAvailability: publicProcedure
    .input(z.object({
      schemaId: z.string().min(1),
      variantId: z.string().uuid().optional()
    }))
    .query(async ({ input }) => {
      const positions = await $prisma.schemaPosition.findMany({
        where: {
          schemaId: input.schemaId,
          variantId: input.variantId || null
        },
        include: {
          part: {
            include: {
              shopParts: {
                where: {
                  isAvailable: true
                },
                include: {
                  shop: true
                }
              },
              partNumbers: {
                include: {
                  brand: true
                }
              }
            }
          }
        }
      })

      const availability = positions.map(position => {
        const availableShopParts = position.part?.shopParts
          .filter(sp => sp.isAvailable) || []

        return {
          positionId: position.id,
          positionNumber: position.positionNumber,
          partId: position.partId,
          partName: position.part?.name,
          isAvailable: availableShopParts.length > 0,
          availableCount: availableShopParts.length,
          shops: availableShopParts.map(sp => ({
            shopId: sp.shopId,
            shopName: sp.shop.name,
            price: sp.price,
            quantity: sp.quantity
          }))
        }
      })

      return availability
    }),

  // Получить стандартизированные атрибуты для детали
  getStandardizedAttributes: publicProcedure
    .input(z.object({ partId: z.string().uuid() }))
    .query(async ({ input }) => {
      const attributes = await $prisma.standardizedPartAttribute.findMany({
        where: { partId: input.partId },
        include: {
          attribute: true
        }
      })

      return attributes
    }),

  // Создать стандартизированный атрибут
  createStandardizedAttribute: authProcedure
    .input(z.object({
      partId: z.string().uuid(),
      attributeId: z.string().uuid(),
      stringValue: z.string().optional(),
      numberValue: z.number().optional(),
      booleanValue: z.boolean().optional(),
      dateValue: z.date().optional(),
      jsonValue: z.any().optional(),
      unit: z.string().optional(),
      precision: z.number().int().optional(),
      tolerance: z.number().optional(),
      context: z.string().optional(),
      conditions: z.string().optional()
    }))
    .mutation(async ({ input }) => {
      const attribute = await $prisma.standardizedPartAttribute.create({
        data: input,
        include: {
          attribute: true
        }
      })

      return attribute
    }),

  // =====================================================
  // ПОЗИЦИИ СХЕМ
  // =====================================================

  // Создать позицию на схеме
  createPosition: authProcedure
    .input(z.object({
      schemaId: z.string().min(1),
      partId: z.string().min(1),
      positionNumber: z.string().min(1).max(10),
      x: z.number().min(0).max(100),
      y: z.number().min(0).max(100),
      quantity: z.number().int().min(1).default(1),
      isRequired: z.boolean().default(true),
      isHighlighted: z.boolean().default(false),
      notes: z.string().max(500).optional(),
      installationOrder: z.number().int().optional(),
      variantId: z.string().uuid().optional()
    }))
    .mutation(async ({ input }) => {
      const { variantId, ...data } = input; // variantId игнорируется, т.к. нет в модели
      const position = await $prisma.schemaPosition.create({
        data,
        include: {
          part: {
            include: {
              partNumbers: {
                include: {
                  brand: true
                }
              }
            }
          }
        }
      })

      return position
    }),

  // Обновить позицию на схеме
  updatePosition: authProcedure
    .input(z.object({
      id: z.string().uuid(),
      x: z.number().min(0).max(100).optional(),
      y: z.number().min(0).max(100).optional(),
      quantity: z.number().int().min(1).optional(),
      isRequired: z.boolean().optional(),
      isHighlighted: z.boolean().optional(),
      notes: z.string().max(500).optional(),
      installationOrder: z.number().int().optional()
    }))
    .mutation(async ({ input }) => {
      const { id, ...data } = input

      const position = await $prisma.schemaPosition.update({
        where: { id },
        data,
        include: {
          part: {
            include: {
              partNumbers: {
                include: {
                  brand: true
                }
              }
            }
          }
        }
      })

      return position
    }),

  // Удалить позицию
  deletePosition: authProcedure
    .input(z.object({ id: z.string().uuid() }))
    .mutation(async ({ input }) => {
      await $prisma.schemaPosition.delete({
        where: { id: input.id }
      })

      return { success: true }
    })
})
