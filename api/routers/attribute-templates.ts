import { z } from 'zod'
import { router, procedure, expertProcedure } from '../trpc'
import { TRPCError } from '@trpc/server'
import { getSystemDB } from '../db'
import { AttributeDataTypeSchema } from '../generated/zod/enums/AttributeDataType.schema'
import { AttributeUnitSchema } from '../generated/zod/enums/AttributeUnit.schema'

// Схемы для работы с шаблонами атрибутов
const CreateAttributeTemplateInputSchema = z.object({
  name: z.string().min(1).max(100).regex(/^[a-z0-9_]+$/, 'Имя должно содержать только строчные буквы, цифры и подчеркивания'),
  title: z.string().min(1).max(200),
  description: z.string().optional().nullable(),
  dataType: AttributeDataTypeSchema.default('STRING'),
  unit: AttributeUnitSchema.optional().nullable(),
  isRequired: z.boolean().default(false),
  minValue: z.number().optional().nullable(),
  maxValue: z.number().optional().nullable(),
  allowedValues: z.array(z.string()).optional().default([]),
  groupId: z.number().optional().nullable()
});

const UpdateAttributeTemplateInputSchema = z.object({
  id: z.number(),
  name: z.string().min(1).max(100).regex(/^[a-z0-9_]+$/, 'Имя должно содержать только строчные буквы, цифры и подчеркивания').optional(),
  title: z.string().min(1).max(200).optional(),
  description: z.string().optional().nullable(),
  dataType: AttributeDataTypeSchema.optional(),
  unit: AttributeUnitSchema.optional().nullable(),
  isRequired: z.boolean().optional(),
  minValue: z.number().optional().nullable(),
  maxValue: z.number().optional().nullable(),
  allowedValues: z.array(z.string()).optional(),
  groupId: z.number().optional().nullable()
});

const CreateAttributeGroupInputSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional().nullable()
});

const UpdateAttributeGroupInputSchema = z.object({
  id: z.number(),
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional().nullable()
});

/**
 * Роутер для управления шаблонами атрибутов
 * Доступен только администраторам
 */
export const attributeTemplatesRouter = router({
  
  // =====================================================
  // УПРАВЛЕНИЕ ШАБЛОНАМИ АТРИБУТОВ
  // =====================================================
  
  /**
   * Получение всех шаблонов атрибутов с группировкой
   */
  findMany: procedure
    .input(z.object({
      groupId: z.number().optional(),
      search: z.string().optional(),
      dataType: AttributeDataTypeSchema.optional(),
      limit: z.number().min(1).max(100).default(50),
      offset: z.number().min(0).default(0)
    }))
    .query(async ({ input }) => {
      try {
        const db = getSystemDB();
        
        const where: any = {};
        
        if (input.groupId) {
          where.groupId = input.groupId;
        }
        
        if (input.search) {
          where.OR = [
            { name: { contains: input.search, mode: 'insensitive' } },
            { title: { contains: input.search, mode: 'insensitive' } },
            { description: { contains: input.search, mode: 'insensitive' } }
          ];
        }
        
        if (input.dataType) {
          where.dataType = input.dataType;
        }
        
        const [templates, total] = await Promise.all([
          db.attributeTemplate.findMany({
            where,
            include: {
              group: true,
              _count: {
                select: {
                  partAttributes: true,
                  catalogItemAttributes: true,
                  equipmentAttributes: true
                }
              }
            },
            orderBy: [
              { group: { name: 'asc' } },
              { name: 'asc' }
            ],
            take: input.limit,
            skip: input.offset
          }),
          db.attributeTemplate.count({ where })
        ]);
        
        return {
          templates,
          total,
          hasMore: input.offset + input.limit < total
        };
        
      } catch (error: any) {
        console.error('Ошибка получения шаблонов атрибутов:', error);
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось получить шаблоны атрибутов'
        });
      }
    }),

  /**
   * Получение шаблона по ID
   */
  findById: procedure
    .input(z.object({
      id: z.number()
    }))
    .query(async ({ input }) => {
      try {
        const db = getSystemDB();
        
        const template = await db.attributeTemplate.findUnique({
          where: { id: input.id },
          include: {
            group: true,
            _count: {
              select: {
                partAttributes: true,
                catalogItemAttributes: true,
                equipmentAttributes: true
              }
            }
          }
        });
        
        if (!template) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Шаблон атрибута не найден'
          });
        }
        
        return template;
        
      } catch (error: any) {
        console.error('Ошибка получения шаблона атрибута:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось получить шаблон атрибута'
        });
      }
    }),

  /**
   * Создание нового шаблона атрибута
   */
  create: expertProcedure
    .input(CreateAttributeTemplateInputSchema)
    .mutation(async ({ input }) => {
      try {
        const db = getSystemDB();
        
        // Проверяем уникальность имени
        const existingTemplate = await db.attributeTemplate.findUnique({
          where: { name: input.name }
        });
        
        if (existingTemplate) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'Шаблон с таким именем уже существует'
          });
        }
        
        // Проверяем группу, если указана
        if (input.groupId) {
          const group = await db.attributeGroup.findUnique({
            where: { id: input.groupId }
          });
          
          if (!group) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Группа атрибутов не найдена'
            });
          }
        }
        
        // Создаем шаблон
        const template = await db.attributeTemplate.create({
          data: {
            name: input.name,
            title: input.title,
            description: input.description,
            dataType: input.dataType,
            unit: input.unit,
            isRequired: input.isRequired,
            minValue: input.minValue,
            maxValue: input.maxValue,
            allowedValues: input.allowedValues,
            groupId: input.groupId
          },
          include: {
            group: true
          }
        });
        
        return template;
        
      } catch (error: any) {
        console.error('Ошибка создания шаблона атрибута:', error);
        
        if (error instanceof TRPCError) {
          throw error;
        }
        
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось создать шаблон атрибута'
        });
      }
    }),

  /**
   * Обновление шаблона атрибута
   */
  update: expertProcedure
    .input(UpdateAttributeTemplateInputSchema)
    .mutation(async ({ input }) => {
      try {
        const db = getSystemDB();
        
        // Проверяем, что шаблон существует
        const existingTemplate = await db.attributeTemplate.findUnique({
          where: { id: input.id }
        });
        
        if (!existingTemplate) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Шаблон атрибута не найден'
          });
        }
        
        // Проверяем уникальность имени, если оно изменяется
        if (input.name && input.name !== existingTemplate.name) {
          const nameConflict = await db.attributeTemplate.findUnique({
            where: { name: input.name }
          });
          
          if (nameConflict) {
            throw new TRPCError({
              code: 'CONFLICT',
              message: 'Шаблон с таким именем уже существует'
            });
          }
        }
        
        // Проверяем группу, если она изменяется
        if (input.groupId !== undefined && input.groupId !== null) {
          const group = await db.attributeGroup.findUnique({
            where: { id: input.groupId }
          });
          
          if (!group) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Группа атрибутов не найдена'
            });
          }
        }
        
        // Обновляем шаблон
        const { id, ...updateData } = input;
        const updatedTemplate = await db.attributeTemplate.update({
          where: { id },
          data: updateData,
          include: {
            group: true
          }
        });
        
        return updatedTemplate;

      } catch (error: any) {
        console.error('Ошибка обновления шаблона атрибута:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось обновить шаблон атрибута'
        });
      }
    }),

  /**
   * Удаление шаблона атрибута
   */
  delete: expertProcedure
    .input(z.object({
      id: z.number()
    }))
    .mutation(async ({ input }) => {
      try {
        const db = getSystemDB();

        // Проверяем, что шаблон существует
        const existingTemplate = await db.attributeTemplate.findUnique({
          where: { id: input.id },
          include: {
            _count: {
              select: {
                partAttributes: true,
                catalogItemAttributes: true,
                equipmentAttributes: true
              }
            }
          }
        });

        if (!existingTemplate) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Шаблон атрибута не найден'
          });
        }

        // Проверяем, используется ли шаблон
        const totalUsage = existingTemplate._count.partAttributes +
                          existingTemplate._count.catalogItemAttributes +
                          existingTemplate._count.equipmentAttributes;

        if (totalUsage > 0) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: `Шаблон используется в ${totalUsage} атрибутах и не может быть удален`
          });
        }

        // Удаляем шаблон
        await db.attributeTemplate.delete({
          where: { id: input.id }
        });

        return { success: true };

      } catch (error: any) {
        console.error('Ошибка удаления шаблона атрибута:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось удалить шаблон атрибута'
        });
      }
    }),

  // =====================================================
  // УПРАВЛЕНИЕ ГРУППАМИ АТРИБУТОВ
  // =====================================================

  /**
   * Получение всех групп атрибутов
   */
  findAllGroups: procedure
    .query(async () => {
      try {
        const db = getSystemDB();

        const groups = await db.attributeGroup.findMany({
          include: {
            _count: {
              select: {
                templates: true
              }
            }
          },
          orderBy: {
            name: 'asc'
          }
        });

        return groups;

      } catch (error: any) {
        console.error('Ошибка получения групп атрибутов:', error);

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось получить группы атрибутов'
        });
      }
    }),

  /**
   * Поиск групп атрибутов
   */
  searchGroups: procedure
    .input(z.object({
      query: z.string().optional(),
      limit: z.number().min(1).max(50).default(20)
    }))
    .query(async ({ input }) => {
      try {
        const db = getSystemDB();

        const where: any = {};
        
        if (input.query && input.query.trim()) {
          where.OR = [
            { name: { contains: input.query.trim(), mode: 'insensitive' } },
            { description: { contains: input.query.trim(), mode: 'insensitive' } }
          ];
        }

        const groups = await db.attributeGroup.findMany({
          where,
          include: {
            _count: {
              select: {
                templates: true
              }
            }
          },
          orderBy: [
            { name: 'asc' }
          ],
          take: input.limit
        });

        return groups;

      } catch (error: any) {
        console.error('Ошибка поиска групп атрибутов:', error);

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось найти группы атрибутов'
        });
      }
    }),

  /**
   * Создание группы атрибутов
   */
  createGroup: expertProcedure
    .input(CreateAttributeGroupInputSchema)
    .mutation(async ({ input }) => {
      try {
        const db = getSystemDB();

        // Проверяем уникальность имени группы
        const existingGroup = await db.attributeGroup.findFirst({
          where: {
            name: {
              equals: input.name,
              mode: 'insensitive'
            }
          }
        });

        if (existingGroup) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'Группа с таким именем уже существует'
          });
        }

        // Создаем группу
        const group = await db.attributeGroup.create({
          data: {
            name: input.name.trim(),
            description: input.description
          }
        });

        return group;

      } catch (error: any) {
        console.error('Ошибка создания группы атрибутов:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось создать группу атрибутов'
        });
      }
    }),

  /**
   * Обновление группы атрибутов
   */
  updateGroup: expertProcedure
    .input(UpdateAttributeGroupInputSchema)
    .mutation(async ({ input }) => {
      try {
        const db = getSystemDB();

        // Проверяем, что группа существует
        const existingGroup = await db.attributeGroup.findUnique({
          where: { id: input.id }
        });

        if (!existingGroup) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Группа атрибутов не найдена'
          });
        }

        // Проверяем уникальность имени, если оно изменяется
        if (input.name && input.name !== existingGroup.name) {
          const nameConflict = await db.attributeGroup.findFirst({
            where: {
              name: {
                equals: input.name,
                mode: 'insensitive'
              },
              id: {
                not: input.id
              }
            }
          });

          if (nameConflict) {
            throw new TRPCError({
              code: 'CONFLICT',
              message: 'Группа с таким именем уже существует'
            });
          }
        }

        // Обновляем группу
        const { id, ...updateData } = input;
        const updatedGroup = await db.attributeGroup.update({
          where: { id },
          data: updateData
        });

        return updatedGroup;

      } catch (error: any) {
        console.error('Ошибка обновления группы атрибутов:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось обновить группу атрибутов'
        });
      }
    }),

  /**
   * Удаление группы атрибутов
   */
  deleteGroup: expertProcedure
    .input(z.object({
      id: z.number()
    }))
    .mutation(async ({ input }) => {
      try {
        const db = getSystemDB();

        // Проверяем, что группа существует
        const existingGroup = await db.attributeGroup.findUnique({
          where: { id: input.id },
          include: {
            _count: {
              select: {
                templates: true
              }
            }
          }
        });

        if (!existingGroup) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Группа атрибутов не найдена'
          });
        }

        // Проверяем, есть ли шаблоны в группе
        if (existingGroup._count.templates > 0) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: `В группе есть ${existingGroup._count.templates} шаблонов. Сначала удалите или переместите их.`
          });
        }

        // Удаляем группу
        await db.attributeGroup.delete({
          where: { id: input.id }
        });

        return { success: true };

      } catch (error: any) {
        console.error('Ошибка удаления группы атрибутов:', error);

        if (error instanceof TRPCError) {
          throw error;
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось удалить группу атрибутов'
        });
      }
    })
});
