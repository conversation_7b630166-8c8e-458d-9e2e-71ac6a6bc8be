import { z } from 'zod'
import { router, expertProcedure } from '../trpc'
import { auth } from '../auth'
import { TRPCError } from '@trpc/server'
import { AdminAuditLogger, AdminActionType, validateAdminPermissions, AdminRateLimiter } from '../lib/admin-audit'

/**
 * Административный tRPC роутер для управления пользователями
 * Использует better-auth admin plugin API
 * Доступен только пользователям с ролью ADMIN (админы)
 */
export const adminRouter = router({
  
  // =====================================================
  // УПРАВЛЕНИЕ ПОЛЬЗОВАТЕЛЯМИ
  // =====================================================
  
  /**
   * Создание нового пользователя администратором
   */
  createUser: expertProcedure
    .input(z.object({
      name: z.string().min(1, 'Имя обязательно').max(100, 'Имя слишком длинное'),
      email: z.string().email('Некорректный email').max(255, 'Email слишком длинный'),
      password: z.string().min(6, 'Пароль должен содержать минимум 6 символов').max(100, 'Пароль слишком длинный'),
      role: z.enum(['USER', 'SHOP', 'ADMIN']).default('USER'),
      emailVerified: z.boolean().default(false),
      data: z.record(z.any()).optional() // Дополнительные поля
    }))
    .mutation(async ({ input, ctx }) => {
      // Валидация прав доступа
      if (!validateAdminPermissions(ctx.user, 'createUser')) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Недостаточно прав для создания пользователей'
        })
      }

      // Rate limiting
      if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'createUser', 10, 60000)) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Слишком много запросов на создание пользователей. Попробуйте позже.'
        })
      }

      try {
        const result = await auth.api.createUser({
          body: {
            name: input.name,
            email: input.email,
            password: input.password,
            role: input.role,
            data: {
              emailVerified: input.emailVerified,
              ...input.data
            }
          }
        })

        // Логирование успешного действия
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_CREATED,
          targetUserEmail: input.email,
          details: {
            name: input.name,
            role: input.role,
            emailVerified: input.emailVerified
          }
        })

        return result
      } catch (error: any) {
        // Логирование ошибки
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_CREATED,
          targetUserEmail: input.email,
          details: {
            error: error.message,
            success: false
          }
        })

        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message || 'Не удалось создать пользователя'
        })
      }
    }),

  /**
   * Получение списка пользователей с фильтрацией и пагинацией
   */
  listUsers: expertProcedure
    .input(z.object({
      search: z.string().optional(),
      searchField: z.enum(['email', 'name']).default('email'),
      searchOperator: z.enum(['contains', 'starts_with', 'ends_with']).default('contains'),
      filterField: z.string().optional(),
      filterOperator: z.enum(['eq', 'contains', 'starts_with', 'ends_with']).default('eq'),
      filterValue: z.string().optional(),
      sortBy: z.string().default('createdAt'),
      sortDirection: z.enum(['asc', 'desc']).default('desc'),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0)
    }))
    .query(async ({ input }) => {
      try {
        const query: any = {
          limit: input.limit,
          offset: input.offset,
          sortBy: input.sortBy,
          sortDirection: input.sortDirection
        }

        // Добавляем поиск если указан
        if (input.search) {
          query.searchField = input.searchField
          query.searchOperator = input.searchOperator
          query.searchValue = input.search
        }

        // Добавляем фильтр если указан
        if (input.filterField && input.filterValue) {
          query.filterField = input.filterField
          query.filterOperator = input.filterOperator
          query.filterValue = input.filterValue
        }

        const result = await auth.api.listUsers({
          query
        })
        
        return result
      } catch (error: any) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось получить список пользователей'
        })
      }
    }),

  /**
   * Изменение роли пользователя
   */
  setUserRole: expertProcedure
    .input(z.object({
      userId: z.string(),
      role: z.enum(['USER', 'SHOP', 'ADMIN'])
    }))
    .mutation(async ({ input }) => {
      try {
        const result = await auth.api.setRole({
          body: {
            userId: input.userId,
            role: input.role
          }
        })
        
        return result
      } catch (error: any) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message || 'Не удалось изменить роль пользователя'
        })
      }
    }),

  /**
   * Сброс пароля пользователя администратором
   */
  resetUserPassword: expertProcedure
    .input(z.object({
      userId: z.string(),
      newPassword: z.string().min(6, 'Пароль должен содержать минимум 6 символов').max(100, 'Пароль слишком длинный'),
      sendByEmail: z.boolean().default(false)
    }))
    .mutation(async ({ input, ctx }) => {
      // Валидация прав доступа
      if (!validateAdminPermissions(ctx.user, 'resetUserPassword')) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Недостаточно прав для сброса паролей пользователей'
        })
      }

      // Rate limiting
      if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'resetUserPassword', 10, 60000)) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Слишком много запросов на сброс паролей. Попробуйте позже.'
        })
      }

      try {
        // Получаем информацию о пользователе
        const user = await $prisma.user.findUnique({
          where: { id: input.userId },
          select: { id: true, email: true, name: true }
        })

        if (!user) {
          throw new Error('Пользователь не найден')
        }

        // Обновляем пароль через better-auth admin API
        const result = await auth.api.setPassword({
          body: {
            userId: input.userId,
            newPassword: input.newPassword
          }
        })

        // Если нужно отправить пароль по email
        if (input.sendByEmail) {
          // TODO: Реализовать отправку пароля по email
          console.log('📧 ОТПРАВКА НОВОГО ПАРОЛЯ:')
          console.log('👤 Пользователь:', user.email)
          console.log('🔑 Новый пароль:', input.newPassword)
          console.log('✅ Пароль "отправлен" по email (в режиме разработки)')
        }

        // Логирование успешного действия
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_UPDATED,
          targetUserId: input.userId,
          targetUserEmail: user.email,
          details: {
            action: 'password_reset',
            sentByEmail: input.sendByEmail
          }
        })

        return { success: true }
      } catch (error: any) {
        // Логирование ошибки
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_UPDATED,
          targetUserId: input.userId,
          details: {
            error: error.message,
            action: 'password_reset'
          },
          success: false
        })

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Ошибка сброса пароля: ${error.message}`
        })
      }
    }),

  /**
   * Удаление пользователя
   */
  removeUser: expertProcedure
    .input(z.object({
      userId: z.string().uuid('Некорректный ID пользователя')
    }))
    .mutation(async ({ input, ctx }) => {
      // Валидация прав доступа
      if (!validateAdminPermissions(ctx.user, 'removeUser')) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Недостаточно прав для удаления пользователей'
        })
      }

      // Rate limiting (строгий для удаления)
      if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'removeUser', 5, 60000)) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Слишком много запросов на удаление. Попробуйте позже.'
        })
      }

      // Проверяем, что администратор не удаляет сам себя
      if (input.userId === ctx.user!.id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Нельзя удалить самого себя'
        })
      }

      try {
        const result = await auth.api.removeUser({
          body: {
            userId: input.userId
          }
        })

        // Логирование успешного действия
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_DELETED,
          targetUserId: input.userId
        })

        return result
      } catch (error: any) {
        // Логирование ошибки
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_DELETED,
          targetUserId: input.userId,
          details: {
            error: error.message,
            success: false
          }
        })

        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message || 'Не удалось удалить пользователя'
        })
      }
    }),

  // =====================================================
  // СИСТЕМА БАНОВ
  // =====================================================

  /**
   * Блокировка пользователя
   */
  banUser: expertProcedure
    .input(z.object({
      userId: z.string().uuid('Некорректный ID пользователя'),
      banReason: z.string().max(500, 'Причина блокировки слишком длинная').optional(),
      banExpiresIn: z.number().min(60, 'Минимальный срок блокировки - 1 минута').max(60 * 60 * 24 * 365, 'Максимальный срок блокировки - 1 год').optional()
    }))
    .mutation(async ({ input, ctx }) => {
      // Валидация прав доступа
      if (!validateAdminPermissions(ctx.user, 'banUser')) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Недостаточно прав для блокировки пользователей'
        })
      }

      // Rate limiting
      if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'banUser', 20, 60000)) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Слишком много запросов на блокировку. Попробуйте позже.'
        })
      }

      // Проверяем, что администратор не блокирует сам себя
      if (input.userId === ctx.user!.id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Нельзя заблокировать самого себя'
        })
      }

      try {
        const result = await auth.api.banUser({
          body: {
            userId: input.userId,
            banReason: input.banReason,
            banExpiresIn: input.banExpiresIn
          }
        })

        // Логирование успешного действия
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_BANNED,
          targetUserId: input.userId,
          details: {
            banReason: input.banReason,
            banExpiresIn: input.banExpiresIn
          }
        })

        return result
      } catch (error: any) {
        // Логирование ошибки
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_BANNED,
          targetUserId: input.userId,
          details: {
            error: error.message,
            success: false
          }
        })

        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message || 'Не удалось заблокировать пользователя'
        })
      }
    }),

  /**
   * Разблокировка пользователя
   */
  unbanUser: expertProcedure
    .input(z.object({
      userId: z.string().uuid('Некорректный ID пользователя')
    }))
    .mutation(async ({ input, ctx }) => {
      // Валидация прав доступа
      if (!validateAdminPermissions(ctx.user, 'unbanUser')) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Недостаточно прав для разблокировки пользователей'
        })
      }

      // Rate limiting
      if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'unbanUser', 20, 60000)) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Слишком много запросов на разблокировку. Попробуйте позже.'
        })
      }

      try {
        const result = await auth.api.unbanUser({
          body: {
            userId: input.userId
          }
        })

        // Логирование успешного действия
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_UNBANNED,
          targetUserId: input.userId
        })

        return result
      } catch (error: any) {
        // Логирование ошибки
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_UNBANNED,
          targetUserId: input.userId,
          details: {
            error: error.message,
            success: false
          }
        })

        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message || 'Не удалось разблокировать пользователя'
        })
      }
    }),

  // =====================================================
  // УПРАВЛЕНИЕ СЕССИЯМИ
  // =====================================================

  /**
   * Получение списка сессий пользователя
   */
  listUserSessions: expertProcedure
    .input(z.object({
      userId: z.string()
    }))
    .query(async ({ input }) => {
      try {
        const result = await auth.api.listUserSessions({
          body: {
            userId: input.userId
          }
        })
        
        return result
      } catch (error: any) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message || 'Не удалось получить список сессий'
        })
      }
    }),

  /**
   * Отзыв конкретной сессии пользователя
   */
  revokeUserSession: expertProcedure
    .input(z.object({
      sessionToken: z.string()
    }))
    .mutation(async ({ input }) => {
      try {
        const result = await auth.api.revokeUserSession({
          body: {
            sessionToken: input.sessionToken
          }
        })
        
        return result
      } catch (error: any) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message || 'Не удалось отозвать сессию'
        })
      }
    }),

  /**
   * Отзыв всех сессий пользователя
   */
  revokeAllUserSessions: expertProcedure
    .input(z.object({
      userId: z.string()
    }))
    .mutation(async ({ input }) => {
      try {
        const result = await auth.api.revokeUserSessions({
          body: {
            userId: input.userId
          }
        })
        
        return result
      } catch (error: any) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message || 'Не удалось отозвать сессии пользователя'
        })
      }
    }),

  // =====================================================
  // IMPERSONATION (ИМПЕРСОНАЦИЯ)
  // =====================================================

  /**
   * Вход от имени пользователя (impersonation)
   */
  impersonateUser: expertProcedure
    .input(z.object({
      userId: z.string().uuid('Некорректный ID пользователя')
    }))
    .mutation(async ({ input, ctx }) => {
      // Валидация прав доступа
      if (!validateAdminPermissions(ctx.user, 'impersonateUser')) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Недостаточно прав для входа от имени пользователей'
        })
      }

      // Rate limiting (более строгий для impersonation)
      if (!AdminRateLimiter.checkRateLimit(ctx.user!.id, 'impersonateUser', 5, 60000)) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Слишком много попыток входа от имени пользователей. Попробуйте позже.'
        })
      }

      // Проверяем, что администратор не пытается войти от своего имени
      if (input.userId === ctx.user!.id) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Нельзя войти от своего имени'
        })
      }

      try {
        const result = await auth.api.impersonateUser({
          body: {
            userId: input.userId
          }
        })

        // Логирование успешного действия
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_IMPERSONATED,
          targetUserId: input.userId,
          details: {
            timestamp: new Date().toISOString()
          }
        })

        return result
      } catch (error: any) {
        // Логирование ошибки
        await AdminAuditLogger.logAction({
          admin: ctx.user!,
          action: AdminActionType.USER_IMPERSONATED,
          targetUserId: input.userId,
          details: {
            error: error.message,
            success: false
          }
        })

        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message || 'Не удалось войти от имени пользователя'
        })
      }
    }),

  /**
   * Выход из режима impersonation
   */
  stopImpersonating: expertProcedure
    .mutation(async () => {
      try {
        const result = await auth.api.stopImpersonating({})
        
        return result
      } catch (error: any) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message || 'Не удалось выйти из режима имперсонации'
        })
      }
    }),

  // =====================================================
  // ПРОВЕРКА ПРАВ ДОСТУПА
  // =====================================================

  /**
   * Проверка прав пользователя
   */
  hasPermission: expertProcedure
    .input(z.object({
      permissions: z.record(z.array(z.string()))
    }))
    .query(async ({ input }) => {
      try {
        const result = await auth.api.hasPermission({
          body: {
            permissions: input.permissions
          }
        })
        
        return result
      } catch (error: any) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error.message || 'Не удалось проверить права доступа'
        })
      }
    }),

  // =====================================================
  // ЖУРНАЛ АУДИТА
  // =====================================================

  /**
   * Получение логов административных действий
   */
  getAuditLogs: expertProcedure
    .input(z.object({
      adminId: z.string().optional(),
      action: z.string().optional(),
      targetUserId: z.string().optional(),
      targetUserEmail: z.string().optional(),
      search: z.string().optional(),
      searchField: z.enum(['adminEmail', 'targetUserEmail']).optional(),
      success: z.boolean().optional(),
      dateFrom: z.date().optional(),
      dateTo: z.date().optional(),
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
      sortBy: z.string().default('createdAt'),
      sortDirection: z.enum(['asc', 'desc']).default('desc')
    }))
    .query(async ({ input, ctx }) => {
      // Валидация прав доступа
      if (!validateAdminPermissions(ctx.user, 'getAuditLogs')) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Недостаточно прав для просмотра журнала аудита'
        })
      }

      try {
        const result = await AdminAuditLogger.getAdminLogs({
          adminId: input.adminId,
          action: input.action as AdminActionType,
          targetUserId: input.targetUserId,
          targetUserEmail: input.targetUserEmail,
          search: input.search,
          searchField: input.searchField,
          success: input.success,
          dateFrom: input.dateFrom,
          dateTo: input.dateTo,
          limit: input.limit,
          offset: input.offset,
          sortBy: input.sortBy,
          sortDirection: input.sortDirection
        })

        return result
      } catch (error: any) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось получить журнал аудита'
        })
      }
    }),

  /**
   * Получение статистики административных действий
   */
  getAuditStats: expertProcedure
    .input(z.object({
      adminId: z.string().optional()
    }))
    .query(async ({ input, ctx }) => {
      // Валидация прав доступа
      if (!validateAdminPermissions(ctx.user, 'getAuditStats')) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Недостаточно прав для просмотра статистики аудита'
        })
      }

      try {
        const result = await AdminAuditLogger.getAdminStats(input.adminId)

        return result
      } catch (error: any) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error.message || 'Не удалось получить статистику аудита'
        })
      }
    })
})
