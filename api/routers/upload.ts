import { z } from 'zod';
import { router, authProcedure } from '../trpc';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export const uploadRouter = router({
  // Загрузка изображения схемы
  uploadSchemaImage: authProcedure
    .input(z.object({
      fileName: z.string().min(1),
      fileData: z.string(), // base64 encoded file data
      mimeType: z.string().min(1)
    }))
    .mutation(async ({ input }) => {
      try {
        // Создаем директорию для загрузок, если её нет
        const uploadsDir = join(process.cwd(), 'uploads', 'schema-images');
        if (!existsSync(uploadsDir)) {
          await mkdir(uploadsDir, { recursive: true });
        }

        // Генерируем уникальное имя файла
        const timestamp = Date.now();
        const extension = input.fileName.split('.').pop() || 'png';
        const uniqueFileName = `${timestamp}-${Math.random().toString(36).substring(2)}.${extension}`;
        const filePath = join(uploadsDir, uniqueFileName);

        // Декодируем base64 и сохраняем файл
        const base64Data = input.fileData.replace(/^data:image\/[a-z]+;base64,/, '');
        const buffer = Buffer.from(base64Data, 'base64');
        
        await writeFile(filePath, buffer);

        // Возвращаем URL для доступа к файлу
        const fileUrl = `/api/uploads/schema-images/${uniqueFileName}`;
        
        return {
          success: true,
          url: fileUrl,
          fileName: uniqueFileName
        };
      } catch (error) {
        console.error('Error uploading file:', error);
        throw new Error('Failed to upload file');
      }
    })
});
