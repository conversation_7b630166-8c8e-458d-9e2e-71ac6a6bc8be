

// =====================================================
// ПОЛЬЗОВАТЕЛИ
// =====================================================

enum Role {
    GUEST        // Гость
    USER         // Обычный пользователь (инженер, механик, частное лицо)
    SHOP         // Магазин запчастей
    ADMIN        // Администратор
}

model User {
    id            String    @id @default(uuid()) // Уникальный идентификатор пользователя
    name          String?   // Имя пользователя
    email         String    @unique // Email пользователя
    emailVerified Boolean   @default(false) // Подтвержден ли email
    image         String?   // URL аватара пользователя
    role          Role      @default(USER) // Роль пользователя
    createdAt     DateTime  @default(now()) // Дата создания
    updatedAt     DateTime  @updatedAt // Дата последнего обновления

    // Better-auth связи
    accounts      Account[] // Аккаунты пользователя (OAuth, email/password)
    sessions      Session[] // Сессии пользователя

    // Бизнес связи
    // orders        Order[]   // Заказы пользователя
    // shop          Shop?     // Магазин пользователя (если есть)
    // carts         Cart[]    // Корзины пользователя

    @@allow('read', true)
    @@allow('create,update', auth() != null && auth().id == id)
    @@allow('delete', auth() != null && auth().role == 'ADMIN')
    @@map("user")
}

// =====================================================
// BETTER-AUTH МОДЕЛИ
// =====================================================


model Account {
    id                    String    @id // Уникальный идентификатор аккаунта
    accountId             String    // ID аккаунта у провайдера
    providerId            String    // ID провайдера (google, github, email)
    userId                String    // ID пользователя
    accessToken           String?   // Токен доступа
    refreshToken          String?   // Токен обновления
    idToken               String?   // ID токен
    accessTokenExpiresAt  DateTime? // Срок действия токена доступа
    refreshTokenExpiresAt DateTime? // Срок действия токена обновления
    scope                 String?   // Области доступа
    password              String?   // Хэш пароля (для email/password)
    createdAt             DateTime  // Дата создания
    updatedAt             DateTime  // Дата обновления

    user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade) // Связь с пользователем

    @@allow('read,update', auth() != null && auth().id == userId)
    @@allow('create,delete', true)
    @@map("account")
}

model Session {
    id             String   @id // Уникальный идентификатор сессии
    expiresAt      DateTime // Срок действия сессии
    token          String   @unique // Токен сессии
    createdAt      DateTime // Дата создания
    updatedAt      DateTime // Дата обновления
    ipAddress      String?  // IP адрес
    userAgent      String?  // User Agent
    userId         String   // ID пользователя
    impersonatedBy String? // ID администратора, который выполняет impersonation

    user           User     @relation(fields: [userId], references: [id], onDelete: Cascade) // Связь с пользователем

    @@allow('read', auth() != null && auth().id == userId)
    @@allow('create,update,delete', true)
    @@map("session")
}
