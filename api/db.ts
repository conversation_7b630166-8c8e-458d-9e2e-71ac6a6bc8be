import { PrismaClient } from '@prisma/client'
import { enhance } from '@zenstackhq/runtime'
import type { ExtendedUser } from './types/auth'

const prisma = new PrismaClient({
  log: ["query", "info", "warn", "error"],
});

// Экспорт обычного Prisma клиента для системных операций
export const $prisma = prisma

// Тип для контекста авторизации ZenStack
export interface AuthContext {
  user: ExtendedUser | null
}

/**
 * Создает enhanced Prisma клиент с контекстом пользователя
 * Автоматически применяет правила авторизации из schema.zmodel
 */
export function getEnhancedDB(user: ExtendedUser | null = null) {
  return enhance(prisma, { user });
}

/**
 * Создает публичный DB клиент для неавторизованных пользователей
 * Доступны только данные с правилом @@allow('read', true)
 */
export function getPublicDB() {
  return enhance(prisma, { user: null });
}

/**
 * Системный DB клиент без ограничений авторизации
 * Использовать ТОЛЬКО для внутренних операций (миграции, сиды, админ-панель)
 * ⚠️ ОСТОРОЖНО: Обходит все правила безопасности!
 */
export function getSystemDB() {
  return prisma;
}
