# Каталог идентичности запчастей - PartTec3

## Описание проекта

**PartTec3** - это профессиональная система каталогизации запчастей, предназначенная для создания базы данных взаимозаменяемых деталей от различных производителей. Основная цель проекта - связать физически идентичные или частично совместимые запчасти, узлы и агрегаты, которые производятся под разными брендами и артикулами.

## Ключевые возможности

### 1. Система взаимозаменяемости
- **Группы взаимозаменяемости (Part)** - объединяют физически идентичные детали от разных производителей
- **Точность применимости** - система меток точности (EXACT_MATCH, MATCH_WITH_NOTES, REQUIRES_MODIFICATION, PARTIAL_MATCH)
- **Иерархическая структура** - поддержка сложных узлов и агрегатов (двигатель → блок цилиндров → поршень)

### 2. Гибкая система атрибутов
- **архитектура** - атрибуты могут принадлежать деталям, каталожным позициям или моделям техники
- **Типизированные данные** - поддержка строковых, числовых, булевых значений и JSON
- **Стандартизированные единицы измерения** - от миллиметров до лошадиных сил
- **Группировка атрибутов** - логическое объединение характеристик (например, "Размеры сальника")

### 3. Интерактивные схемы агрегатов
- **SVG-схемы** - визуализация узлов и агрегатов
- **Позиционирование деталей** - точное размещение запчастей на схемах
- **Аннотации** - текстовые пояснения и выделения
- **Интерактивность** - кликабельные элементы схем

### 4. Система ролей и доступа
- **GUEST** - гости (ограниченный доступ)
- **USER** - инженеры, механики, частные лица
- **SHOP** - магазины запчастей
- **ADMIN** - администраторы системы

## Примеры использования

### Пример 1: Сальники
**Сальник Corteco** и **Сальник SKF** с одинаковыми размерами, типом и материалом. Физически полная копия, но:
- Размеры указаны с разной точностью (до тысячных vs до сотых)
- Типы называются по-разному (BABSL vs ABC123)

### Пример 2: Редукторы
**Редуктор комбайна John Deere** и **редуктор экскаватора Komatsu** - физически один агрегат с несколькими отличиями.

### Пример 3: Двигатели
**Двигатель Seat** и **двигатель Volkswagen** - полностью идентичные моторы с разными выпускными коллекторами.

## Технический стек

### Backend
- **Bun** - JavaScript runtime для высокой производительности
- **Hono** - современный веб-фреймворк
- **tRPC** - типобезопасные API с end-to-end типизацией
- **ZenStack** - расширение Prisma с декларативными правилами доступа
- **Prisma** - ORM для работы с базой данных
- **PostgreSQL** - основная база данных

### Аутентификация
- **Better Auth** - современная система аутентификации
- **OAuth** - поддержка внешних провайдеров
- **Email/Password** - классическая аутентификация

### Дополнительные инструменты
- **Zod** - валидация данных и типов

## Архитектурные принципы

### 1. Конструкторный паттерн
Все сущности и их взаимоотношения создаются на основе конструктора, обеспечивая гибкость и расширяемость системы.

### 2. Полиморфная система атрибутов
Использование ZenStack delegation для создания полиморфных связей между атрибутами и различными типами сущностей.

### 3. Иерархическая структура данных
Поддержка древовидных структур для категорий запчастей, групп взаимозаменяемости и схем агрегатов.

### 4. Типобезопасность
End-to-end типизация через tRPC и Zod обеспечивает надежность и предотвращает ошибки на этапе разработки.

## Монетизация

Проект планируется как платный каталог для профессионалов в enterprise-сегменте, где цена ошибки очень велика. Система обеспечивает:

- **Надежность данных** - верифицированная информация от команды инженеров
- **Точность взаимозаменяемости** - детальная система меток точности
- **Профессиональные инструменты** - интерактивные схемы и продвинутый поиск
- **Безопасность** - многоуровневая система доступа и аудита

## Документация

- [ZenStack ZModel Language](https://zenstack.dev/docs/reference/zmodel-language)
- [tRPC Documentation](https://trpc.io/docs)
- [Prisma Documentation](https://www.prisma.io/docs)