import 'catalog_schema'
import 'user_schema'
import 'aggregate_schemas_schema'



generator client {
    provider = "prisma-client-js"
}

plugin zod {
    provider = '@core/zod'
    output = 'generated/zod'
    compile = false
    preserveTsFiles = true
}

plugin trpc {
    provider = '@zenstackhq/trpc'
    output = 'generated/trpc'
    version = 'v11'
    importCreateRouter = '../../../trpc'
    importProcedure = '../../../trpc'
}

datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}