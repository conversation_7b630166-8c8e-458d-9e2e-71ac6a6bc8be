generator client {
  provider = "prisma-client-js"
}

plugin zod {
  provider = '@core/zod'
  output = 'generated/zod'
  compile = false
  preserveTsFiles = true
}

plugin trpc {
  provider = '@zenstackhq/trpc'
  output = 'generated/trpc'
  version = 'v11'
  importCreateRouter = '../../../trpc'
  importProcedure = '../../../trpc'
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// =====================================================
// BETTER-AUTH МОДЕЛИ
// =====================================================

// АККАУНТЫ OAUTH И ПАРОЛИ
model Account {
  id                    String    @id
  accountId             String    // ID аккаунта у провайдера
  providerId            String    // ID провайдера (google, github, email)
  userId                String    // ID пользователя
  accessToken           String?   // Токен доступа
  refreshToken          String?   // Токен обновления
  idToken               String?   // ID токен
  accessTokenExpiresAt  DateTime? // Срок действия токена доступа
  refreshTokenExpiresAt DateTime? // Срок действия токена обновления
  scope                 String?   // Области доступа
  password              String?   // Хэш пароля (для email/password)
  createdAt             DateTime  // Дата создания
  updatedAt             DateTime  // Дата обновления

  // СВЯЗЬ С ПОЛЬЗОВАТЕЛЕМ
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Аккаунты видит только их владелец
  @@allow('read,update', auth() != null && auth().id == userId)
  @@allow('create,delete', true) // Для системных операций better-auth

  @@map("account")
}

// СЕССИИ ПОЛЬЗОВАТЕЛЕЙ
model Session {
  id        String   @id
  expiresAt DateTime // Срок действия сессии
  token     String   @unique // Токен сессии
  createdAt DateTime // Дата создания
  updatedAt DateTime // Дата обновления
  ipAddress String?  // IP адрес
  userAgent String?  // User Agent
  userId    String   // ID пользователя

  // ADMIN PLUGIN ПОЛЯ
  impersonatedBy String? // ID администратора, который выполняет impersonation

  // СВЯЗЬ С ПОЛЬЗОВАТЕЛЕМ
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Сессии видит только их владелец
  @@allow('read', auth() != null && auth().id == userId)
  @@allow('create,update,delete', true) // Для системных операций better-auth

  @@map("session")
}

// ВЕРИФИКАЦИЯ (EMAIL, ТЕЛЕФОН)
model Verification {
  id         String    @id
  identifier String    // Email или телефон
  value      String    // Код верификации
  expiresAt  DateTime  // Срок действия
  createdAt  DateTime? // Дата создания
  updatedAt  DateTime? // Дата обновления

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Верификация - системные операции better-auth
  @@allow('create,read,update,delete', true)

  @@map("verification")
}

// =====================================================
// СПРАВОЧНИКИ И ПЕРЕЧИСЛЕНИЯ
// =====================================================

// Уровень проверки данных (насколько можно доверять информации)
enum VerificationLevel {
  UNVERIFIED   // Данные не проверены - могут быть ошибки
  COMMUNITY    // Подтверждено пользователями - средняя надежность
  EXPERT       // Проверено экспертом - высокая надежность
  OFFICIAL     // Официально подтверждено производителем - максимальная надежность
}

// Тип эквивалентности между запчастями
enum EquivalenceType {
  IDENTICAL    // Полная идентичность - можно заменять без ограничений
  COMPATIBLE   // Совместимость с ограничениями - нужно учитывать особенности
  ALTERNATIVE  // Альтернативная замена - может потребовать доработки
}

// Состояние запчасти
enum PartCondition {
  NEW           // Новая запчасть
  USED          // Б/У запчасть
  REFURBISHED   // Восстановленная
}

// Статус заказа (ОПТИМИЗИРОВАННАЯ СИСТЕМА)
enum OrderStatus {
  // НАЧАЛЬНЫЕ СТАТУСЫ
  DRAFT       // Черновик заказа
  PENDING     // Ожидает обработки

  // ОБРАБОТКА
  CONFIRMED   // Подтвержден
  PROCESSING  // В процессе сборки
  PARTIALLY_AVAILABLE // Частично доступен
  BACKORDERED // Ожидает поступления товара

  // ЗАВЕРШАЮЩИЕ СТАТУСЫ
  COMPLETED   // Завершен
  CANCELLED   // Отменено
  RETURNED    // Возвращен
}

// Тип заказа
enum OrderType {
  REGULAR     // Обычный заказ
  PREORDER    // Предзаказ
  QUOTE       // Запрос котировки
  EMERGENCY   // Срочный заказ
  BULK        // Оптовый заказ
}



// Тип номера детали
enum NumberType {
  OEM         // Оригинальный номер производителя техники
  AFTERMARKET // Номер производителя аналогов
  INTERNAL    // Внутренний номер каталога
  SUPERSEDED  // Замененный номер (уже не используется)
}

// Основные категории запчастей (расширенная система по стандарту PIES)
enum PartCategory {
  // СИЛОВАЯ УСТАНОВКА
  ENGINE        // Двигатель
  TRANSMISSION  // Трансмиссия
  DRIVETRAIN    // Трансмиссия и привод
  FUEL_SYSTEM   // Топливная система
  EXHAUST       // Выхлопная система
  COOLING       // Система охлаждения

  // ХОДОВАЯ ЧАСТЬ
  SUSPENSION    // Подвеска
  STEERING      // Рулевое управление
  BRAKES        // Тормозная система
  WHEELS_TIRES  // Колеса и шины

  // ГИДРАВЛИКА И ПНЕВМАТИКА
  HYDRAULICS    // Гидравлика
  PNEUMATICS    // Пневматика

  // ЭЛЕКТРООБОРУДОВАНИЕ
  ELECTRONICS   // Электроника
  ELECTRICAL    // Электрооборудование
  LIGHTING      // Освещение

  // РАСХОДНЫЕ МАТЕРИАЛЫ
  FILTERS       // Фильтры
  FLUIDS        // Жидкости и масла
  BELTS_HOSES   // Ремни и шланги

  // КРЕПЕЖ И УПЛОТНЕНИЯ
  FASTENERS     // Крепежные элементы
  SEALS         // Уплотнения
  BEARINGS      // Подшипники

  // КУЗОВ И ИНТЕРЬЕР
  BODY          // Кузовные детали
  INTERIOR      // Интерьер
  GLASS         // Стекла

  // СПЕЦИАЛЬНОЕ ОБОРУДОВАНИЕ
  ATTACHMENTS   // Навесное оборудование
  IMPLEMENTS    // Рабочие органы

  OTHER         // Прочее
}

// Типы применимости деталей (по стандарту ACES)
enum ApplicationType {
  UNIVERSAL       // Универсальная деталь
  VEHICLE_SPECIFIC // Специфична для модели техники
  ENGINE_SPECIFIC  // Специфична для двигателя
  YEAR_SPECIFIC    // Специфична для года выпуска
  OPTION_SPECIFIC  // Специфична для комплектации
}

// Типы кросс-референсов
enum CrossReferenceType {
  OEM_TO_AFTERMARKET  // OEM к аналогу
  AFTERMARKET_TO_OEM  // Аналог к OEM
  SUPERSESSION        // Замена (новый номер)
  INTERCHANGE         // Взаимозаменяемость
  COMPETITIVE         // Конкурентный аналог
}

// Роли пользователей в системе (УПРОЩЕННАЯ СИСТЕМА - СОВМЕСТИМАЯ С БД)
enum Role {
  USER         // Обычный пользователь (инженер, механик, частное лицо)
  SHOP         // Магазин запчастей
  SERVICE      // Сервисный центр
}

// Типы бизнес-аккаунтов (УПРОЩЕННАЯ СИСТЕМА)
enum BusinessType {
  // B2C СЕГМЕНТ
  INDIVIDUAL      // Частное лицо
  FLEET_OPERATOR  // Управляющий автопарком
  DEALER          // Официальный дилер

  // ПОСТАВЩИКИ
  RETAIL_SHOP     // Розничный магазин
  WHOLESALE       // Оптовый поставщик
  DISTRIBUTOR_COMPANY // Дистрибьюторская компания
  OEM_MANUFACTURER    // Производитель оригинальных запчастей
  AFTERMARKET_MANUFACTURER // Производитель аналогов
}

// =====================================================
// КАТАЛОЖНАЯ ЧАСТЬ - ТЕХНИЧЕСКАЯ СТРУКТУРА
// =====================================================

// КАТЕГОРИИ ЗАПЧАСТЕЙ (РАСШИРЕННАЯ МОДЕЛЬ ПО СТАНДАРТУ PIES)
// Иерархическая структура для группировки запчастей по типам
// Пример: Двигатель -> Топливная система -> Форсунки
model Category {
  id          String      @id @default(uuid())
  name        String      @length(1, 200) // Название категории: "Гидравлические насосы"
  slug        String      @unique @regex("^[a-z0-9-]+$") @length(1, 100) // URL-совместимое имя: "hydraulic-pumps"
  description String?     @length(0, 1000) // Описание категории

  // СТАНДАРТИЗИРОВАННАЯ ИЕРАРХИЯ (PIES)
  level       Int         @default(1) @gte(1) @lte(4) // Уровень в иерархии (1-4)
  piesCode    String?     @length(1, 20) // Стандартный код PIES (если применимо)

  // ИЕРАРХИЯ: категория может быть вложена в другую категорию
  parent      Category?   @relation("CategoryToParent", fields: [parentId], references: [id])
  parentId    String?     // ID родительской категории
  children    Category[]  @relation("CategoryToParent") // Дочерние категории

  // ДОПОЛНИТЕЛЬНЫЕ МЕТАДАННЫЕ
  isStandard  Boolean     @default(false) // Стандартная категория PIES
  sortOrder   Int         @default(0) // Порядок сортировки в группе
  isActive    Boolean     @default(true) // Активна ли категория

  // Связь с узлами и запчастями
  units       Unit[]      // Узлы в этой категории
  parts       Part[]      // Запчасти в этой категории

  // СВЯЗЬ С АТРИБУТАМИ
  attributes  CategoryAttribute[] // Атрибуты, доступные в этой категории

  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Категории - публичное чтение для всех
  @@allow('read', true)

  // Создание, изменение и удаление только для авторизованных пользователей
  @@allow('create,update,delete', auth() != null)

  @@index([parentId])
  @@index([level])
  @@index([piesCode])
  @@index([isStandard])
  @@index([sortOrder])
  // СОСТАВНЫЕ ИНДЕКСЫ
  @@index([parentId, level])
  @@index([level, sortOrder])
  @@index([isStandard, level])
  @@map("category")
}

// БРЕНДЫ ПРОИЗВОДИТЕЛЕЙ
// Включает как производителей техники (CAT, Komatsu), так и производителей запчастей
model Brand {
  id          String       @id @default(uuid())
  name        String       @length(1, 100) // Название бренда: "Caterpillar", "Komatsu"
  slug        String       @unique @regex("^[a-z0-9-]+$") @length(1, 100) // URL-совместимое имя
  country     String?      @length(2, 100) // Страна производителя
  isOem       Boolean      @default(false) // true = производитель техники, false = производитель запчастей
  
  // Связи с другими сущностями
  parts       Part[]             // Запчасти этого бренда
  partNumbers PartNumber[]       // Номера деталей этого бренда
  equipmentModels EquipmentModel[] // Модели техники этого бренда
  
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Бренды - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  @@index([isOem])
  @@map("brand")
}

// МОДЕЛИ ТЕХНИКИ
// Конкретные модели машин: CAT 320D, Komatsu PC200-8
model EquipmentModel {
  id          String     @id @default(uuid())
  name        String     @length(1, 100) // Название модели: "CAT 320D", "Komatsu PC200-8"
  brand       Brand?     @relation(fields: [brandId], references: [id])
  brandId     String?    // К какому бренду относится
  yearFrom    Int?       @gte(1900) @lte(2100) // Год начала производства
  yearTo      Int?       @gte(1900) @lte(2100) // Год окончания производства
  category    String?    @length(1, 100) // Тип техники: "Экскаватор", "Бульдозер"

  // Связи
  partCompatibility PartCompatibility[] // Какие запчасти подходят к этой модели
  unitCompatibility UnitCompatibility[] // Какие узлы используются в этой модели

  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Модели техники - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@index([brandId])
  @@map("equipment_model")
}

// УЗЛЫ И АГРЕГАТЫ
// Группируют запчасти по функциональному назначению
// Пример: "Гидравлическая система", "Двигатель", "Топливная система"
model Unit {
  id            String     @id @default(uuid())
  name          String     @length(1, 200) // Название узла: "Гидравлическая система", "Двигатель"
  description   String?    @length(1, 1000) // Подробное описание узла
  specifications Json?     // Общие характеристики узла
  
  // КАТЕГОРИЯ УЗЛА
  category      Category?  @relation(fields: [categoryId], references: [id])
  categoryId    String?    // К какой категории относится узел
  
  // ИЕРАРХИЯ УЗЛОВ - ВАЖНО!
  // Позволяет создать структуру: Двигатель -> Топливная система -> Система впрыска
  parent        Unit?      @relation("UnitHierarchy", fields: [parentId], references: [id])
  parentId      String?    // ID родительского узла
  children      Unit[]     @relation("UnitHierarchy") // Дочерние узлы
  level         Int        @default(0) // Уровень вложенности: 0=основной узел, 1=подузел, 2=подподузел
  
  // ВЕРИФИКАЦИЯ ДАННЫХ
  verificationLevel VerificationLevel // Насколько проверены данные
  verifiedBy    String?    // Кто проверил
  verifiedAt    DateTime?  // Когда проверено
  
  // СВЯЗИ С ДРУГИМИ СУЩНОСТЯМИ
  parts         Part[]            // Запчасти, входящие в этот узел
  compatibility UnitCompatibility[] // Совместимость с техникой
  schemas       AggregateSchema[] // Схемы агрегатов для этого узла

  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Узлы - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@index([parentId])
  @@index([level])
  @@index([name])
  @@index([categoryId])
  @@index([verificationLevel])
  // СОСТАВНЫЕ ИНДЕКСЫ ДЛЯ ПРОИЗВОДИТЕЛЬНОСТИ
  @@index([categoryId, level])
  @@index([parentId, level])
  @@index([verificationLevel, categoryId])
  @@map("unit")
}

// ЗАПЧАСТИ
// КЛЮЧЕВАЯ ТАБЛИЦА: конкретные запчасти (фильтры, подшипники, манжеты и т.д.)
model Part {
  id            String     @id @default(uuid())
  name          String     @length(1, 300) // Название запчасти: "Фильтр масляный", "Подшипник роликовый"
  description   String?    @length(1, 2000) // Подробное описание
  specifications Json?     // Технические характеристики: {диаметр: 50, высота: 100}


  
  // КАТЕГОРИЯ ЗАПЧАСТИ
  category      Category?  @relation(fields: [categoryId], references: [id])
  categoryId    String?    // К какой категории относится
  
  // ПРИНАДЛЕЖНОСТЬ К УЗЛУ
  unit          Unit?      @relation(fields: [unitId], references: [id])
  unitId        String?    // К какому узлу относится эта запчасть (опционально)
  
  // ПРОИЗВОДИТЕЛЬ ЗАПЧАСТИ (если известен)
  brand         Brand?     @relation(fields: [brandId], references: [id])
  brandId       String?    // Кто производит эту запчасть
  
  // ВЕРИФИКАЦИЯ ДАННЫХ
  verificationLevel VerificationLevel // Насколько проверены данные
  verifiedBy    String?    // Кто проверил
  verifiedAt    DateTime?  // Когда проверено
  
  // СВЯЗИ С ДРУГИМИ СУЩНОСТЯМИ
  partNumbers   PartNumber[]        // Все номера этой запчасти
  equivalents   PartEquivalent[] @relation("PartToEquivalent")   // Эквиваленты
  equivalentOf  PartEquivalent[] @relation("EquivalentToPart")   // Обратные эквиваленты
  compatibility PartCompatibility[] // Совместимость с техникой
  numberGroups  NumberGroup[]      // Группы номеров
  catalogParts  CatalogPart[]      // Каталожные позиции

  // НОВЫЕ СВЯЗИ ДЛЯ ГРУПП ВЗАИМОЗАМЕНЯЕМОСТИ
  masterOfGroups InterchangeabilityGroup[] @relation("MasterPartGroup") // Группы, где эта запчасть является основной
  groupMemberships InterchangeabilityGroupMember[] // Членство в группах взаимозаменяемости

  // СВЯЗЬ С АТРИБУТАМИ
  attributeValues PartAttributeValue[] // Значения атрибутов этой запчасти
  standardizedAttributes StandardizedPartAttribute[] // Стандартизированные атрибуты

  // СВЯЗИ С ПРЕДЛОЖЕНИЯМИ ПОЛЬЗОВАТЕЛЕЙ
  suggestions     UserSuggestion[] @relation("SuggestionPart") // Предложения, где эта запчасть основная
  equivalentSuggestions UserSuggestion[] @relation("SuggestionEquivalent") // Предложения, где эта запчасть эквивалентная

  // СВЯЗИ СО СХЕМАМИ АГРЕГАТОВ
  schemaPositions SchemaPosition[] // Позиции этой запчасти на схемах агрегатов

  // СВЯЗЬ С РАЗМЕРНЫМ ПОИСКОМ
  dimensionSearch PartDimensionSearch? // Денормализованные размерные характеристики для поиска

  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Каталог запчастей - публичное чтение
  @@allow('read', true)

  // Создавать предложения могут авторизованные пользователи
  @@allow('create', auth() != null)

  // Изменять могут только пользователи с ролью SERVICE (эксперты)
  @@allow('update', auth() != null && auth().role == 'SERVICE')

  @@index([unitId])
  @@index([name])
  @@index([categoryId])
  @@index([brandId])
  @@index([verificationLevel])
  @@map("part")
}

// НОМЕРА ЗАПЧАСТЕЙ
// КРИТИЧЕСКИ ВАЖНАЯ ТАБЛИЦА: хранит все номера (OEM, аналоги) для каждой запчасти
model PartNumber {
  id          String     @id @default(uuid())
  number      String     @length(1, 100) @regex("^[A-Za-z0-9\\-\\.\\s]+$") // Номер детали: "CAT-1234", "32A4000100"
  numberType  NumberType @default(OEM) // Тип номера
  
  // БРЕНД НОМЕРА
  brand       Brand?     @relation(fields: [brandId], references: [id])
  brandId     String?    // Чей это номер (CAT, Komatsu, и т.д.)
  isOriginal  Boolean    @default(false) // Оригинальный номер или аналог
  
  // СВЯЗЬ С ЗАПЧАСТЬЮ - ОСНОВНАЯ СВЯЗЬ
  part        Part       @relation(fields: [partId], references: [id])
  partId      String     // К какой запчасти относится
  
  // ДОПОЛНИТЕЛЬНАЯ ИНФОРМАЦИЯ
  description String?    // Описание применимости
  isActive    Boolean    @default(true) // Активен ли номер
  supersededBy String?   // Номер, которым заменен (если устарел)
  
  // СВЯЗИ
  numberGroups NumberGroupItem[] // Группы эквивалентных номеров
  catalogParts CatalogPart[]     // Каталожные позиции с этим номером

  // КРОСС-РЕФЕРЕНСЫ
  sourceCrossReferences PartNumberCrossReference[] @relation("SourceCrossReference") // Исходящие кросс-референсы
  targetCrossReferences PartNumberCrossReference[] @relation("TargetCrossReference") // Входящие кросс-референсы
  
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Номера запчастей - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@unique([number, brandId]) // Один номер у одного бренда
  @@index([partId])
  @@index([number])
  @@index([brandId])
  @@index([numberType])
  @@index([isActive])
  // СОСТАВНЫЕ ИНДЕКСЫ ДЛЯ ПОИСКА
  @@index([number, numberType])
  @@index([brandId, numberType])
  @@index([partId, isActive])
  @@index([isOriginal, brandId])
  @@map("part_number")
}

// ЭКВИВАЛЕНТНОСТЬ ЗАПЧАСТЕЙ
// Связывает взаимозаменяемые запчасти
model PartEquivalent {
  id              String     @id @default(uuid())
  
  // ОСНОВНАЯ ЗАПЧАСТЬ
  part            Part       @relation("PartToEquivalent", fields: [partId], references: [id])
  partId          String
  
  // ЭКВИВАЛЕНТНАЯ ЗАПЧАСТЬ
  equivalentPart  Part       @relation("EquivalentToPart", fields: [equivalentPartId], references: [id])
  equivalentPartId String
  
  // ХАРАКТЕРИСТИКИ ЭКВИВАЛЕНТНОСТИ
  confidence      Float      @default(1.0) // Уверенность в эквивалентности (0.0-1.0)
  equivalenceType EquivalenceType @default(IDENTICAL) // Тип эквивалентности
  
  // ВЕРИФИКАЦИЯ
  verifiedBy      String?
  verificationLevel VerificationLevel
  notes           String?    // Примечания об ограничениях
  
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Эквивалентность запчастей - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@unique([partId, equivalentPartId])
  @@map("part_equivalent")
}

// ГРУППЫ ВЗАИМОЗАМЕНЯЕМОСТИ
// НОВАЯ КОНЦЕПЦИЯ: Объединяет все взаимозаменяемые запчасти в единую группу
// Пример: Все фильтры масляные для CAT 3306 (оригинал + все аналоги)
model InterchangeabilityGroup {
  id          String       @id @default(uuid())
  name        String       // Название группы: "Фильтр масляный CAT 3306 - группа взаимозаменяемости"
  description String?      // Описание группы и ограничений

  // ОСНОВНАЯ ЗАПЧАСТЬ (обычно OEM)
  masterPart  Part?        @relation("MasterPartGroup", fields: [masterPartId], references: [id])
  masterPartId String?     // Основная (эталонная) запчасть в группе

  // ХАРАКТЕРИСТИКИ ГРУППЫ
  equivalenceType EquivalenceType @default(IDENTICAL) // Общий тип эквивалентности
  confidence      Float      @default(1.0) // Общая уверенность в группе

  // ВЕРИФИКАЦИЯ
  verificationLevel VerificationLevel @default(UNVERIFIED)
  verifiedBy      String?    // Кто проверил группу
  verifiedAt      DateTime?  // Когда проверено

  // СВЯЗИ
  parts           InterchangeabilityGroupMember[] // Все запчасти в группе
  numberGroups    NumberGroup[] // Группы номеров, относящиеся к этой группе

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Группы взаимозаменяемости - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@index([masterPartId])
  @@index([equivalenceType])
  @@index([verificationLevel])
  @@map("interchangeability_group")
}

// ЧЛЕНЫ ГРУППЫ ВЗАИМОЗАМЕНЯЕМОСТИ
// Связывает конкретные запчасти с группами взаимозаменяемости
model InterchangeabilityGroupMember {
  id              String     @id @default(uuid())

  // ГРУППА
  group           InterchangeabilityGroup @relation(fields: [groupId], references: [id])
  groupId         String

  // ЗАПЧАСТЬ
  part            Part       @relation(fields: [partId], references: [id])
  partId          String

  // ХАРАКТЕРИСТИКИ ЧЛЕНСТВА
  isMaster        Boolean    @default(false) // Является ли основной в группе
  confidence      Float      @default(1.0)   // Уверенность в принадлежности к группе
  equivalenceType EquivalenceType @default(IDENTICAL) // Тип эквивалентности с группой

  // ОГРАНИЧЕНИЯ И ПРИМЕЧАНИЯ
  restrictions    String?    // Ограничения по применению
  notes           String?    // Дополнительные примечания

  // ВЕРИФИКАЦИЯ
  verifiedBy      String?
  verifiedAt      DateTime?

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Члены групп взаимозаменяемости - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@unique([groupId, partId])
  @@index([partId])
  @@index([isMaster])
  @@map("interchangeability_group_member")
}

// ГРУППЫ ЭКВИВАЛЕНТНЫХ НОМЕРОВ (ОБНОВЛЕННАЯ)
// Объединяет номера, которые обозначают одну и ту же запчасть
// Теперь связана с группами взаимозаменяемости
model NumberGroup {
  id          String       @id @default(uuid())
  name        String       // Название группы: "Фильтр масляный CAT 3306"
  description String?      // Описание группы

  // СВЯЗЬ С ЗАПЧАСТЬЮ
  part        Part         @relation(fields: [partId], references: [id])
  partId      String       // К какой запчасти относится группа

  // СВЯЗЬ С ГРУППОЙ ВЗАИМОЗАМЕНЯЕМОСТИ
  interchangeabilityGroup InterchangeabilityGroup? @relation(fields: [interchangeabilityGroupId], references: [id])
  interchangeabilityGroupId String? // К какой группе взаимозаменяемости относится

  // НОМЕРА В ГРУППЕ
  numbers     NumberGroupItem[] // Все номера в этой группе

  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Группы номеров - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@index([interchangeabilityGroupId])
  @@map("number_group")
}

// ЭЛЕМЕНТЫ ГРУППЫ НОМЕРОВ
// Связывает конкретные номера с группами эквивалентных номеров
model NumberGroupItem {
  id            String      @id @default(uuid())
  
  // ГРУППА
  group         NumberGroup @relation(fields: [groupId], references: [id])
  groupId       String
  
  // НОМЕР В ГРУППЕ
  partNumber    PartNumber  @relation(fields: [partNumberId], references: [id])
  partNumberId  String
  
  isPrimary     Boolean     @default(false) // Основной номер в группе (обычно OEM)

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Элементы групп номеров - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@unique([groupId, partNumberId])
  @@map("number_group_item")
}

// СОВМЕСТИМОСТЬ УЗЛОВ С ТЕХНИКОЙ
// Определяет, в какой технике используется узел
model UnitCompatibility {
  id              String     @id @default(uuid())
  
  // УЗЕЛ
  unit            Unit       @relation(fields: [unitId], references: [id])
  unitId          String
  
  // МОДЕЛЬ ТЕХНИКИ
  equipmentModel  EquipmentModel @relation(fields: [equipmentModelId], references: [id])
  equipmentModelId String
  
  // ПЕРИОД ПРИМЕНИМОСТИ
  yearFrom        Int?       // С какого года применяется
  yearTo          Int?       // До какого года применяется
  
  // ДОПОЛНИТЕЛЬНАЯ ИНФОРМАЦИЯ
  notes           String?    // Примечания по совместимости
  
  createdAt       DateTime   @default(now())

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Совместимость узлов - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@unique([unitId, equipmentModelId])
  @@map("unit_compatibility")
}

// СОВМЕСТИМОСТЬ ЗАПЧАСТЕЙ С ТЕХНИКОЙ (РАСШИРЕННАЯ МОДЕЛЬ ПО СТАНДАРТУ ACES)
// Определяет, на какую технику устанавливается конкретная запчасть и в какие годы
model PartCompatibility {
  id              String     @id @default(uuid())

  // ЗАПЧАСТЬ
  part            Part       @relation(fields: [partId], references: [id])
  partId          String

  // МОДЕЛЬ ТЕХНИКИ
  equipmentModel  EquipmentModel @relation(fields: [equipmentModelId], references: [id])
  equipmentModelId String

  // ПЕРИОД ПРИМЕНИМОСТИ
  yearFrom        Int?       // С какого года применяется
  yearTo          Int?       // До какого года применяется

  // РАСШИРЕННЫЕ КВАЛИФИКАТОРЫ (ACES)
  applicationType ApplicationType @default(VEHICLE_SPECIFIC) // Тип применимости
  position        String?    // Позиция установки: "передняя ось", "задний мост"
  engineCode      String?    // Код двигателя
  transmissionType String?   // Тип трансмиссии
  driveType       String?    // Тип привода (2WD, 4WD, AWD)
  bodyStyle       String?    // Тип кузова
  fuelType        String?    // Тип топлива

  // ДОПОЛНИТЕЛЬНЫЕ ОГРАНИЧЕНИЯ
  serialNumberFrom String?   // Серийный номер от
  serialNumberTo   String?   // Серийный номер до
  productionDate   DateTime? // Дата производства

  // МЕТАДАННЫЕ
  confidence      Float      @default(1.0) @gte(0.0) @lte(1.0) // Уверенность в совместимости
  notes           String?    // Примечания по совместимости
  source          String?    // Источник информации

  // ВЕРИФИКАЦИЯ
  verificationLevel VerificationLevel @default(UNVERIFIED)
  verifiedBy      String?    // Кто проверил
  verifiedAt      DateTime?  // Когда проверено

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Совместимость запчастей - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@unique([partId, equipmentModelId, position, engineCode]) // Уникальность с учетом квалификаторов
  @@index([applicationType])
  @@index([engineCode])
  @@index([transmissionType])
  @@index([confidence])
  @@index([verificationLevel])
  @@map("part_compatibility")
}

// КАТАЛОЖНЫЕ ПОЗИЦИИ
// Коммерческие позиции в каталоге с основным номером и характеристиками
model CatalogPart {
  id           String       @id @default(uuid())
  name         String       // Коммерческое название: "Фильтр масляный для CAT 320D"
  slug         String       @unique // URL-совместимое имя
  description  String?      // Коммерческое описание
  category     PartCategory // Основная категория для фильтрации
  
  // СВЯЗЬ С ЗАПЧАСТЬЮ - КЛЮЧЕВАЯ СВЯЗЬ!
  part         Part         @relation(fields: [partId], references: [id])
  partId       String       // Какая именно запчасть
  
  // ОСНОВНОЙ НОМЕР ДЛЯ ЭТОЙ КАТАЛОЖНОЙ ПОЗИЦИИ
  primaryPartNumber PartNumber? @relation(fields: [primaryPartNumberId], references: [id])
  primaryPartNumberId String?   // Главный номер по каталогу
  
  attributes   Json?        // Дополнительные коммерческие характеристики
  
  // СВЯЗИ
  shopParts    ShopPart[]   // Предложения в магазинах
  cartItems    CartItem[]   // Позиции в корзинах
  
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Каталожные позиции - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@index([partId])
  @@index([primaryPartNumberId])
  @@map("catalog_part")
}

// КРОСС-РЕФЕРЕНСЫ НОМЕРОВ ДЕТАЛЕЙ
// Система перекрестных ссылок между номерами (OEM ↔ Aftermarket, замены и т.д.)
model PartNumberCrossReference {
  id              String     @id @default(uuid())

  // ИСХОДНЫЙ НОМЕР
  sourcePartNumber PartNumber @relation("SourceCrossReference", fields: [sourcePartNumberId], references: [id])
  sourcePartNumberId String

  // ЦЕЛЕВОЙ НОМЕР
  targetPartNumber PartNumber @relation("TargetCrossReference", fields: [targetPartNumberId], references: [id])
  targetPartNumberId String

  // ТИП КРОСС-РЕФЕРЕНСА
  referenceType   CrossReferenceType // Тип связи между номерами
  confidence      Float      @default(1.0) @gte(0.0) @lte(1.0) // Уверенность в соответствии

  // ДОПОЛНИТЕЛЬНАЯ ИНФОРМАЦИЯ
  notes           String?    // Примечания о соответствии
  restrictions    String?    // Ограничения применимости
  effectiveDate   DateTime?  // Дата вступления в силу

  // ВЕРИФИКАЦИЯ
  verificationLevel VerificationLevel @default(UNVERIFIED)
  verifiedBy      String?    // Кто проверил
  verifiedAt      DateTime?  // Когда проверено
  source          String?    // Источник информации

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Кросс-референсы - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@unique([sourcePartNumberId, targetPartNumberId, referenceType])
  @@index([referenceType])
  @@index([confidence])
  @@index([verificationLevel])
  @@index([effectiveDate])
  @@map("part_number_cross_reference")
}

// СТАНДАРТИЗИРОВАННЫЕ АТРИБУТЫ ДЕТАЛЕЙ (РАСШИРЕННАЯ СИСТЕМА)
// Расширенная система атрибутов с типизацией и единицами измерения
model StandardizedPartAttribute {
  id              String     @id @default(uuid())

  // СВЯЗЬ С ДЕТАЛЬЮ
  part            Part       @relation(fields: [partId], references: [id])
  partId          String

  // ОПРЕДЕЛЕНИЕ АТРИБУТА
  attribute       AttributeDefinition @relation(fields: [attributeId], references: [id])
  attributeId     String

  // ОСНОВНЫЕ ЗНАЧЕНИЯ (типизированные)
  stringValue     String?    // Строковое значение
  numberValue     Float?     // Числовое значение
  booleanValue    Boolean?   // Булево значение
  dateValue       DateTime?  // Дата/время
  jsonValue       Json?      // Сложные структурированные данные

  // РАСШИРЕННЫЕ ПОЛЯ ДЛЯ РАЗМЕРНЫХ ХАРАКТЕРИСТИК
  minValue        Float?     // Минимальное значение (для диапазонов)
  maxValue        Float?     // Максимальное значение (для диапазонов)
  nominalValue    Float?     // Номинальное значение

  // МЕТАДАННЫЕ ЗНАЧЕНИЯ
  unit            String?    // Единица измерения
  precision       Int?       // Точность для числовых значений
  tolerance       Float?     // Допуск для числовых значений
  toleranceType   String?    // Тип допуска: "±", "+/-", "unilateral"

  // НОРМАЛИЗАЦИЯ ДЛЯ ПОИСКА
  normalizedValue Float?     // Нормализованное значение в базовых единицах
  normalizedMin   Float?     // Нормализованное минимальное значение
  normalizedMax   Float?     // Нормализованное максимальное значение

  // КОНТЕКСТ ПРИМЕНЕНИЯ
  context         String?    // Контекст применения атрибута
  conditions      String?    // Условия, при которых применим атрибут

  // ВЕРИФИКАЦИЯ
  verificationLevel VerificationLevel @default(UNVERIFIED)
  verifiedBy      String?    // Кто проверил
  verifiedAt      DateTime?  // Когда проверено

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Атрибуты деталей - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@unique([partId, attributeId, context]) // Уникальность с учетом контекста
  @@index([attributeId])
  @@index([stringValue])
  @@index([numberValue])
  @@index([minValue])
  @@index([maxValue])
  @@index([nominalValue])
  @@index([normalizedValue])
  @@index([normalizedMin])
  @@index([normalizedMax])
  @@index([unit])
  @@index([verificationLevel])
  // СОСТАВНЫЕ ИНДЕКСЫ ДЛЯ РАЗМЕРНОГО ПОИСКА
  @@index([attributeId, normalizedValue])
  @@index([attributeId, normalizedMin, normalizedMax])
  @@index([partId, attributeId, normalizedValue])
  @@map("standardized_part_attribute")
}

// =====================================================
// СХЕМЫ АГРЕГАТОВ (EXPLODED DIAGRAMS)
// =====================================================

// СХЕМЫ АГРЕГАТОВ
// Интерактивные схемы разборки узлов с позициями деталей
model AggregateSchema {
  id            String     @id @default(uuid())
  name          String     @length(1, 200) // Название схемы: "Схема разборки гидронасоса"
  slug          String     @unique @regex("^[a-z0-9-]+$") @length(1, 100) // URL-совместимое имя
  description   String?    @length(1, 1000) // Описание схемы

  // СВЯЗЬ С УЗЛОМ
  unit          Unit       @relation(fields: [unitId], references: [id])
  unitId        String     // К какому узлу относится схема

  // ГРАФИЧЕСКИЕ ДАННЫЕ
  svgContent    String?    @length(1, 100000) // SVG код схемы (если есть)
  imageUrl      String?    @length(1, 500) // URL изображения схемы (fallback)
  imageWidth    Int?       @gte(100) @lte(10000) // Ширина изображения в пикселях
  imageHeight   Int?       @gte(100) @lte(10000) // Высота изображения в пикселях

  // МЕТАДАННЫЕ СХЕМЫ
  version       String     @default("1.0") @length(1, 20) // Версия схемы
  isActive      Boolean    @default(true) // Активна ли схема
  isDefault     Boolean    @default(false) // Схема по умолчанию для узла

  // ВЕРИФИКАЦИЯ
  verificationLevel VerificationLevel @default(UNVERIFIED)
  verifiedBy    String?    // Кто проверил схему
  verifiedAt    DateTime?  // Когда проверено

  // СВЯЗИ
  positions     SchemaPosition[] // Позиции деталей на схеме
  variants      SchemaVariant[]  // Варианты исполнения схемы
  annotations   SchemaAnnotation[] // Аннотации на схеме
  layers        SchemaLayer[]    // Слои схемы

  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Схемы - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@index([unitId])
  @@index([isActive])
  @@index([isDefault])
  @@index([verificationLevel])
  @@index([slug])
  // СОСТАВНЫЕ ИНДЕКСЫ
  @@index([unitId, isActive])
  @@index([unitId, isDefault])
  @@map("aggregate_schema")
}

// ПОЗИЦИИ ДЕТАЛЕЙ НА СХЕМЕ
// Определяет расположение и характеристики деталей на схеме агрегата
model SchemaPosition {
  id              String     @id @default(uuid())

  // СВЯЗЬ СО СХЕМОЙ
  schema          AggregateSchema @relation(fields: [schemaId], references: [id], onDelete: Cascade)
  schemaId        String     // К какой схеме относится

  // СВЯЗЬ С ЗАПЧАСТЬЮ
  part            Part       @relation(fields: [partId], references: [id])
  partId          String     // Какая запчасть

  // ПОЗИЦИЯ НА СХЕМЕ
  positionNumber  String     @length(1, 10) // Номер позиции на схеме: "1", "2A", "15"
  x               Float      @gte(0) @lte(100) // Координата X в процентах (0-100)
  y               Float      @gte(0) @lte(100) // Координата Y в процентах (0-100)

  // ХАРАКТЕРИСТИКИ ПОЗИЦИИ
  quantity        Int        @default(1) @gte(1) // Количество деталей в этой позиции
  isRequired      Boolean    @default(true) // Обязательная ли деталь
  isHighlighted   Boolean    @default(false) // Выделена ли позиция (важная деталь)

  // ДОПОЛНИТЕЛЬНАЯ ИНФОРМАЦИЯ
  notes           String?    @length(1, 500) // Примечания к позиции
  installationOrder Int?     @gte(1) // Порядок установки (для сборки)

  // СВЯЗЬ С ВАРИАНТАМИ И СЛОЯМИ
  variant         SchemaVariant? @relation(fields: [variantId], references: [id])
  variantId       String?    // К какому варианту относится (null = общая для всех)

  layer           SchemaLayer? @relation(fields: [layerId], references: [id])
  layerId         String?    // К какому слою относится позиция

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Позиции - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update,delete', auth() != null)

  @@unique([schemaId, positionNumber, variantId]) // Уникальный номер позиции в схеме/варианте
  @@index([schemaId])
  @@index([partId])
  @@index([variantId])
  @@index([layerId])
  @@index([positionNumber])
  @@index([isRequired])
  @@index([isHighlighted])
  // СОСТАВНЫЕ ИНДЕКСЫ
  @@index([schemaId, variantId])
  @@index([schemaId, isRequired])
  @@index([partId, schemaId])
  @@index([layerId, isHighlighted])
  @@map("schema_position")
}

// ВАРИАНТЫ ИСПОЛНЕНИЯ СХЕМ
// Позволяет иметь разные варианты одной схемы (стандартное/северное исполнение и т.д.)
model SchemaVariant {
  id            String     @id @default(uuid())

  // СВЯЗЬ СО СХЕМОЙ
  schema        AggregateSchema @relation(fields: [schemaId], references: [id], onDelete: Cascade)
  schemaId      String     // К какой схеме относится

  // ХАРАКТЕРИСТИКИ ВАРИАНТА
  name          String     @length(1, 100) // Название варианта: "Стандартное исполнение"
  slug          String     @length(1, 100) @regex("^[a-z0-9-]+$") // URL-совместимое имя
  description   String?    @length(1, 500) // Описание варианта

  // НАСТРОЙКИ ВАРИАНТА
  isDefault     Boolean    @default(false) // Вариант по умолчанию
  isActive      Boolean    @default(true) // Активен ли вариант
  displayOrder  Int        @default(0) // Порядок отображения

  // ГРАФИЧЕСКИЕ ДАННЫЕ (если отличаются от основной схемы)
  svgContent    String?    @length(1, 100000) // Специфичный SVG для варианта
  imageUrl      String?    @length(1, 500) // Специфичное изображение для варианта

  // СВЯЗИ
  positions     SchemaPosition[] // Позиции, специфичные для этого варианта
  annotations   SchemaAnnotation[] // Аннотации, специфичные для этого варианта

  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Варианты - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update,delete', auth() != null)

  @@unique([schemaId, slug]) // Уникальный slug в рамках схемы
  @@index([schemaId])
  @@index([isDefault])
  @@index([isActive])
  @@index([displayOrder])
  // СОСТАВНЫЕ ИНДЕКСЫ
  @@index([schemaId, isActive])
  @@index([schemaId, isDefault])
  @@index([schemaId, displayOrder])
  @@map("schema_variant")
}

// АННОТАЦИИ НА СХЕМАХ
// Текстовые и графические аннотации для схем агрегатов
model SchemaAnnotation {
  id              String     @id @default(uuid())

  // СВЯЗЬ СО СХЕМОЙ
  schema          AggregateSchema @relation(fields: [schemaId], references: [id], onDelete: Cascade)
  schemaId        String     // К какой схеме относится

  // ТИП АННОТАЦИИ
  annotationType  String     // Тип: "text", "arrow", "highlight", "area", "callout"

  // ПОЗИЦИЯ И РАЗМЕРЫ
  x               Float      @gte(0) @lte(100) // Координата X в процентах
  y               Float      @gte(0) @lte(100) // Координата Y в процентах
  width           Float?     @gte(0) @lte(100) // Ширина (для областей)
  height          Float?     @gte(0) @lte(100) // Высота (для областей)

  // СОДЕРЖИМОЕ
  text            String?    @length(1, 1000) // Текст аннотации
  description     String?    @length(1, 2000) // Подробное описание

  // СТИЛЬ И ВНЕШНИЙ ВИД
  color           String?    @default("#FF0000") // Цвет аннотации
  fontSize        Int?       @default(12) @gte(8) @lte(72) // Размер шрифта
  strokeWidth     Int?       @default(2) @gte(1) @lte(10) // Толщина линии
  opacity         Float?     @default(1.0) @gte(0.0) @lte(1.0) // Прозрачность

  // СВЯЗИ
  variant         SchemaVariant? @relation(fields: [variantId], references: [id])
  variantId       String?    // К какому варианту относится (null = общая)

  // МЕТАДАННЫЕ
  isVisible       Boolean    @default(true) // Видима ли аннотация
  displayOrder    Int        @default(0) // Порядок отображения

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Аннотации - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update,delete', auth() != null)

  @@index([schemaId])
  @@index([variantId])
  @@index([annotationType])
  @@index([isVisible])
  @@index([displayOrder])
  @@map("schema_annotation")
}

// СЛОИ СХЕМ
// Система слоев для организации элементов схемы
model SchemaLayer {
  id              String     @id @default(uuid())

  // СВЯЗЬ СО СХЕМОЙ
  schema          AggregateSchema @relation(fields: [schemaId], references: [id], onDelete: Cascade)
  schemaId        String     // К какой схеме относится

  // ХАРАКТЕРИСТИКИ СЛОЯ
  name            String     @length(1, 100) // Название слоя: "Основные детали", "Крепеж"
  description     String?    @length(1, 500) // Описание слоя

  // НАСТРОЙКИ ОТОБРАЖЕНИЯ
  isVisible       Boolean    @default(true) // Видим ли слой
  isLocked        Boolean    @default(false) // Заблокирован ли для редактирования
  opacity         Float      @default(1.0) @gte(0.0) @lte(1.0) // Прозрачность слоя
  displayOrder    Int        @default(0) // Порядок отображения (z-index)

  // ЦВЕТОВАЯ СХЕМА
  color           String?    @default("#000000") // Основной цвет слоя
  backgroundColor String?    // Цвет фона слоя

  // СВЯЗИ
  positions       SchemaPosition[] // Позиции в этом слое

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Слои - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update,delete', auth() != null)

  @@index([schemaId])
  @@index([isVisible])
  @@index([displayOrder])
  @@map("schema_layer")
}

// =====================================================
// СИСТЕМА АТРИБУТОВ И ХАРАКТЕРИСТИК
// =====================================================

// ОПРЕДЕЛЕНИЯ АТРИБУТОВ (РАСШИРЕННАЯ СИСТЕМА ДЛЯ РАЗМЕРНЫХ ХАРАКТЕРИСТИК)
// Определяет возможные атрибуты для запчастей (диаметр, высота, материал и т.д.)
model AttributeDefinition {
  id          String     @id @default(uuid())
  name        String     // Название атрибута: "Диаметр", "Высота", "Материал"
  slug        String     @unique // URL-совместимое имя: "diameter", "height"
  dataType    String     // Тип данных: "number", "string", "boolean", "enum", "dimension_range", "composite_dimension"
  unit        String?    // Единица измерения: "мм", "кг", "л"
  enumValues  String[]   @default([]) // Возможные значения для enum
  isRequired  Boolean    @default(false) // Обязательный ли атрибут
  isFilterable Boolean   @default(true)  // Можно ли фильтровать по этому атрибуту

  // РАСШИРЕННЫЕ ПОЛЯ ДЛЯ РАЗМЕРНЫХ ХАРАКТЕРИСТИК
  isDimensional Boolean   @default(false) // Является ли размерной характеристикой
  isComposite   Boolean   @default(false) // Составной атрибут (например, "14*30.5/38.4*5/11")
  defaultTolerance Float? // Допуск по умолчанию для размерных характеристик

  // АВТОКОНВЕРТАЦИЯ ЕДИНИЦ ИЗМЕРЕНИЯ
  baseUnit      String?   // Базовая единица для конвертации (например, "mm" для размеров)
  conversionFactor Float? @default(1.0) // Коэффициент конвертации к базовой единице
  alternativeUnits String[] @default([]) // Альтернативные единицы: ["дм", "см", "дюйм"]

  // ОГРАНИЧЕНИЯ ЗНАЧЕНИЙ
  minValue      Float?    // Минимальное допустимое значение
  maxValue      Float?    // Максимальное допустимое значение
  precision     Int?      @default(2) // Точность для числовых значений (знаков после запятой)

  // СВЯЗЬ С КАТЕГОРИЯМИ
  categories  CategoryAttribute[] // В каких категориях используется

  // ЗНАЧЕНИЯ АТРИБУТОВ
  values      PartAttributeValue[] // Значения этого атрибута у запчастей
  standardizedValues StandardizedPartAttribute[] // Стандартизированные значения атрибутов
  dimensionComponents DimensionComponent[] // Компоненты составных размеров

  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Определения атрибутов - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@index([slug])
  @@index([dataType])
  @@index([isFilterable])
  @@index([isDimensional])
  @@index([isComposite])
  @@index([baseUnit])
  @@map("attribute_definition")
}

// СВЯЗЬ АТРИБУТОВ С КАТЕГОРИЯМИ
// Определяет, какие атрибуты доступны в каждой категории
model CategoryAttribute {
  id            String     @id @default(uuid())

  category      Category   @relation(fields: [categoryId], references: [id])
  categoryId    String

  attribute     AttributeDefinition @relation(fields: [attributeId], references: [id])
  attributeId   String

  isRequired    Boolean    @default(false) // Обязательный ли в этой категории
  displayOrder  Int        @default(0)     // Порядок отображения

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Связи атрибутов с категориями - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@unique([categoryId, attributeId])
  @@map("category_attribute")
}

// ЗНАЧЕНИЯ АТРИБУТОВ ЗАПЧАСТЕЙ (РАСШИРЕННАЯ СИСТЕМА ДЛЯ РАЗМЕРОВ)
// Хранит конкретные значения атрибутов для каждой запчасти
model PartAttributeValue {
  id            String     @id @default(uuid())

  part          Part       @relation(fields: [partId], references: [id])
  partId        String

  attribute     AttributeDefinition @relation(fields: [attributeId], references: [id])
  attributeId   String

  // ОСНОВНЫЕ ЗНАЧЕНИЯ (только одно поле должно быть заполнено в зависимости от типа)
  stringValue   String?    // Для строковых значений
  numberValue   Float?     // Для числовых значений
  booleanValue  Boolean?   // Для булевых значений

  // РАСШИРЕННЫЕ ПОЛЯ ДЛЯ РАЗМЕРНЫХ ХАРАКТЕРИСТИК
  minValue      Float?     // Минимальное значение (для диапазонов)
  maxValue      Float?     // Максимальное значение (для диапазонов)
  tolerance     Float?     // Допуск (±значение)
  unit          String?    // Единица измерения для этого конкретного значения

  // СОСТАВНЫЕ РАЗМЕРЫ (например, "14*30.5/38.4*5/11")
  compositeValue String?   // Исходная строка составного размера
  isParsed      Boolean    @default(false) // Разобран ли составной размер на компоненты

  // МЕТАДАННЫЕ ДЛЯ ПОИСКА
  searchableValue Float?   // Нормализованное значение для поиска (в базовых единицах)
  isApproximate Boolean    @default(false) // Приблизительное ли значение

  // СВЯЗИ
  dimensionComponents DimensionComponent[] // Компоненты составного размера

  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Значения атрибутов запчастей - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@unique([partId, attributeId])
  @@index([attributeId])
  @@index([stringValue])
  @@index([numberValue])
  @@index([minValue])
  @@index([maxValue])
  @@index([searchableValue])
  @@index([unit])
  @@index([compositeValue])
  // СОСТАВНЫЕ ИНДЕКСЫ ДЛЯ РАЗМЕРНОГО ПОИСКА
  @@index([attributeId, minValue, maxValue])
  @@index([attributeId, searchableValue])
  @@index([partId, attributeId, searchableValue])
  @@map("part_attribute_value")
}

// КОМПОНЕНТЫ СОСТАВНЫХ РАЗМЕРОВ
// Хранит отдельные компоненты составных размеров типа "14*30.5/38.4*5/11"
model DimensionComponent {
  id              String     @id @default(uuid())

  // СВЯЗЬ С АТРИБУТОМ И ЗНАЧЕНИЕМ
  attributeDefinition AttributeDefinition @relation(fields: [attributeDefinitionId], references: [id])
  attributeDefinitionId String // К какому определению атрибута относится

  partAttributeValue PartAttributeValue? @relation(fields: [partAttributeValueId], references: [id])
  partAttributeValueId String? // К какому значению атрибута относится (опционально)

  // ХАРАКТЕРИСТИКИ КОМПОНЕНТА
  componentName   String     // Название компонента: "inner_diameter", "outer_diameter", "height"
  componentOrder  Int        // Порядок в составном размере (1, 2, 3...)

  // ЗНАЧЕНИЕ КОМПОНЕНТА
  value           Float      // Числовое значение компонента
  unit            String?    // Единица измерения
  tolerance       Float?     // Допуск для этого компонента

  // МЕТАДАННЫЕ
  description     String?    // Описание компонента
  isRequired      Boolean    @default(true) // Обязательный ли компонент

  // НОРМАЛИЗОВАННОЕ ЗНАЧЕНИЕ ДЛЯ ПОИСКА
  normalizedValue Float?     // Значение в базовых единицах для поиска

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Компоненты размеров - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@index([attributeDefinitionId])
  @@index([partAttributeValueId])
  @@index([componentName])
  @@index([value])
  @@index([normalizedValue])
  @@index([componentOrder])
  // СОСТАВНЫЕ ИНДЕКСЫ ДЛЯ ПОИСКА
  @@index([componentName, value])
  @@index([componentName, normalizedValue])
  @@index([attributeDefinitionId, componentName, value])
  @@map("dimension_component")
}

// ОПТИМИЗИРОВАННЫЙ ПОИСК ПО РАЗМЕРАМ
// Денормализованная таблица для быстрого поиска запчастей по размерным характеристикам
model PartDimensionSearch {
  id              String     @id @default(uuid())

  // СВЯЗЬ С ЗАПЧАСТЬЮ
  part            Part       @relation(fields: [partId], references: [id])
  partId          String

  // РАЗМЕРНЫЕ ХАРАКТЕРИСТИКИ (ДЕНОРМАЛИЗОВАННЫЕ)
  // Для сальников: внутренний диаметр, внешний диаметр, высота
  innerDiameter   Float?     // Внутренний диаметр в мм
  outerDiameter   Float?     // Внешний диаметр в мм
  height          Float?     // Высота в мм

  // ДОПОЛНИТЕЛЬНЫЕ РАЗМЕРЫ (для сложных сальников)
  outerDiameter2  Float?     // Второй внешний диаметр
  height2         Float?     // Вторая высота

  // ДОПУСКИ (для поиска с учетом допусков)
  innerDiameterTolerance Float? @default(0.3) // Допуск внутреннего диаметра
  outerDiameterTolerance Float? @default(0.3) // Допуск внешнего диаметра
  heightTolerance        Float? @default(3.0) // Допуск высоты

  // МЕТАДАННЫЕ
  dimensionString String?    // Исходная строка размера: "14*30.5/38.4*5/11"
  isComplete      Boolean    @default(false) // Все ли размеры заполнены
  lastUpdated     DateTime   @default(now()) // Когда обновлялись размеры

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Поиск по размерам - публичное чтение
  @@allow('read', true)

  // Создание и изменение только для авторизованных пользователей
  @@allow('create,update', auth() != null)

  // Удаление только для экспертов
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@unique([partId]) // Одна запись на запчасть
  @@index([innerDiameter])
  @@index([outerDiameter])
  @@index([height])
  @@index([outerDiameter2])
  @@index([height2])
  @@index([isComplete])
  // СОСТАВНЫЕ ИНДЕКСЫ ДЛЯ БЫСТРОГО ПОИСКА ПО РАЗМЕРАМ
  @@index([innerDiameter, outerDiameter])
  @@index([innerDiameter, outerDiameter, height])
  @@index([innerDiameter, outerDiameter, outerDiameter2, height, height2])
  @@map("part_dimension_search")
}

// =====================================================
// СИСТЕМА АУДИТА И ВЕРСИОНИРОВАНИЯ
// =====================================================

// ЖУРНАЛ ИЗМЕНЕНИЙ ГРУПП ВЗАИМОЗАМЕНЯЕМОСТИ
// Отслеживает все изменения в группах для аудита и отката
model InterchangeabilityGroupAudit {
  id              String     @id @default(uuid())

  groupId         String     // ID группы взаимозаменяемости
  action          String     // Тип действия: CREATE, UPDATE, DELETE, ADD_MEMBER, REMOVE_MEMBER

  // ДАННЫЕ ИЗМЕНЕНИЯ
  oldData         Json?      // Старые данные (для UPDATE и DELETE)
  newData         Json?      // Новые данные (для CREATE и UPDATE)

  // МЕТАДАННЫЕ
  userId          String?    // Кто внес изменение
  reason          String?    // Причина изменения
  source          String?    // Источник изменения: MANUAL, IMPORT, API

  createdAt       DateTime   @default(now())

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Аудит групп взаимозаменяемости - только для экспертов
  @@allow('read', auth() != null && auth().role == 'SERVICE')

  // Создание записей аудита - системные операции
  @@allow('create', true)

  // Изменение и удаление запрещено (только чтение и создание)
  @@deny('update,delete', true)

  @@index([groupId])
  @@index([action])
  @@index([createdAt])
  @@index([userId])
  @@map("interchangeability_group_audit")
}

// ПРЕДЛОЖЕНИЯ ПОЛЬЗОВАТЕЛЕЙ
// Система для сбора предложений от пользователей о новых эквивалентах
model UserSuggestion {
  id              String     @id @default(uuid())

  // ПРЕДЛАГАЕМАЯ СВЯЗЬ
  partId          String     // Основная запчасть
  part            Part       @relation("SuggestionPart", fields: [partId], references: [id])

  equivalentPartId String    // Предлагаемый эквивалент
  equivalentPart  Part       @relation("SuggestionEquivalent", fields: [equivalentPartId], references: [id])

  // ДЕТАЛИ ПРЕДЛОЖЕНИЯ
  equivalenceType EquivalenceType @default(IDENTICAL)
  confidence      Float      @default(1.0)
  description     String?    // Описание от пользователя
  evidence        String?    // Доказательства (ссылки, документы)

  // СТАТУС ОБРАБОТКИ
  status          String     @default("PENDING") // PENDING, APPROVED, REJECTED, UNDER_REVIEW
  reviewedBy      String?    // Кто рассмотрел
  reviewedAt      DateTime?  // Когда рассмотрено
  reviewNotes     String?    // Комментарии рецензента

  // АВТОР ПРЕДЛОЖЕНИЯ
  user            User?      @relation(fields: [userId], references: [id])
  userId          String?    // Кто предложил
  userEmail       String?    // Email пользователя

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Предложения пользователей - публичное чтение
  @@allow('read', true)

  // Создавать предложения могут авторизованные пользователи
  @@allow('create', auth() != null)

  // Изменять может автор предложения или эксперт
  @@allow('update', auth() != null && (auth().id == userId || auth().role == 'SERVICE'))

  // Удалять может только эксперт
  @@allow('delete', auth() != null && auth().role == 'SERVICE')

  @@index([status])
  @@index([partId])
  @@index([equivalentPartId])
  @@index([userId])
  @@index([createdAt])
  @@map("user_suggestion")
}

// =====================================================
// КОММЕРЧЕСКАЯ ЧАСТЬ - МАГАЗИНЫ И ЗАКАЗЫ
// =====================================================

// СКЛАДСКИЕ ЛОКАЦИИ
// Позволяет магазинам иметь несколько складов/точек выдачи с разными координатами
model InventoryLocation {
  id          String     @id @default(uuid())
  name        String     @length(1, 200) // Название локации: "Основной склад", "Точка выдачи №1"
  description String?    @length(1, 500) // Описание локации

  // СВЯЗЬ С МАГАЗИНОМ
  shop        Shop       @relation(fields: [shopId], references: [id])
  shopId      String     // К какому магазину относится

  // АДРЕС И ГЕОЛОКАЦИЯ
  fullAddress String     @length(1, 500) // Полный адрес локации
  city        String?    @length(1, 100) // Город
  region      String?    @length(1, 100) // Регион/область
  country     String?    @length(2, 100) // Страна
  postalCode  String?    @length(1, 20)  // Почтовый индекс
  latitude    Float      // Широта (обязательно для геопоиска)
  longitude   Float      // Долгота (обязательно для геопоиска)

  // ОПЕРАЦИОННЫЕ ХАРАКТЕРИСТИКИ
  isActive    Boolean    @default(true)  // Активна ли локация
  isPrimary   Boolean    @default(false) // Основная локация магазина
  locationType String    @default("WAREHOUSE") // Тип: WAREHOUSE, PICKUP_POINT, SHOWROOM

  // РАБОЧИЕ ЧАСЫ И КОНТАКТЫ
  workingHours Json?     // Часы работы локации
  contactPhone String?   // Телефон локации
  contactEmail String?   // Email локации

  // СВЯЗИ
  shopParts   ShopPart[] // Запчасти в этой локации

  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Складские локации - публичное чтение
  @@allow('read', true)

  // Управлять локациями может только владелец магазина
  @@allow('create,update,delete', auth() != null && auth().shop.id == shopId)

  @@index([shopId])
  @@index([latitude, longitude]) // Для геопространственного поиска
  @@index([city])
  @@index([isActive])
  @@index([isPrimary])
  @@index([locationType])
  // СОСТАВНЫЕ ИНДЕКСЫ
  @@index([shopId, isActive])
  @@index([shopId, isPrimary])
  @@map("inventory_location")
}

// =====================================================
// BETTER-AUTH МОДЕЛИ
// ПОЛЬЗОВАТЕЛИ СИСТЕМЫ (СОВМЕСТИМАЯ С BETTER-AUTH)
model User {
  id              String     @id @default(uuid())
  email           String     @unique
  phoneNumber     String?    @unique @omit // Совместимость с better-auth (скрыто от клиента)
  name            String     // Имя пользователя

  // BETTER-AUTH ПОЛЯ
  emailVerified   Boolean?   @default(false) // Верификация email
  image           String?    // URL аватара (better-auth использует image, не avatar)
  phoneVerified   Boolean?   // Верификация телефона
  isAnonymous     Boolean?   // Анонимный пользователь
  username        String?    @unique // Имя пользователя
  displayUsername String?    // Отображаемое имя пользователя

  // РОЛЬ (УПРОЩЕННАЯ СИСТЕМА)
  role            Role       @default(USER) // Основная роль пользователя

  // ADMIN PLUGIN ПОЛЯ
  banned          Boolean?   @default(false) // Заблокирован ли пользователь
  banReason       String?    // Причина блокировки
  banExpires      DateTime?  // Дата окончания блокировки

  // СВЯЗИ С АУДИТОМ
  adminActions    AdminAuditLog[] @relation("AdminActions") // Действия, выполненные этим администратором
  targetActions   AdminAuditLog[] @relation("TargetActions") // Действия, направленные на этого пользователя

  // СВЯЗИ С BETTER-AUTH
  account         Account[]  // OAuth аккаунты
  session         Session[]  // Сессии пользователя

  // СВЯЗИ С БИЗНЕС-ЛОГИКОЙ
  orders          Order[]    // Заказы пользователя
  suggestions     UserSuggestion[] // Предложения пользователя
  shopReviews     ShopReview[] @relation("ShopReviews") // Отзывы о магазинах
  notifications   Notification[] // Уведомления пользователя
  shop            Shop?      // Магазин пользователя (если он владелец)
  cart            Cart?      // Корзина пользователя

  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Пользователь может читать только свои данные
  @@allow('read', auth() == this)

  // Пользователь может обновлять только свои данные
  @@allow('update', auth() == this)

  // Регистрация доступна всем
  @@allow('create', true)

  @@map("user")
}

// =====================================================
// СИСТЕМА АУДИТА АДМИНИСТРАТИВНЫХ ДЕЙСТВИЙ
// =====================================================

// ЖУРНАЛ АДМИНИСТРАТИВНЫХ ДЕЙСТВИЙ
// Записывает все действия администраторов для аудита и безопасности
model AdminAuditLog {
  id              String     @id @default(uuid())

  // АДМИНИСТРАТОР, ВЫПОЛНИВШИЙ ДЕЙСТВИЕ
  admin           User       @relation("AdminActions", fields: [adminId], references: [id])
  adminId         String     // ID администратора
  adminEmail      String     // Email администратора (для быстрого поиска)

  // ДЕЙСТВИЕ
  action          String     // Тип действия: USER_CREATED, USER_BANNED, etc.

  // ЦЕЛЕВОЙ ПОЛЬЗОВАТЕЛЬ (если применимо)
  targetUser      User?      @relation("TargetActions", fields: [targetUserId], references: [id])
  targetUserId    String?    // ID пользователя, над которым выполнено действие
  targetUserEmail String?    // Email целевого пользователя

  // ДЕТАЛИ ДЕЙСТВИЯ
  details         Json?      // Подробности действия в JSON формате

  // МЕТАДАННЫЕ ЗАПРОСА
  ipAddress       String?    // IP адрес администратора
  userAgent       String?    // User Agent браузера

  // РЕЗУЛЬТАТ ДЕЙСТВИЯ
  success         Boolean    @default(true) // Успешно ли выполнено действие
  errorMessage    String?    // Сообщение об ошибке (если не успешно)

  // ВРЕМЕННЫЕ МЕТКИ
  createdAt       DateTime   @default(now()) // Время выполнения действия
  executionTime   Int?       // Время выполнения в миллисекундах

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Журнал аудита - только для администраторов SERVICE
  @@allow('read', auth() != null && auth().role == 'SERVICE')

  // Создание записей аудита - системные операции
  @@allow('create', true)

  // Изменение и удаление запрещено (только чтение и создание)
  @@deny('update,delete', true)

  // ИНДЕКСЫ ДЛЯ БЫСТРОГО ПОИСКА
  @@index([adminId])
  @@index([adminEmail])
  @@index([action])
  @@index([targetUserId])
  @@index([targetUserEmail])
  @@index([createdAt])
  @@index([success])
  @@index([ipAddress])

  // СОСТАВНЫЕ ИНДЕКСЫ
  @@index([adminId, action])
  @@index([action, createdAt])
  @@index([targetUserId, action])
  @@index([adminEmail, createdAt])
  @@index([success, action])

  @@map("admin_audit_log")
}



// МАГАЗИНЫ ЗАПЧАСТЕЙ (ОБНОВЛЕННАЯ МОДЕЛЬ)
model Shop {
  id          String     @id @default(uuid())
  name        String     @length(1, 200) // Название магазина
  slug        String     @unique @regex("^[a-z0-9-]+$") @length(1, 100) // URL-совместимое имя
  description String?    @length(1, 1000) // Описание магазина

  // СВЯЗЬ С ПОЛЬЗОВАТЕЛЕМ
  user            User       @relation(fields: [userId], references: [id])
  userId          String     @unique // Один пользователь - один магазин

  // НАСТРОЙКИ МАГАЗИНА
  isActive    Boolean    @default(true) // Активен ли магазин
  isVerified  Boolean    @default(false) // Проверенный магазин
  verifiedAt  DateTime?  // Дата верификации

  // ГЕОЛОКАЦИЯ И АДРЕС
  fullAddress String?    @length(1, 500) // Полный адрес магазина
  city        String?    @length(1, 100) // Город
  region      String?    @length(1, 100) // Регион/область
  country     String?    @length(2, 100) // Страна (ISO код или название)
  postalCode  String?    @length(1, 20)  // Почтовый индекс
  latitude    Float?     // Широта (для геопоиска)
  longitude   Float?     // Долгота (для геопоиска)
  timezone    String?    @length(1, 50)  // Часовой пояс (например, "Europe/Moscow")

  // ОПЕРАЦИОННЫЕ НАСТРОЙКИ
  minOrderAmount Decimal? // Минимальная сумма заказа
  workingHours Json?     // Часы работы

  // КОНТАКТНАЯ ИНФОРМАЦИЯ (может отличаться от бизнес-профиля)
  contactEmail String?   // Email для заказов
  contactPhone String?   // Телефон для заказов

  // ТОВАРЫ И ЗАКАЗЫ
  shopParts   ShopPart[] // Запчасти в наличии
  orders      Order[]    // Заказы в магазине
  inventoryLocations InventoryLocation[] // Складские локации магазина

  // РЕЙТИНГИ И ОТЗЫВЫ
  reviews     ShopReview[] // Отзывы о магазине
  averageRating Float?   // Средний рейтинг
  totalReviews Int       @default(0) // Общее количество отзывов

  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Информация о магазинах - публичное чтение
  @@allow('read', true)

  // Создавать магазин может авторизованный пользователь с ролью SHOP
  @@allow('create', auth() != null && auth().role == 'SHOP')

  // Изменять может только владелец магазина
  @@allow('update', auth() != null && auth().id == userId)

  // Удалять может только владелец
  @@allow('delete', auth() != null && auth().id == userId)

  @@index([userId])
  @@index([isActive])
  @@index([isVerified])
  @@index([averageRating])
  // ГЕОЛОКАЦИОННЫЕ ИНДЕКСЫ
  @@index([latitude, longitude]) // Для геопространственного поиска
  @@index([city])
  @@index([region])
  @@index([country])
  @@map("shop")
}



// ЗАПЧАСТИ В МАГАЗИНАХ (РАСШИРЕННАЯ МОДЕЛЬ)
// Связывает каталожные позиции с конкретными предложениями в магазинах
model ShopPart {
  id            String     @id @default(uuid())

  // КАТАЛОЖНАЯ ПОЗИЦИЯ
  catalogPart   CatalogPart @relation(fields: [catalogPartId], references: [id])
  catalogPartId String      // Какая именно запчасть

  // МАГАЗИН
  shop          Shop       @relation(fields: [shopId], references: [id])
  shopId        String     // В каком магазине

  // ОСНОВНЫЕ КОММЕРЧЕСКИЕ ДАННЫЕ
  basePrice     Decimal    @default(0) @gte(0) // Базовая цена
  stock         Int        @default(0) @gte(0) // Количество в наличии
  condition     PartCondition @default(NEW) // Состояние запчасти
  warranty      Int?       @gte(0) @lte(120) // Гарантия в месяцах

  // РАСШИРЕННАЯ ИНФОРМАЦИЯ О ТОВАРЕ
  sku           String?    // Артикул магазина
  location      String?    // Местоположение на складе (полка, ряд и т.д.)
  supplierInfo  String?    // Информация о поставщике
  leadTime      Int?       // Срок поставки в днях
  minOrderQty   Int        @default(1) // Минимальное количество для заказа
  maxOrderQty   Int?       // Максимальное количество

  // СКЛАДСКАЯ ЛОКАЦИЯ
  inventoryLocation InventoryLocation? @relation(fields: [inventoryLocationId], references: [id])
  inventoryLocationId String?          // В какой складской локации находится

  // СТАТУС И ДОСТУПНОСТЬ
  isActive      Boolean    @default(true) // Активно ли предложение
  isInStock     Boolean    @default(true) // В наличии ли
  expectedRestockDate DateTime? // Ожидаемая дата поступления

  // СВЯЗИ
  orderItems    OrderItem[] // Позиции заказов
  alternatives  OrderAlternative[] // Альтернативные предложения

  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Цены и наличие - публичное чтение
  @@allow('read', true)

  // Управлять товарами может только владелец магазина
  @@allow('create,update,delete', auth() != null && auth().shop.id == shopId)

  @@unique([catalogPartId, shopId]) // Один товар в одном магазине
  @@index([isActive])
  @@index([isInStock])
  @@index([basePrice])
  @@index([stock])
  @@index([inventoryLocationId])
  // СОСТАВНЫЕ ИНДЕКСЫ ДЛЯ ПРОИЗВОДИТЕЛЬНОСТИ
  @@index([shopId, isActive])
  @@index([shopId, isInStock])
  @@index([catalogPartId, isActive])
  @@index([inventoryLocationId, isInStock])
  @@map("shop_part")
}





// ЗАКАЗЫ (РАСШИРЕННАЯ МОДЕЛЬ)
model Order {
  id           String     @id @default(uuid())
  orderNumber  String     @unique @length(1, 50) @regex("^[A-Za-z0-9\\-]+$") // Номер заказа
  status       OrderStatus @default(PENDING) // Статус заказа
  orderType    OrderType  @default(REGULAR) // Тип заказа

  // ПОКУПАТЕЛЬ
  user         User       @relation(fields: [userId], references: [id])
  userId       String

  // МАГАЗИН
  shop         Shop       @relation(fields: [shopId], references: [id])
  shopId       String

  // ФИНАНСОВАЯ ИНФОРМАЦИЯ
  subtotal     Decimal    @gte(0) // Сумма без доставки и налогов
  taxAmount    Decimal    @default(0) @gte(0) // Сумма налога
  shippingCost Decimal    @default(0) @gte(0) // Стоимость доставки
  discountAmount Decimal  @default(0) @gte(0) // Сумма скидки
  totalPrice   Decimal    @gte(0) // Общая стоимость

  // УСЛОВИЯ ОПЛАТЫ (для B2B)
  paymentTerms Int?       // Отсрочка платежа в днях
  creditUsed   Decimal?   // Использованный кредитный лимит



  // ДОПОЛНИТЕЛЬНАЯ ИНФОРМАЦИЯ
  customerNotes String?   // Комментарии покупателя
  internalNotes String?   // Внутренние комментарии
  priority     Int        @default(0) // Приоритет заказа

  // СВЯЗИ
  items        OrderItem[] // Позиции заказа
  alternatives OrderAlternative[] // Предложения альтернатив
  reviews      ShopReview[] // Отзывы по этому заказу

  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Заказ видят только покупатель и владелец магазина
  @@allow('read', auth() != null && (auth().id == userId || auth().shop.id == shopId))

  // Создавать заказы могут авторизованные пользователи
  @@allow('create', auth() != null && auth().id == userId)

  // Изменять заказ может покупатель (до подтверждения) или владелец магазина
  @@allow('update', auth() != null && (
    (auth().id == userId && status == 'DRAFT') ||
    auth().shop.id == shopId
  ))

  @@index([status])
  @@index([orderType])
  @@index([userId])
  @@index([shopId])
  @@index([createdAt])
  @@map("order")
}



// АЛЬТЕРНАТИВНЫЕ ПРЕДЛОЖЕНИЯ В ЗАКАЗАХ
// Система предложения взаимозаменяемых запчастей
model OrderAlternative {
  id              String     @id @default(uuid())

  order           Order      @relation(fields: [orderId], references: [id])
  orderId         String

  // ОРИГИНАЛЬНАЯ ПОЗИЦИЯ (которой нет в наличии)
  originalItem    OrderItem  @relation(fields: [originalItemId], references: [id])
  originalItemId  String

  // АЛЬТЕРНАТИВНОЕ ПРЕДЛОЖЕНИЕ
  alternativeShopPart ShopPart @relation(fields: [alternativeShopPartId], references: [id])
  alternativeShopPartId String

  // ХАРАКТЕРИСТИКИ АЛЬТЕРНАТИВЫ
  equivalenceType EquivalenceType // Тип эквивалентности
  priceAdjustment Decimal @default(0) // Разница в цене
  availableQuantity Int    // Доступное количество

  // СТАТУС ПРЕДЛОЖЕНИЯ
  status          String   @default("PENDING") // PENDING, ACCEPTED, REJECTED
  customerResponse String? // Ответ клиента
  respondedAt     DateTime? // Когда клиент ответил

  // ДОПОЛНИТЕЛЬНАЯ ИНФОРМАЦИЯ
  notes           String?  // Комментарии к альтернативе

  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Альтернативы в заказах видят только участники заказа
  @@allow('read', auth() != null && (auth().id == order.userId || auth().shop.id == order.shopId))

  // Создавать альтернативы может владелец магазина
  @@allow('create', auth() != null && auth().shop.id == order.shopId)

  // Изменять может владелец магазина или покупатель (для ответа)
  @@allow('update', auth() != null && (auth().shop.id == order.shopId || auth().id == order.userId))

  // Удалять может только владелец магазина
  @@allow('delete', auth() != null && auth().shop.id == order.shopId)

  @@index([orderId])
  @@index([originalItemId])
  @@index([status])
  @@map("order_alternative")
}

// ПОЗИЦИИ ЗАКАЗА (РАСШИРЕННАЯ МОДЕЛЬ)
// Отдельные товары в заказе
model OrderItem {
  id           String     @id @default(uuid())

  // ЗАКАЗ
  order        Order      @relation(fields: [orderId], references: [id])
  orderId      String

  // ТОВАР
  shopPart     ShopPart   @relation(fields: [shopPartId], references: [id])
  shopPartId   String

  // КОЛИЧЕСТВО И ЦЕНА
  requestedQuantity Int   @gt(0) // Запрошенное количество
  availableQuantity Int   @gte(0) // Доступное количество
  shippedQuantity   Int   @default(0) @gte(0) // Отгруженное количество

  unitPrice    Decimal    @gte(0) // Цена за единицу на момент заказа
  discountPercent Float  @default(0) @gte(0) @lte(100) // Скидка на позицию
  totalPrice   Decimal    @gte(0) // Общая стоимость позиции

  // СТАТУС ПОЗИЦИИ
  status       String     @default("PENDING") // PENDING, AVAILABLE, BACKORDERED, SHIPPED
  estimatedDeliveryDate DateTime? // Ожидаемая дата поставки

  // ДОПОЛНИТЕЛЬНАЯ ИНФОРМАЦИЯ
  customerNotes String?   // Комментарии к позиции

  // СВЯЗИ
  alternatives OrderAlternative[] // Альтернативные предложения

  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Позиции заказов видят только участники заказа
  @@allow('read', auth() != null && (auth().id == order.userId || auth().shop.id == order.shopId))

  // Создавать позиции может покупатель при создании заказа
  @@allow('create', auth() != null && auth().id == order.userId)

  // Изменять может покупатель (до подтверждения) или владелец магазина
  @@allow('update', auth() != null && (
    (auth().id == order.userId && order.status == 'DRAFT') ||
    auth().shop.id == order.shopId
  ))

  // Удалять может покупатель (до подтверждения) или владелец магазина
  @@allow('delete', auth() != null && (
    (auth().id == order.userId && order.status == 'DRAFT') ||
    auth().shop.id == order.shopId
  ))

  @@index([orderId])
  @@index([shopPartId])
  @@index([status])
  @@map("order_item")
}

// =====================================================
// СИСТЕМА РЕЙТИНГОВ И ОТЗЫВОВ
// =====================================================

// ОТЗЫВЫ О МАГАЗИНАХ (УПРОЩЕННАЯ ВЕРСИЯ)
model ShopReview {
  id          String     @id @default(uuid())

  shop        Shop       @relation(fields: [shopId], references: [id])
  shopId      String

  reviewer    User       @relation("ShopReviews", fields: [reviewerId], references: [id])
  reviewerId  String

  // ОСНОВНАЯ ОЦЕНКА
  rating      Int        @gte(1) @lte(5) // Общая оценка (1-5)

  // ОТЗЫВ
  content     String?    // Текст отзыва

  // СВЯЗАННЫЙ ЗАКАЗ
  order       Order?     @relation(fields: [orderId], references: [id])
  orderId     String?    // К какому заказу относится отзыв

  // СТАТУС
  isPublished Boolean    @default(true)  // Опубликован ли

  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Отзывы - публичное чтение опубликованных
  @@allow('read', isPublished == true)

  // Автор может читать свои отзывы (даже неопубликованные)
  @@allow('read', auth() != null && auth().id == reviewerId)

  // Создавать отзывы могут авторизованные пользователи
  @@allow('create', auth() != null && auth().id == reviewerId)

  // Изменять может только автор отзыва
  @@allow('update', auth() != null && auth().id == reviewerId)

  // Удалять может автор или эксперт
  @@allow('delete', auth() != null && (auth().id == reviewerId || auth().role == 'SERVICE'))

  @@index([shopId])
  @@index([reviewerId])
  @@index([rating])
  @@index([isPublished])
  @@map("shop_review")
}



// =====================================================
// СИСТЕМА УВЕДОМЛЕНИЙ И КОММУНИКАЦИИ
// =====================================================

// УВЕДОМЛЕНИЯ
model Notification {
  id          String     @id @default(uuid())

  // ПОЛУЧАТЕЛЬ
  user        User       @relation(fields: [userId], references: [id])
  userId      String

  // СОДЕРЖАНИЕ
  type        String     // Тип уведомления: "ORDER_STATUS", "PRICE_CHANGE", "STOCK_ALERT"
  title       String     // Заголовок
  message     String     // Текст уведомления
  data        Json?      // Дополнительные данные

  // СТАТУС
  isRead      Boolean    @default(false) // Прочитано ли
  readAt      DateTime?  // Когда прочитано

  // КАНАЛЫ ДОСТАВКИ
  emailSent   Boolean    @default(false) // Отправлено по email
  pushSent    Boolean    @default(false) // Push-уведомление отправлено
  smsSent     Boolean    @default(false) // SMS отправлено

  // СВЯЗАННЫЕ ОБЪЕКТЫ
  orderId     String?    // Связанный заказ
  shopId      String?    // Связанный магазин

  createdAt   DateTime   @default(now())

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Уведомления видит только их получатель
  @@allow('read,update', auth() != null && auth().id == userId)

  // Создавать уведомления может система (без ограничений для внутренних процессов)
  @@allow('create', true)

  @@index([userId])
  @@index([type])
  @@index([isRead])
  @@index([createdAt])
  @@map("notification")
}

// =====================================================
// КОРЗИНА ПОКУПОК
// =====================================================

// КОРЗИНА ПОЛЬЗОВАТЕЛЯ
// Персистентная корзина для авторизованных пользователей
model Cart {
  id        String     @id @default(uuid())

  // СВЯЗЬ С ПОЛЬЗОВАТЕЛЕМ
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId    String     @unique // Одна корзина на пользователя

  // ПОЗИЦИИ В КОРЗИНЕ
  items     CartItem[] // Товары в корзине

  // МЕТАДАННЫЕ
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Корзину видит и редактирует только её владелец
  @@allow('all', auth() != null && auth().id == userId)

  @@map("cart")
}

// ПОЗИЦИЯ В КОРЗИНЕ
// Отдельный товар в корзине пользователя
model CartItem {
  id        String     @id @default(uuid())

  // СВЯЗЬ С КОРЗИНОЙ
  cart      Cart       @relation(fields: [cartId], references: [id], onDelete: Cascade)
  cartId    String

  // СВЯЗЬ С КАТАЛОЖНОЙ ПОЗИЦИЕЙ
  catalogPart   CatalogPart @relation(fields: [catalogPartId], references: [id])
  catalogPartId String

  // КОЛИЧЕСТВО И ЦЕНА
  quantity  Int        @gt(0) // Количество товара
  price     Decimal    @gte(0) // Цена на момент добавления в корзину

  // МЕТАДАННЫЕ
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  // ПРАВИЛА АВТОРИЗАЦИИ
  // Позицию корзины видит и редактирует только владелец корзины
  @@allow('all', auth() != null && cart.userId == auth().id)

  // УНИКАЛЬНОСТЬ
  @@unique([cartId, catalogPartId]) // Один товар может быть только один раз в корзине

  @@map("cart_item")
}

