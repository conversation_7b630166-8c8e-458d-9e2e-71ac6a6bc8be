import { initTRPC } from '@trpc/server';
import type { CreateFastifyContextOptions } from '@trpc/server/adapters/fastify';
import { auth } from './auth';
import { getSystemDB, getEnhancedDB, getPublicDB } from './db';
import { createAuthContext, createAuthProcedure } from './lib/trpc-auth';
import type { ExtendedUser } from './types/auth';

// Расширенный контекст с авторизацией
export interface Context {
  auth: typeof auth;
  prisma: ReturnType<typeof getSystemDB>; // Системный DB для совместимости
  user: ExtendedUser | null;
  db: ReturnType<typeof getEnhancedDB> | ReturnType<typeof getPublicDB>; // Enhanced DB с авторизацией
  req: CreateFastifyContextOptions['req'];
  res: CreateFastifyContextOptions['res'];
}

// Создание контекста с автоматической авторизацией
export async function createContext({ req, res }: CreateFastifyContextOptions): Promise<Context> {
  const authContext = await createAuthContext(req);

  return {
    auth,
    prisma: getSystemDB(), // Для совместимости со старым кодом
    user: authContext.user,
    db: authContext.db,
    req,
    res
  };
}

// Инициализация tRPC с новым контекстом
export const t = initTRPC.context<Context>().create();

// Базовые экспорты
export const router = t.router;
export const createTRPCRouter = t.router;

// Создаем процедуры с разными уровнями авторизации
const procedures = createAuthProcedure(t);

export const procedure = procedures.public;        // Публичная процедура
export const publicProcedure = procedure;         // Экспорт для совместимости
export const authProcedure = procedures.auth;     // Требует авторизации
export const protectedProcedure = procedures.auth; // Алиас для authProcedure
export const shopProcedure = procedures.shopOwner; // Только владельцы магазинов
export const expertProcedure = procedures.expert;  // Только эксперты
