import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function testEquipmentFunctionality() {
  console.log('Тестирование функциональности связи запчастей с техникой...');

  try {
    // Получаем существующие данные
    const category = await prisma.partCategory.findFirst({
      where: { slug: 'engine' }
    });

    const catalogItem = await prisma.catalogItem.findFirst({
      where: { sku: 'SKF-12345' }
    });

    const equipmentModel = await prisma.equipmentModel.findFirst({
      where: { id: 'cat-320d' }
    });

    if (!category || !catalogItem || !equipmentModel) {
      throw new Error('Тестовые данные не найдены. Запустите seed-test-data.ts сначала.');
    }

    console.log('Найдены тестовые данные:', {
      category: category.name,
      catalogItem: catalogItem.sku,
      equipmentModel: equipmentModel.name
    });

    // Создаем запчасть
    const part = await prisma.part.create({
      data: {
        name: 'Тестовая запчасть с техникой',
        partCategoryId: category.id,
        level: 0,
        path: '/test-part'
      }
    });

    console.log('Запчасть создана:', part.name);

    // Создаем связь с каталожной позицией
    const partApplicability = await prisma.partApplicability.create({
      data: {
        partId: part.id,
        catalogItemId: catalogItem.id,
        accuracy: 'EXACT_MATCH',
        notes: 'Тестовая связь'
      }
    });

    console.log('Связь с каталожной позицией создана');

    // Создаем связь с техникой
    const equipmentApplicability = await prisma.equipmentApplicability.create({
      data: {
        partId: part.id,
        equipmentModelId: equipmentModel.id,
        notes: 'Подходит для всех модификаций'
      }
    });

    console.log('Связь с техникой создана');

    // Проверяем, что все связи работают
    const partWithRelations = await prisma.part.findUnique({
      where: { id: part.id },
      include: {
        partCategory: true,
        applicabilities: {
          include: {
            catalogItem: {
              include: {
                brand: true
              }
            }
          }
        },
        equipmentApplicabilities: {
          include: {
            equipmentModel: {
              include: {
                brand: true
              }
            }
          }
        }
      }
    });

    console.log('Запчасть с полными связями:', JSON.stringify(partWithRelations, null, 2));

    console.log('✅ Тест прошел успешно! Функциональность работает корректно.');

  } catch (error) {
    console.error('❌ Ошибка при тестировании:', error);
    throw error;
  }
}

testEquipmentFunctionality()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
