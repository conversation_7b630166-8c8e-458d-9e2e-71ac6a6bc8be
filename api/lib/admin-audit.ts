import { $prisma } from '../db'
import type { ExtendedUser } from '../types/auth'

/**
 * Типы административных действий для аудита
 */
export enum AdminActionType {
  USER_CREATED = 'USER_CREATED',
  USER_UPDATED = 'USER_UPDATED',
  USER_DELETED = 'USER_DELETED',
  USER_BANNED = 'USER_BANNED',
  USER_UNBANNED = 'USER_UNBANNED',
  USER_ROLE_CHANGED = 'USER_ROLE_CHANGED',
  USER_SESSION_REVOKED = 'USER_SESSION_REVOKED',
  USER_ALL_SESSIONS_REVOKED = 'USER_ALL_SESSIONS_REVOKED',
  USER_IMPERSONATED = 'USER_IMPERSONATED',
  IMPERSONATION_STOPPED = 'IMPERSONATION_STOPPED'
}

/**
 * Интерфейс для записи аудита
 */
export interface AdminAuditLog {
  id?: string
  adminId: string
  adminEmail: string
  action: AdminActionType
  targetUserId?: string
  targetUserEmail?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  createdAt?: Date
}

/**
 * Логирование административных действий
 */
export class AdminAuditLogger {
  
  /**
   * Записывает действие администратора в лог
   */
  static async logAction(params: {
    admin: ExtendedUser
    action: AdminActionType
    targetUserId?: string
    targetUserEmail?: string
    details?: Record<string, any>
    ipAddress?: string
    userAgent?: string
  }) {
    try {
      // Сохраняем в базу данных
      const logEntry = await $prisma.adminAuditLog.create({
        data: {
          adminId: params.admin.id,
          adminEmail: params.admin.email,
          action: params.action,
          targetUserId: params.targetUserId,
          targetUserEmail: params.targetUserEmail,
          details: params.details,
          ipAddress: params.ipAddress,
          userAgent: params.userAgent,
          success: true
        }
      })

      // Также логируем в консоль для отладки
      console.log('🔒 ADMIN AUDIT LOG:', JSON.stringify({
        id: logEntry.id,
        adminEmail: logEntry.adminEmail,
        action: logEntry.action,
        targetUserEmail: logEntry.targetUserEmail,
        createdAt: logEntry.createdAt
      }, null, 2))

      return logEntry
    } catch (error) {
      console.error('❌ Failed to log admin action:', error)
    }
  }

  /**
   * Получает логи действий администратора
   */
  static async getAdminLogs(params: {
    adminId?: string
    action?: AdminActionType
    targetUserId?: string
    targetUserEmail?: string
    search?: string
    searchField?: 'adminEmail' | 'targetUserEmail'
    success?: boolean
    dateFrom?: Date
    dateTo?: Date
    limit?: number
    offset?: number
    sortBy?: string
    sortDirection?: 'asc' | 'desc'
  }) {
    try {
      const where: any = {}

      // Фильтры
      if (params.adminId) {
        where.adminId = params.adminId
      }

      if (params.action) {
        where.action = params.action
      }

      if (params.targetUserId) {
        where.targetUserId = params.targetUserId
      }

      if (params.targetUserEmail) {
        where.targetUserEmail = {
          contains: params.targetUserEmail,
          mode: 'insensitive'
        }
      }

      if (params.success !== undefined) {
        where.success = params.success
      }

      // Поиск
      if (params.search && params.searchField) {
        where[params.searchField] = {
          contains: params.search,
          mode: 'insensitive'
        }
      }

      // Фильтр по дате
      if (params.dateFrom || params.dateTo) {
        where.createdAt = {}
        if (params.dateFrom) {
          where.createdAt.gte = params.dateFrom
        }
        if (params.dateTo) {
          where.createdAt.lte = params.dateTo
        }
      }

      // Сортировка
      const orderBy: any = {}
      if (params.sortBy) {
        orderBy[params.sortBy] = params.sortDirection || 'desc'
      } else {
        orderBy.createdAt = 'desc'
      }

      const logs = await $prisma.adminAuditLog.findMany({
        where,
        orderBy,
        take: params.limit || 50,
        skip: params.offset || 0,
        include: {
          admin: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          targetUser: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      // Получаем общее количество записей для пагинации
      const total = await $prisma.adminAuditLog.count({ where })

      return {
        logs,
        total,
        limit: params.limit || 50,
        offset: params.offset || 0
      }
    } catch (error) {
      console.error('❌ Error fetching admin logs:', error)
      return {
        logs: [],
        total: 0,
        limit: params.limit || 50,
        offset: params.offset || 0
      }
    }
  }

  /**
   * Получает статистику административных действий
   */
  static async getAdminStats(adminId?: string) {
    try {
      const where: any = {}
      if (adminId) {
        where.adminId = adminId
      }

      // Общее количество действий
      const totalActions = await $prisma.adminAuditLog.count({ where })

      // Статистика по типам действий
      const actionsByType = await $prisma.adminAuditLog.groupBy({
        by: ['action'],
        where,
        _count: {
          action: true
        }
      })

      // Последнее действие
      const lastAction = await $prisma.adminAuditLog.findFirst({
        where,
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          admin: {
            select: {
              name: true,
              email: true
            }
          }
        }
      })

      // Статистика успешности
      const successStats = await $prisma.adminAuditLog.groupBy({
        by: ['success'],
        where,
        _count: {
          success: true
        }
      })

      // Статистика по дням (последние 30 дней)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const dailyStats = await $prisma.adminAuditLog.groupBy({
        by: ['createdAt'],
        where: {
          ...where,
          createdAt: {
            gte: thirtyDaysAgo
          }
        },
        _count: {
          id: true
        }
      })

      return {
        totalActions,
        actionsByType: actionsByType.reduce((acc, item) => {
          acc[item.action] = item._count.action
          return acc
        }, {} as Record<string, number>),
        successStats: successStats.reduce((acc, item) => {
          acc[item.success ? 'success' : 'error'] = item._count.success
          return acc
        }, {} as Record<string, number>),
        lastAction,
        dailyStats: dailyStats.length
      }
    } catch (error) {
      console.error('❌ Error fetching admin stats:', error)
      return {
        totalActions: 0,
        actionsByType: {},
        successStats: {},
        lastAction: null,
        dailyStats: 0
      }
    }
  }
}

/**
 * Middleware для автоматического логирования административных действий
 */
export function createAdminAuditMiddleware() {
  return async (opts: any) => {
    const { ctx, next, path, type } = opts
    
    // Проверяем, что это административное действие
    if (!path.includes('admin.')) {
      return next()
    }

    // Проверяем, что пользователь - администратор
    if (!ctx.user || ctx.user.role !== 'ADMIN') {
      return next()
    }

    const startTime = Date.now()
    let result: any
    let error: any

    try {
      result = await next()
      
      // Определяем тип действия на основе пути
      let actionType: AdminActionType | null = null
      let targetUserId: string | undefined
      let details: Record<string, any> = {}

      if (path.includes('createUser')) {
        actionType = AdminActionType.USER_CREATED
        details = { userData: opts.input }
      } else if (path.includes('removeUser')) {
        actionType = AdminActionType.USER_DELETED
        targetUserId = opts.input?.userId
      } else if (path.includes('banUser')) {
        actionType = AdminActionType.USER_BANNED
        targetUserId = opts.input?.userId
        details = { banReason: opts.input?.banReason, banExpiresIn: opts.input?.banExpiresIn }
      } else if (path.includes('unbanUser')) {
        actionType = AdminActionType.USER_UNBANNED
        targetUserId = opts.input?.userId
      } else if (path.includes('setUserRole')) {
        actionType = AdminActionType.USER_ROLE_CHANGED
        targetUserId = opts.input?.userId
        details = { newRole: opts.input?.role }
      } else if (path.includes('revokeUserSession')) {
        actionType = AdminActionType.USER_SESSION_REVOKED
        details = { sessionToken: opts.input?.sessionToken }
      } else if (path.includes('revokeAllUserSessions')) {
        actionType = AdminActionType.USER_ALL_SESSIONS_REVOKED
        targetUserId = opts.input?.userId
      } else if (path.includes('impersonateUser')) {
        actionType = AdminActionType.USER_IMPERSONATED
        targetUserId = opts.input?.userId
      } else if (path.includes('stopImpersonating')) {
        actionType = AdminActionType.IMPERSONATION_STOPPED
      }

      // Логируем действие, если оно определено
      if (actionType) {
        await AdminAuditLogger.logAction({
          admin: ctx.user,
          action: actionType,
          targetUserId,
          details: {
            ...details,
            executionTime: Date.now() - startTime,
            success: true
          }
        })
      }

      return result
    } catch (err) {
      error = err
      
      // Логируем ошибку
      await AdminAuditLogger.logAction({
        admin: ctx.user,
        action: AdminActionType.USER_UPDATED, // Общий тип для ошибок
        details: {
          error: err.message,
          path,
          input: opts.input,
          executionTime: Date.now() - startTime,
          success: false
        }
      })
      
      throw err
    }
  }
}

/**
 * Валидация прав доступа для административных операций
 */
export function validateAdminPermissions(user: ExtendedUser | null, action: string): boolean {
  // Проверяем, что пользователь авторизован
  if (!user) {
    return false
  }

      // Проверяем, что пользователь имеет роль ADMIN (администратор)
    if (user.role !== 'ADMIN') {
    return false
  }

  // Проверяем, что пользователь не заблокирован
  if (user.banned) {
    return false
  }

  // Дополнительные проверки можно добавить здесь
  // Например, проверка конкретных разрешений для разных действий

  return true
}

/**
 * Rate limiting для административных операций
 */
export class AdminRateLimiter {
  private static actionCounts = new Map<string, { count: number; resetTime: number }>()
  
  /**
   * Проверяет, не превышен ли лимит действий для администратора
   */
  static checkRateLimit(adminId: string, action: string, limit: number = 100, windowMs: number = 60000): boolean {
    const key = `${adminId}:${action}`
    const now = Date.now()
    
    const current = this.actionCounts.get(key)
    
    if (!current || now > current.resetTime) {
      // Сбрасываем счетчик
      this.actionCounts.set(key, { count: 1, resetTime: now + windowMs })
      return true
    }
    
    if (current.count >= limit) {
      return false
    }
    
    current.count++
    return true
  }
  
  /**
   * Очищает старые записи rate limiting
   */
  static cleanup() {
    const now = Date.now()
    for (const [key, value] of this.actionCounts.entries()) {
      if (now > value.resetTime) {
        this.actionCounts.delete(key)
      }
    }
  }
}

// Запускаем очистку rate limiter каждые 5 минут
setInterval(() => {
  AdminRateLimiter.cleanup()
}, 5 * 60 * 1000)
