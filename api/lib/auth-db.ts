import { auth } from '../auth'
import { getEnhancedDB, getPublicDB } from '../db'
import type { ExtendedUser } from '../types/auth'

/**
 * Получает текущего пользователя из Better-Auth сессии
 */
export async function getCurrentUser(request?: Request): Promise<ExtendedUser | null> {
  try {
    const session = await auth.api.getSession({
      headers: request?.headers || new Headers()
    })

    if (!session?.user) return null

    // Если пользователь имеет роль SHOP, загружаем связанный магазин
    if (session.user.role === 'SHOP') {
      const { getSystemDB } = await import('../db')
      const systemDB = getSystemDB()

      const shop = await systemDB.shop.findUnique({
        where: { userId: session.user.id },
        select: {
          id: true,
          name: true,
          isActive: true,
          isVerified: true
        }
      })

      return {
        ...session.user,
        shop
      } as ExtendedUser
    }

    return {
      ...session.user,
      shop: null
    } as ExtendedUser;
  } catch (error) {
    console.error('Ошибка получения текущего пользователя:', error)
    return null
  }
}

/**
 * Создает DB клиент с автоматическим определением пользователя из сессии
 * Основная функция для использования в API роутах
 */
export async function getAuthenticatedDB(request?: Request) {
  const user = await getCurrentUser(request)
  return getEnhancedDB(user)
}

/**
 * Создает DB клиент для конкретного пользователя
 * Полезно для операций от имени другого пользователя (админ-функции)
 */
export function getDBForUser(user: ExtendedUser | null) {
  return getEnhancedDB(user)
}

/**
 * Middleware для Hono - автоматически добавляет DB клиент в контекст
 */
export async function authDBMiddleware(c: any, next: any) {
  const user = await getCurrentUser()
  const db = getEnhancedDB(user)
  
  // Добавляем в контекст Hono
  c.set('db', db)
  c.set('user', user)
  
  await next()
}

/**
 * Хелпер для проверки ролей пользователя
 */
export function hasRole(user: ExtendedUser | null, role: string): boolean {
  return user?.role === role
}

/**
 * Хелпер для проверки, является ли пользователь владельцем магазина
 */
export function isShopOwner(user: ExtendedUser | null): boolean {
  return hasRole(user, 'SHOP')
}

/**
 * Хелпер для проверки, является ли пользователь экспертом
 */
export function isExpert(user: ExtendedUser | null): boolean {
  return hasRole(user, 'ADMIN')
}

/**
 * Получает ID магазина пользователя (если он владелец)
 */
export async function getUserShopId(user: ExtendedUser | null): Promise<string | null> {
  if (!user || !isShopOwner(user)) return null
  
  try {
    const db = getPublicDB()
    const shop = await db.shop.findUnique({
      where: { userId: user.id },
      select: { id: true }
    })
    return shop?.id || null
  } catch (error) {
    console.error('Ошибка получения ID магазина:', error)
    return null
  }
}
