

// =====================================================
// BETTER-AUTH МОДЕЛИ
// =====================================================

model Account {
    id                    String    @id // Уникальный идентификатор аккаунта
    accountId             String    // ID аккаунта у провайдера
    providerId            String    // ID провайдера (google, github, email)
    userId                String    // ID пользователя
    accessToken           String?   // Токен доступа
    refreshToken          String?   // Токен обновления
    idToken               String?   // ID токен
    accessTokenExpiresAt  DateTime? // Срок действия токена доступа
    refreshTokenExpiresAt DateTime? // Срок действия токена обновления
    scope                 String?   // Области доступа
    password              String?   // Хэш пароля (для email/password)
    createdAt             DateTime  // Дата создания
    updatedAt             DateTime  // Дата обновления

    user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade) // Связь с пользователем

    @@allow('read,update', auth() != null && auth().id == userId)
    @@allow('create,delete', true)
    @@map("account")
}

model Session {
    id             String   @id // Уникальный идентификатор сессии
    expiresAt      DateTime // Срок действия сессии
    token          String   @unique // Токен сессии
    createdAt      DateTime // Дата создания
    updatedAt      DateTime // Дата обновления
    ipAddress      String?  // IP адрес
    userAgent      String?  // User Agent
    userId         String   // ID пользователя
    impersonatedBy String? // ID администратора, который выполняет impersonation

    user           User     @relation(fields: [userId], references: [id], onDelete: Cascade) // Связь с пользователем

    @@allow('read', auth() != null && auth().id == userId)
    @@allow('create,update,delete', true)
    @@map("session")
}

model Verification {
    id         String    @id // Уникальный идентификатор верификации
    identifier String    // Email или телефон
    value      String    // Код верификации
    expiresAt  DateTime  // Срок действия
    createdAt  DateTime? // Дата создания
    updatedAt  DateTime? // Дата обновления

    @@allow('create,read,update,delete', true)
    @@map("verification")
}

// =====================================================
// СПРАВОЧНИКИ И ПЕРЕЧИСЛЕНИЯ
// =====================================================

enum VerificationLevel {
    UNVERIFIED   // Данные не проверены - могут быть ошибки
    COMMUNITY    // Подтверждено пользователями - средняя надежность
    EXPERT       // Проверено экспертом - высокая надежность
    OFFICIAL     // Официально подтверждено производителем - максимальная надежность
}

enum EquivalenceType {
    IDENTICAL    // Полная идентичность - можно заменять без ограничений
    COMPATIBLE   // Совместимость с ограничениями - нужно учитывать особенности
    ALTERNATIVE  // Альтернативная замена - может потребовать доработки
}

enum CatalogItemType {
    PART          // Запчасть
    UNIT          // Узел/агрегат
    ASSEMBLY      // Сборка
    TOOL          // Инструмент
    CONSUMABLE    // Расходник
    ACCESSORY     // Аксессуар
}

enum PartCondition {
    NEW           // Новая запчасть
    USED          // Б/У запчасть
    REFURBISHED   // Восстановленная
}

enum OrderStatus {
    DRAFT       // Черновик заказа
    PENDING     // Ожидает обработки
    CONFIRMED   // Подтвержден
    PROCESSING  // В процессе сборки
    COMPLETED   // Завершен
    CANCELLED   // Отменено
}

enum OrderType {
    REGULAR     // Обычный заказ
    PREORDER    // Предзаказ
    QUOTE       // Запрос котировки
    EMERGENCY   // Срочный заказ
}

enum NumberType {
    OEM         // Оригинальный номер производителя техники
    AFTERMARKET // Номер производителя аналогов
    INTERNAL    // Внутренний номер каталога
    SUPERSEDED  // Замененный номер (уже не используется)
}

enum CrossReferenceType {
    OEM_TO_AFTERMARKET  // OEM к аналогу
    AFTERMARKET_TO_OEM  // Аналог к OEM
    SUPERSESSION        // Замена (новый номер)
    INTERCHANGE         // Взаимозаменяемость
}

enum PartCategory {
    ENGINE        // Двигатель
    TRANSMISSION  // Трансмиссия
    HYDRAULICS    // Гидравлика
    ELECTRICAL    // Электрооборудование
    FILTERS       // Фильтры
    SEALS         // Уплотнения
    BEARINGS      // Подшипники
    ATTACHMENTS   // Навесное оборудование
    OTHER         // Прочее
}

enum Role {
    USER         // Обычный пользователь (инженер, механик, частное лицо)
    SHOP         // Магазин запчастей
    SERVICE      // Сервисный центр
}



// =====================================================
// ПОЛЬЗОВАТЕЛИ
// =====================================================

model User {
    id            String    @id @default(uuid()) // Уникальный идентификатор пользователя
    name          String?   // Имя пользователя
    email         String    @unique // Email пользователя
    emailVerified Boolean   @default(false) // Подтвержден ли email
    image         String?   // URL аватара пользователя
    role          Role      @default(USER) // Роль пользователя
    createdAt     DateTime  @default(now()) // Дата создания
    updatedAt     DateTime  @updatedAt // Дата последнего обновления

    // Better-auth связи
    accounts      Account[] // Аккаунты пользователя (OAuth, email/password)
    sessions      Session[] // Сессии пользователя

    // Бизнес связи
    orders        Order[]   // Заказы пользователя
    shop          Shop?     // Магазин пользователя (если есть)
    carts         Cart[]    // Корзины пользователя

    @@allow('read', true)
    @@allow('create,update', auth() != null && auth().id == id)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@map("user")
}

// =====================================================
// АДРЕСНАЯ ИНФОРМАЦИЯ
// =====================================================

model Address {
    id                 String              @id @default(uuid()) // Уникальный идентификатор адреса
    fullAddress        String              @length(1, 500) // Полный адрес
    city               String?             @length(1, 100) // Город
    region             String?             @length(1, 100) // Регион/область
    country            String?             @length(2, 100) // Страна
    postalCode         String?             @length(1, 20)  // Почтовый индекс
    latitude           Float?     // Широта
    longitude          Float?     // Долгота

    shops              Shop[]     // Магазины по этому адресу
    inventoryLocations InventoryLocation[] // Складские локации по этому адресу

    createdAt          DateTime            @default(now()) // Дата создания
    updatedAt          DateTime            @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null)
    @@index([latitude, longitude])
    @@index([city])
    @@index([country])
    @@map("address")
}

// =====================================================
// КАТАЛОЖНАЯ ЧАСТЬ
// =====================================================

model Category {
    id           String              @id @default(uuid()) // Уникальный идентификатор категории
    name         String              @length(1, 200) // Название категории: "Гидравлические насосы"
    slug         String              @unique @regex("^[a-z0-9-]+$") @length(1, 100) // URL-совместимое имя
    description  String?             @length(0, 1000) // Описание категории

    parent       Category?           @relation("CategoryToParent", fields: [parentId], references: [id]) // Родительская категория
    parentId     String?     // ID родительской категории
    children     Category[]          @relation("CategoryToParent") // Дочерние категории

    sortOrder    Int                 @default(0) // Порядок сортировки
    isActive     Boolean             @default(true) // Активна ли категория

    catalogItems CatalogItem[] // Унифицированные каталожные объекты в этой категории
    attributes   CategoryAttribute[] // Атрибуты, доступные в этой категории

    createdAt    DateTime            @default(now()) // Дата создания
    updatedAt    DateTime            @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null)
    @@index([parentId])
    @@index([sortOrder])
    @@map("category")
}

model Brand {
    id                 String              @id @default(uuid()) // Уникальный идентификатор бренда
    name               String              @length(1, 100) // Название бренда: "Caterpillar", "Komatsu"
    slug               String              @unique @regex("^[a-z0-9-]+$") @length(1, 100) // URL-совместимое имя
    country            String?             @length(2, 100) // Страна производителя
    isOem              Boolean             @default(false) // true = производитель техники, false = производитель запчастей

    parts              Part[]       // Запчасти этого бренда
    catalogItemNumbers CatalogItemNumber[] // Унифицированные номера каталожных объектов
    equipmentModels    EquipmentModel[] // Модели техники этого бренда

    createdAt          DateTime            @default(now()) // Дата создания
    updatedAt          DateTime            @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@index([isOem])
    @@map("brand")
}

model EquipmentModel {
    id                       String                     @id @default(uuid()) // Уникальный идентификатор модели техники
    name                     String                     @length(1, 100) // Название модели: "CAT 320D"
    brand                    Brand?                     @relation(fields: [brandId], references: [id]) // Связь с брендом
    brandId                  String?    // ID бренда
    yearFrom                 Int?                       @gte(1900) @lte(2100) // Год начала производства
    yearTo                   Int?                       @gte(1900) @lte(2100) // Год окончания производства
    category                 String?                    @length(1, 100) // Тип техники: "Экскаватор"

    catalogItemCompatibility CatalogItemCompatibility[] // Унифицированная совместимость

    createdAt                DateTime                   @default(now()) // Дата создания
    updatedAt                DateTime                   @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@index([brandId])
    @@map("equipment_model")
}

// =====================================================
// УНИФИЦИРОВАННАЯ СИСТЕМА КАТАЛОЖНЫХ ОБЪЕКТОВ
// =====================================================

// БАЗОВАЯ АБСТРАКЦИЯ - КАТАЛОЖНЫЙ ОБЪЕКТ
model CatalogItem {
    id                     String                             @id @default(uuid()) // Уникальный идентификатор каталожного объекта

    // ПОЛИМОРФНЫЙ ДИСКРИМИНАТОР
    itemType               CatalogItemType // PART, UNIT, ASSEMBLY, TOOL, etc.

    // ОБЩИЕ ПОЛЯ ДЛЯ ВСЕХ ТИПОВ
    name                   String                             @length(1, 300) // Название объекта
    description            String?                            @length(1, 2000) // Подробное описание
    specifications         Json?     // Технические характеристики

    // КАТЕГОРИЗАЦИЯ
    category               Category?                          @relation(fields: [categoryId], references: [id]) // Категория объекта
    categoryId             String?    // ID категории

    // ИЕРАРХИЯ (для узлов и сборок)
    parent                 CatalogItem?                       @relation("ItemHierarchy", fields: [parentId], references: [id]) // Родительский объект
    parentId               String?    // ID родительского объекта
    children               CatalogItem[]                      @relation("ItemHierarchy") // Дочерние объекты
    level                  Int                                @default(0) // Уровень вложенности: 0=основной, 1=подобъект

    // СВЯЗИ С КОНКРЕТНЫМИ ТИПАМИ (полиморфные)
    partDetails            Part?      // Если это запчасть
    unitDetails            Unit?      // Если это узел

    // УНИФИЦИРОВАННЫЕ СВЯЗИ
    numbers                CatalogItemNumber[] // Все номера этого объекта
    masterOfGroups         InterchangeabilityGroup[]          @relation("MasterItem") // Группы, где этот объект основной
    groupMemberships       InterchangeabilityGroupMember[] // Членство в группах взаимозаменяемости
    compatibilities        CatalogItemCompatibility[] // Совместимость с техникой
    standardizedAttributes StandardizedCatalogItemAttribute[] // Унифицированные стандартизированные атрибуты

    // ВЕРИФИКАЦИЯ
    verificationLevel      VerificationLevel                  @default(UNVERIFIED) // Насколько проверены данные
    verifiedBy             String?    // Кто проверил
    verifiedAt             DateTime?  // Когда проверено

    createdAt              DateTime                           @default(now()) // Дата создания
    updatedAt              DateTime                           @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@index([itemType])
    @@index([categoryId])
    @@index([parentId, level])
    @@index([verificationLevel])
    @@index([itemType, categoryId])
    @@map("catalog_item")
}

// УПРОЩЕННАЯ МОДЕЛЬ УЗЛА (только специфичные поля)
model Unit {
    id            String            @id @default(uuid()) // Уникальный идентификатор узла

    catalogItem   CatalogItem       @relation(fields: [catalogItemId], references: [id]) // Связь с каталожным объектом
    catalogItemId String            @unique // ID каталожного объекта

    // СПЕЦИФИЧНЫЕ ДЛЯ УЗЛОВ ПОЛЯ
    schemas       AggregateSchema[] // Схемы агрегатов для этого узла

    createdAt     DateTime          @default(now()) // Дата создания
    updatedAt     DateTime          @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@map("unit")
}

// УПРОЩЕННАЯ МОДЕЛЬ ЗАПЧАСТИ (только специфичные поля)
model Part {
    id                    String               @id @default(uuid()) // Уникальный идентификатор запчасти

    catalogItem           CatalogItem          @relation(fields: [catalogItemId], references: [id]) // Связь с каталожным объектом
    catalogItemId         String               @unique // ID каталожного объекта

    // СПЕЦИФИЧНЫЕ ДЛЯ ЗАПЧАСТЕЙ ПОЛЯ
    slug                  String?              @unique @regex("^[a-z0-9-]+$") @length(1, 100) // URL-совместимое имя
    commercialName        String?              @length(1, 300) // Коммерческое название
    commercialDescription String?              @length(1, 2000) // Коммерческое описание
    partCategory          PartCategory         @default(OTHER) // Категория запчасти

    brand                 Brand?               @relation(fields: [brandId], references: [id]) // Бренд (связь)
    brandId               String?    // ID бренда

    // СПЕЦИФИЧНЫЕ СВЯЗИ ДЛЯ ЗАПЧАСТЕЙ
    dimensionSearch       PartDimensionSearch? // Размерные характеристики для поиска
    shopParts             ShopPart[] // Предложения магазинов
    cartItems             CartItem[] // Позиции в корзинах
    schemaPositions       SchemaPosition[] // Позиции на схемах

    createdAt             DateTime             @default(now()) // Дата создания
    updatedAt             DateTime             @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create', auth() != null)
    @@allow('update', auth() != null && auth().role == 'SERVICE')
    @@index([brandId])
    @@index([partCategory])
    @@map("part")
}

// НОМЕРА ЗАПЧАСТЕЙ
model PartNumber {
    id                    String                     @id @default(uuid()) // Уникальный идентификатор номера
    number                String                     @length(1, 100) @regex("^[A-Za-z0-9\\-\\.\\s]+$") // Номер детали: "CAT-1234"
    numberType            NumberType                 @default(OEM) // Тип номера

    brand                 Brand?                     @relation(fields: [brandId], references: [id]) // Бренд номера
    brandId               String?    // ID бренда
    isOriginal            Boolean                    @default(false) // Оригинальный номер или аналог

    part                  Part                       @relation(fields: [partId], references: [id]) // К какой запчасти относится
    partId                String     // ID запчасти

    description           String?    // Описание применимости
    isActive              Boolean                    @default(true) // Активен ли номер
    supersededBy          String?   // Номер, которым заменен (если устарел)

    sourceCrossReferences PartNumberCrossReference[] @relation("SourceCrossReference") // Исходящие кросс-референсы
    targetCrossReferences PartNumberCrossReference[] @relation("TargetCrossReference") // Входящие кросс-референсы

    createdAt             DateTime                   @default(now()) // Дата создания
    updatedAt             DateTime                   @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([number, brandId])
    @@index([partId])
    @@index([number])
    @@index([brandId])
    @@index([numberType])
    @@index([isActive])
    @@index([number, numberType])
    @@index([brandId, numberType])
    @@map("part_number")
}

// КРОСС-РЕФЕРЕНСЫ НОМЕРОВ ДЕТАЛЕЙ
model PartNumberCrossReference {
    id                 String             @id @default(uuid()) // Уникальный идентификатор кросс-референса

    sourcePartNumber   PartNumber         @relation("SourceCrossReference", fields: [sourcePartNumberId], references: [id]) // Исходный номер
    sourcePartNumberId String

    targetPartNumber   PartNumber         @relation("TargetCrossReference", fields: [targetPartNumberId], references: [id]) // Целевой номер
    targetPartNumberId String

    referenceType      CrossReferenceType // Тип связи между номерами
    confidence         Float              @default(1.0) @gte(0.0) @lte(1.0) // Уверенность в соответствии

    notes              String?    // Примечания о соответствии
    restrictions       String?    // Ограничения применимости
    effectiveDate      DateTime?  // Дата вступления в силу

    verificationLevel  VerificationLevel  @default(UNVERIFIED) // Насколько проверены данные
    verifiedBy         String?    // Кто проверил
    verifiedAt         DateTime?  // Когда проверено
    source             String?    // Источник информации

    createdAt          DateTime           @default(now()) // Дата создания
    updatedAt          DateTime           @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([sourcePartNumberId, targetPartNumberId, referenceType])
    @@index([sourcePartNumberId])
    @@index([targetPartNumberId])
    @@index([referenceType])
    @@index([confidence])
    @@index([verificationLevel])
    @@map("part_number_cross_reference")
}

// =====================================================
// СИСТЕМА КРОСС-НОМЕРОВ УЗЛОВ
// =====================================================

// НОМЕРА УЗЛОВ (АНАЛОГ PartNumber)
model UnitNumber {
    id                    String                     @id @default(uuid()) // Уникальный идентификатор номера узла
    number                String                     @length(1, 100) @regex("^[A-Za-z0-9\\-\\.\\s]+$") // Номер узла: "JD-RE-1234"
    numberType            NumberType                 @default(OEM) // Тип номера

    brand                 Brand?                     @relation(fields: [brandId], references: [id]) // Бренд номера
    brandId               String?    // ID бренда
    isOriginal            Boolean                    @default(false) // Оригинальный номер или аналог

    unit                  Unit                       @relation(fields: [unitId], references: [id]) // К какому узлу относится
    unitId                String     // ID узла

    description           String?    // Описание применимости
    isActive              Boolean                    @default(true) // Активен ли номер
    supersededBy          String?   // Номер, которым заменен (если устарел)

    sourceCrossReferences UnitNumberCrossReference[] @relation("SourceUnitCrossReference") // Исходящие кросс-референсы
    targetCrossReferences UnitNumberCrossReference[] @relation("TargetUnitCrossReference") // Входящие кросс-референсы

    createdAt             DateTime                   @default(now()) // Дата создания
    updatedAt             DateTime                   @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([number, brandId])
    @@index([unitId])
    @@index([number])
    @@index([brandId])
    @@index([numberType])
    @@index([isActive])
    @@index([number, numberType])
    @@index([brandId, numberType])
    @@map("unit_number")
}

// КРОСС-РЕФЕРЕНСЫ НОМЕРОВ УЗЛОВ
model UnitNumberCrossReference {
    id                 String             @id @default(uuid()) // Уникальный идентификатор кросс-референса

    sourceUnitNumber   UnitNumber         @relation("SourceUnitCrossReference", fields: [sourceUnitNumberId], references: [id]) // Исходный номер
    sourceUnitNumberId String

    targetUnitNumber   UnitNumber         @relation("TargetUnitCrossReference", fields: [targetUnitNumberId], references: [id]) // Целевой номер
    targetUnitNumberId String

    referenceType      CrossReferenceType // Тип связи между номерами
    confidence         Float              @default(1.0) @gte(0.0) @lte(1.0) // Уверенность в соответствии

    notes              String?    // Примечания о соответствии
    restrictions       String?    // Ограничения применимости
    effectiveDate      DateTime?  // Дата вступления в силу

    verificationLevel  VerificationLevel  @default(UNVERIFIED) // Насколько проверены данные
    verifiedBy         String?    // Кто проверил
    verifiedAt         DateTime?  // Когда проверено
    source             String?    // Источник информации

    createdAt          DateTime           @default(now()) // Дата создания
    updatedAt          DateTime           @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([sourceUnitNumberId, targetUnitNumberId, referenceType])
    @@index([sourceUnitNumberId])
    @@index([targetUnitNumberId])
    @@index([referenceType])
    @@index([confidence])
    @@index([verificationLevel])
    @@map("unit_number_cross_reference")
}

// =====================================================
// УНИФИЦИРОВАННАЯ СИСТЕМА НОМЕРОВ
// =====================================================

// ЕДИНАЯ МОДЕЛЬ ДЛЯ ВСЕХ НОМЕРОВ КАТАЛОЖНЫХ ОБЪЕКТОВ
model CatalogItemNumber {
    id                    String                      @id @default(uuid()) // Уникальный идентификатор номера

    catalogItem           CatalogItem                 @relation(fields: [catalogItemId], references: [id]) // Каталожный объект
    catalogItemId         String   // ID каталожного объекта

    number                String                      @length(1, 100) @regex("^[A-Za-z0-9\\-\\.\\s]+$") // Номер: "CAT-1234", "JD-RE-5678"
    numberType            NumberType                  @default(OEM) // Тип номера

    brand                 Brand?                      @relation(fields: [brandId], references: [id]) // Бренд номера
    brandId               String?    // ID бренда
    isOriginal            Boolean                     @default(false) // Оригинальный номер или аналог

    description           String?    // Описание применимости
    isActive              Boolean                     @default(true) // Активен ли номер
    supersededBy          String?   // Номер, которым заменен (если устарел)

    sourceCrossReferences CatalogItemCrossReference[] @relation("SourceCrossReference") // Исходящие кросс-референсы
    targetCrossReferences CatalogItemCrossReference[] @relation("TargetCrossReference") // Входящие кросс-референсы

    createdAt             DateTime                    @default(now()) // Дата создания
    updatedAt             DateTime                    @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([number, brandId, catalogItemId]) // Уникальный номер в рамках бренда и объекта
    @@index([catalogItemId])
    @@index([number])
    @@index([brandId])
    @@index([numberType])
    @@index([isActive])
    @@index([number, numberType])
    @@index([brandId, numberType])
    @@map("catalog_item_number")
}

// УНИФИЦИРОВАННЫЕ КРОСС-РЕФЕРЕНСЫ НОМЕРОВ
model CatalogItemCrossReference {
    id                String             @id @default(uuid()) // Уникальный идентификатор кросс-референса

    sourceNumber      CatalogItemNumber  @relation("SourceCrossReference", fields: [sourceNumberId], references: [id]) // Исходный номер
    sourceNumberId    String

    targetNumber      CatalogItemNumber  @relation("TargetCrossReference", fields: [targetNumberId], references: [id]) // Целевой номер
    targetNumberId    String

    referenceType     CrossReferenceType // Тип связи между номерами
    confidence        Float              @default(1.0) @gte(0.0) @lte(1.0) // Уверенность в соответствии

    notes             String?    // Примечания о соответствии
    restrictions      String?    // Ограничения применимости
    effectiveDate     DateTime?  // Дата вступления в силу

    verificationLevel VerificationLevel  @default(UNVERIFIED) // Насколько проверены данные
    verifiedBy        String?    // Кто проверил
    verifiedAt        DateTime?  // Когда проверено
    source            String?    // Источник информации

    createdAt         DateTime           @default(now()) // Дата создания
    updatedAt         DateTime           @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([sourceNumberId, targetNumberId, referenceType])
    @@index([sourceNumberId])
    @@index([targetNumberId])
    @@index([referenceType])
    @@index([confidence])
    @@index([verificationLevel])
    @@map("catalog_item_cross_reference")
}

// =====================================================
// СИСТЕМА ВЗАИМОЗАМЕНЯЕМОСТИ
// =====================================================

// УНИФИЦИРОВАННЫЕ ГРУППЫ ВЗАИМОЗАМЕНЯЕМОСТИ
model InterchangeabilityGroup {
    id                String                          @id @default(uuid()) // Уникальный идентификатор группы
    name              String                          @length(1, 300) // Название группы
    description       String?                         @length(1, 1000) // Описание группы и ограничений

    // ПОЛИМОРФНАЯ СВЯЗЬ С МАСТЕР-ОБЪЕКТОМ
    masterItem        CatalogItem?                    @relation("MasterItem", fields: [masterItemId], references: [id]) // Основной каталожный объект в группе
    masterItemId      String?

    // ТИПИЗАЦИЯ ГРУППЫ
    groupType         CatalogItemType // PART, UNIT, ASSEMBLY, etc.
    category          String?                         @length(1, 100) // "HYDRAULIC_MOTOR", "TRANSMISSION", "ENGINE"
    subcategory       String?                         @length(1, 100) // "SWING_MOTOR", "FINAL_DRIVE"

    equivalenceType   EquivalenceType                 @default(IDENTICAL) // Общий тип эквивалентности
    confidence        Float                           @default(1.0) @gte(0.0) @lte(1.0) // Общая уверенность в группе

    // ТЕХНИЧЕСКИЕ ХАРАКТЕРИСТИКИ (для всех типов)
    minWeight         Float?       // Минимальный вес в группе (кг)
    maxWeight         Float?       // Максимальный вес в группе (кг)
    minPower          Float?       // Минимальная мощность (кВт)
    maxPower          Float?       // Максимальная мощность (кВт)

    verificationLevel VerificationLevel               @default(UNVERIFIED) // Насколько проверены данные
    verifiedBy        String?    // Кто проверил группу
    verifiedAt        DateTime?  // Когда проверено

    members           InterchangeabilityGroupMember[] // Все объекты в группе

    createdAt         DateTime                        @default(now()) // Дата создания
    updatedAt         DateTime                        @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@index([masterItemId])
    @@index([groupType])
    @@index([equivalenceType])
    @@index([verificationLevel])
    @@index([category, subcategory])
    @@map("interchangeability_group")
}

// УНИФИЦИРОВАННЫЕ ЧЛЕНЫ ГРУППЫ ВЗАИМОЗАМЕНЯЕМОСТИ
model InterchangeabilityGroupMember {
    id                       String                  @id @default(uuid()) // Уникальный идентификатор членства

    group                    InterchangeabilityGroup @relation(fields: [groupId], references: [id]) // Группа
    groupId                  String

    catalogItem              CatalogItem             @relation(fields: [catalogItemId], references: [id]) // Каталожный объект
    catalogItemId            String

    // РОЛЬ В ГРУППЕ
    isMaster                 Boolean                 @default(false) // Эталонный объект в группе
    isPrimary                Boolean                 @default(false) // Предпочтительный для замены
    isDeprecated             Boolean                 @default(false) // Устаревший, не рекомендуется

    // ЭКВИВАЛЕНТНОСТЬ
    equivalenceType          EquivalenceType         @default(IDENTICAL) // Тип эквивалентности с группой
    confidence               Float                   @default(1.0) @gte(0.0) @lte(1.0) // Уверенность в принадлежности к группе

    restrictions             String?                 @length(1, 2000) // Подробное описание ограничений
    notes                    String?                 @length(1, 2000) // Дополнительные примечания

    // ТЕХНИЧЕСКИЕ РАЗЛИЧИЯ
    weightDifference         Float?    // Разница в весе (кг)
    powerDifference          Float?    // Разница в мощности (кВт)
    dimensionDifferences     Json? // Различия в размерах

    // ИНСТРУКЦИИ ПО ЗАМЕНЕ
    replacementInstructions  String?                 @length(1, 2000) // Инструкции по замене
    requiredModifications    String?                 @length(1, 1000) // Необходимые доработки
    estimatedReplacementTime Int?   // Время замены в минутах

    // КОММЕРЧЕСКАЯ ИНФОРМАЦИЯ
    replacementCost          Float?    // Стоимость замены
    availabilityStatus       String?                 @length(1, 50) // "IN_STOCK", "ON_ORDER", "DISCONTINUED"

    verifiedBy               String?    // Кто проверил
    verifiedAt               DateTime?  // Когда проверено

    createdAt                DateTime                @default(now()) // Дата создания
    updatedAt                DateTime                @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([groupId, catalogItemId])
    @@index([catalogItemId])
    @@index([isMaster])
    @@index([isPrimary])
    @@index([equivalenceType])
    @@index([confidence])
    @@index([availabilityStatus])
    @@map("interchangeability_group_member")
}

// =====================================================
// СИСТЕМА СОВМЕСТИМОСТИ
// =====================================================

// СОВМЕСТИМОСТЬ УЗЛОВ С ТЕХНИКОЙ
model UnitCompatibility {
    id                String            @id @default(uuid()) // Уникальный идентификатор совместимости

    unit              Unit              @relation(fields: [unitId], references: [id]) // Узел
    unitId            String

    equipmentModel    EquipmentModel    @relation(fields: [equipmentModelId], references: [id]) // Модель техники
    equipmentModelId  String

    yearFrom          Int?              @gte(1900) @lte(2100) // С какого года применяется
    yearTo            Int?              @gte(1900) @lte(2100) // До какого года применяется

    notes             String?           @length(1, 1000) // Примечания по совместимости

    verificationLevel VerificationLevel @default(UNVERIFIED) // Насколько проверены данные
    verifiedBy        String?    // Кто проверил
    verifiedAt        DateTime?  // Когда проверено

    createdAt         DateTime          @default(now()) // Дата создания
    updatedAt         DateTime          @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([unitId, equipmentModelId])
    @@index([unitId])
    @@index([equipmentModelId])
    @@index([verificationLevel])
    @@map("unit_compatibility")
}

// СОВМЕСТИМОСТЬ ЗАПЧАСТЕЙ С УЗЛАМИ
model PartCompatibility {
    id                String            @id @default(uuid()) // Уникальный идентификатор совместимости

    part              Part              @relation(fields: [partId], references: [id]) // Запчасть
    partId            String

    unit              Unit              @relation(fields: [unitId], references: [id]) // Узел
    unitId            String

    yearFrom          Int?              @gte(1900) @lte(2100) // С какого года применяется
    yearTo            Int?              @gte(1900) @lte(2100) // До какого года применяется

    position          String?           @length(1, 100) // Позиция установки
    engineCode        String?           @length(1, 50) // Код двигателя
    serialNumberFrom  String?           @length(1, 50) // Серийный номер от
    serialNumberTo    String?           @length(1, 50) // Серийный номер до

    confidence        Float             @default(1.0) @gte(0.0) @lte(1.0) // Уверенность в совместимости
    notes             String?           @length(1, 1000) // Примечания по совместимости
    source            String?           @length(1, 200) // Источник информации

    verificationLevel VerificationLevel @default(UNVERIFIED) // Насколько проверены данные
    verifiedBy        String?    // Кто проверил
    verifiedAt        DateTime?  // Когда проверено

    createdAt         DateTime          @default(now()) // Дата создания
    updatedAt         DateTime          @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([partId, unitId, position, engineCode])
    @@index([partId])
    @@index([unitId])
    @@index([engineCode])
    @@index([confidence])
    @@index([verificationLevel])
    @@map("part_compatibility")
}

// =====================================================
// УНИФИЦИРОВАННАЯ СИСТЕМА СОВМЕСТИМОСТИ
// =====================================================

// ЕДИНАЯ СИСТЕМА СОВМЕСТИМОСТИ КАТАЛОЖНЫХ ОБЪЕКТОВ
model CatalogItemCompatibility {
    id                String            @id @default(uuid()) // Уникальный идентификатор совместимости

    catalogItem       CatalogItem       @relation(fields: [catalogItemId], references: [id]) // Каталожный объект
    catalogItemId     String

    equipmentModel    EquipmentModel    @relation(fields: [equipmentModelId], references: [id]) // Модель техники
    equipmentModelId  String

    yearFrom          Int?              @gte(1900) @lte(2100) // С какого года применяется
    yearTo            Int?              @gte(1900) @lte(2100) // До какого года применяется

    position          String?           @length(1, 100) // Позиция установки
    engineCode        String?           @length(1, 50) // Код двигателя
    serialNumberFrom  String?           @length(1, 50) // Серийный номер от
    serialNumberTo    String?           @length(1, 50) // Серийный номер до

    confidence        Float             @default(1.0) @gte(0.0) @lte(1.0) // Уверенность в совместимости
    notes             String?           @length(1, 1000) // Примечания по совместимости
    source            String?           @length(1, 200) // Источник информации

    verificationLevel VerificationLevel @default(UNVERIFIED) // Насколько проверены данные
    verifiedBy        String?    // Кто проверил
    verifiedAt        DateTime?  // Когда проверено

    createdAt         DateTime          @default(now()) // Дата создания
    updatedAt         DateTime          @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([catalogItemId, equipmentModelId, position, engineCode])
    @@index([catalogItemId])
    @@index([equipmentModelId])
    @@index([engineCode])
    @@index([confidence])
    @@index([verificationLevel])
    @@map("catalog_item_compatibility")
}

// =====================================================
// СИСТЕМА АТРИБУТОВ И ХАРАКТЕРИСТИК
// =====================================================

// ОПРЕДЕЛЕНИЯ АТРИБУТОВ
model AttributeDefinition {
    id                 String                             @id @default(uuid()) // Уникальный идентификатор атрибута
    name               String                             @length(1, 100) // Название атрибута: "Диаметр", "Высота"
    slug               String                             @unique @regex("^[a-z0-9-]+$") @length(1, 100) // URL-совместимое имя
    dataType           String                             @length(1, 50) // Тип данных: "number", "string", "boolean"
    unit               String?                            @length(1, 20) // Единица измерения: "мм", "кг"
    enumValues         String[]                           @default([]) // Возможные значения для enum
    isRequired         Boolean                            @default(false) // Обязательный ли атрибут
    isFilterable       Boolean                            @default(true) // Можно ли фильтровать по этому атрибуту

    isDimensional      Boolean                            @default(false) // Является ли размерной характеристикой
    defaultTolerance   Float? // Допуск по умолчанию для размерных характеристик

    baseUnit           String?                            @length(1, 20) // Базовая единица для конвертации
    conversionFactor   Float?                             @default(1.0) // Коэффициент конвертации к базовой единице

    minValue           Float?    // Минимальное допустимое значение
    maxValue           Float?    // Максимальное допустимое значение
    precision          Int?                               @default(2) // Точность для числовых значений

    categories         CategoryAttribute[] // В каких категориях используется
    standardizedValues StandardizedCatalogItemAttribute[] // Стандартизированные значения атрибутов

    createdAt          DateTime                           @default(now()) // Дата создания
    updatedAt          DateTime                           @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@index([slug])
    @@index([dataType])
    @@index([isFilterable])
    @@index([isDimensional])
    @@map("attribute_definition")
}

// СВЯЗЬ АТРИБУТОВ С КАТЕГОРИЯМИ
model CategoryAttribute {
    id           String              @id @default(uuid()) // Уникальный идентификатор связи

    category     Category            @relation(fields: [categoryId], references: [id]) // Категория
    categoryId   String

    attribute    AttributeDefinition @relation(fields: [attributeId], references: [id]) // Атрибут
    attributeId  String

    isRequired   Boolean             @default(false) // Обязательный ли в этой категории
    displayOrder Int                 @default(0) // Порядок отображения

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([categoryId, attributeId])
    @@map("category_attribute")
}

// УНИФИЦИРОВАННЫЕ АТРИБУТЫ КАТАЛОЖНЫХ ОБЪЕКТОВ
model StandardizedCatalogItemAttribute {
    id                String              @id @default(uuid()) // Уникальный идентификатор значения

    catalogItem       CatalogItem         @relation(fields: [catalogItemId], references: [id], onDelete: Cascade)
    catalogItemId     String

    attribute         AttributeDefinition @relation(fields: [attributeId], references: [id]) // Атрибут
    attributeId       String

    stringValue       String?             @length(1, 500) // Строковое значение
    numberValue       Float?     // Числовое значение
    booleanValue      Boolean?   // Булево значение
    dateValue         DateTime?  // Дата/время
    jsonValue         Json?      // Сложные структурированные данные

    minValue          Float?     // Минимальное значение (для диапазонов)
    maxValue          Float?     // Максимальное значение (для диапазонов)
    nominalValue      Float?     // Номинальное значение

    unit              String?             @length(1, 20) // Единица измерения
    precision         Int?       // Точность для числовых значений
    tolerance         Float?     // Допуск для числовых значений

    normalizedValue   Float?     // Нормализованное значение в базовых единицах
    normalizedMin     Float?     // Нормализованное минимальное значение
    normalizedMax     Float?     // Нормализованное максимальное значение

    context           String?             @length(1, 200) // Контекст применения атрибута
    conditions        String?             @length(1, 500) // Условия применения

    verificationLevel VerificationLevel   @default(UNVERIFIED) // Насколько проверены данные
    verifiedBy        String?    // Кто проверил
    verifiedAt        DateTime?  // Когда проверено

    createdAt         DateTime            @default(now()) // Дата создания
    updatedAt         DateTime            @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([catalogItemId, attributeId, context])
    @@index([attributeId])
    @@index([stringValue])
    @@index([numberValue])
    @@index([normalizedValue])
    @@index([verificationLevel])
    @@index([catalogItemId, attributeId])
    @@map("standardized_attribute_value")
}

// РАЗМЕРНЫЙ ПОИСК ДЛЯ САЛЬНИКОВ
model PartDimensionSearch {
    id                     String   @id @default(uuid()) // Уникальный идентификатор размерного поиска

    part                   Part     @relation(fields: [partId], references: [id]) // Запчасть
    partId                 String   @unique // Одна запись на запчасть

    innerDiameter          Float?     // Внутренний диаметр в мм
    outerDiameter          Float?     // Внешний диаметр в мм
    height                 Float?     // Высота в мм

    innerDiameter2         Float?     // Второй Внутренний диаметр в мм
    outerDiameter2         Float?     // Второй внешний диаметр
    height2                Float?     // Вторая высота

    innerDiameterTolerance Float?   @default(0.3) // Допуск внутреннего диаметра
    outerDiameterTolerance Float?   @default(0.3) // Допуск внешнего диаметра
    heightTolerance        Float?   @default(3.0) // Допуск высоты

    dimensionString        String?  @length(1, 200) // Исходная строка размера: "14*30.5/38.4*5/11"
    isComplete             Boolean  @default(false) // Все ли размеры заполнены
    isParsed               Boolean  @default(false) // Разобрана ли строка размера на компоненты
    lastUpdated            DateTime @default(now()) // Когда обновлялись размеры

    createdAt              DateTime @default(now()) // Дата создания
    updatedAt              DateTime @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@index([innerDiameter])
    @@index([innerDiameter2])

    @@index([outerDiameter])
    @@index([outerDiameter2])

    @@index([height])
    @@index([height2])

    @@index([isComplete])
    @@index([innerDiameter, outerDiameter])
    @@index([innerDiameter, outerDiameter, height])
    @@map("part_dimension_search")
}



// =====================================================
// ИНТЕРАКТИВНЫЕ СХЕМЫ
// =====================================================

// СХЕМЫ АГРЕГАТОВ
model AggregateSchema {
    id                String             @id @default(uuid()) // Уникальный идентификатор схемы
    name              String             @length(1, 200) // Название схемы
    description       String?            @length(1, 1000) // Описание схемы

    unit              Unit?              @relation(fields: [unitId], references: [id]) // Узел, к которому относится схема
    unitId            String?

    imageUrl          String?            @length(1, 500) // URL изображения схемы
    imageWidth        Int?       // Ширина изображения
    imageHeight       Int?       // Высота изображения

    // Контент SVG (для интерактивных схем)
    svgContent        String?    // Содержимое SVG-схемы

    isActive          Boolean            @default(true) // Активна ли схема
    sortOrder         Int                @default(0) // Порядок сортировки

    verificationLevel VerificationLevel  @default(UNVERIFIED) // Насколько проверены данные
    verifiedBy        String?    // Кто проверил
    verifiedAt        DateTime?  // Когда проверено

    positions         SchemaPosition[] // Позиции деталей на схеме
    annotations       SchemaAnnotation[] // Аннотации на схеме
    // layers      SchemaLayer[]      // Слои на схеме // REMOVED

    createdAt         DateTime           @default(now()) // Дата создания
    updatedAt         DateTime           @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@index([unitId])
    @@index([isActive])
    @@index([verificationLevel])
    @@map("aggregate_schema")
}

// ПОЗИЦИИ ДЕТАЛЕЙ НА СХЕМАХ
model SchemaPosition {
    id                String          @id @default(uuid()) // Уникальный идентификатор позиции

    schema            AggregateSchema @relation(fields: [schemaId], references: [id]) // Схема
    schemaId          String

    part              Part            @relation(fields: [partId], references: [id]) // Запчасть
    partId            String

    positionNumber    String          @length(1, 10) // Номер позиции на схеме (1, 2, 3, ...)
    x                 Float      // Координата X
    y                 Float      // Координата Y
    width             Float?     // Ширина позиции
    height            Float?     // Высота позиции

    shape             String          @default("circle") // Форма позиции
    color             String?         @length(1, 20) // Цвет позиции
    label             String?         @length(1, 100) // Метка позиции

    quantity          Int             @default(1) @gte(1) // Количество деталей
    isRequired        Boolean         @default(true) // Обязательная ли деталь
    isHighlighted     Boolean         @default(false) // Выделена ли позиция
    installationOrder Int? // Порядок установки
    notes             String?         @length(1, 500) // Примечания

    isVisible         Boolean         @default(true) // Видима ли позиция
    sortOrder         Int             @default(0) // Порядок сортировки

    // layer       SchemaLayer? @relation(fields: [layerId], references: [id]) // REMOVED
    // layerId     String? // REMOVED

    createdAt         DateTime        @default(now()) // Дата создания
    updatedAt         DateTime        @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@unique([schemaId, partId])
    @@unique([schemaId, positionNumber]) // Уникальный номер позиции в рамках схемы
    @@index([schemaId])
    @@index([partId])
    @@index([positionNumber])
    @@map("schema_position")
}

// АННОТАЦИИ НА СХЕМАХ
model SchemaAnnotation {
    id             String          @id @default(uuid()) // Уникальный идентификатор аннотации

    schema         AggregateSchema @relation(fields: [schemaId], references: [id]) // Схема
    schemaId       String

    x              Float      // Координата X
    y              Float      // Координата Y

    // Дополнительные размеры (для прямоугольников, выделений и т.д.)
    width          Float?     // Ширина области
    height         Float?     // Высота области

    text           String          @length(1, 500) // Текст аннотации
    annotationType String          @default("note") // Тип аннотации

    color          String?         @length(1, 20) // Цвет аннотации
    fontSize       Int?            @default(12) // Размер шрифта

    strokeWidth    Int?            @default(1) // Толщина обводки
    opacity        Float?          @default(1.0) // Прозрачность

    isVisible      Boolean         @default(true) // Видима ли аннотация
    sortOrder      Int             @default(0) // Порядок отображения

    // layer       SchemaLayer? @relation(fields: [layerId], references: [id]) // REMOVED
    // layerId     String? // REMOVED

    createdAt      DateTime        @default(now()) // Дата создания
    updatedAt      DateTime        @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@index([schemaId])
    @@map("schema_annotation")
}

/* // REMOVED
// СЛОИ НА СХЕМАХ
model SchemaLayer {
  id          String     @id @default(uuid()) // Уникальный идентификатор слоя

  schema      AggregateSchema @relation(fields: [schemaId], references: [id]) // Схема
  schemaId    String

  name        String     @length(1, 100) // Название слоя
  description String?    @length(1, 500) // Описание слоя
  
  isVisible   Boolean    @default(true)  // Видим ли слой
  isLocked    Boolean    @default(false) // Заблокирован ли слой для редактирования
  
  displayOrder Int       @default(0)     // Порядок отображения слоев
  
  opacity     Float?     @default(1.0) @gte(0.0) @lte(1.0) // Прозрачность слоя
  color       String?    @length(1, 20)  // Цвет для идентификации слоя
  
  positions   SchemaPosition[] // Позиции на этом слое
  annotations SchemaAnnotation[] // Аннотации на этом слое
  
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  @@allow('read', true)
  @@allow('create,update,delete', auth() != null)
  @@index([schemaId])
  @@index([displayOrder])
  @@map("schema_layer")
}
*/

// =====================================================
// КОММЕРЧЕСКАЯ ЧАСТЬ
// =====================================================

// МАГАЗИНЫ
model Shop {
    id                 String              @id @default(uuid()) // Уникальный идентификатор магазина
    name               String              @length(1, 200) // Название магазина
    description        String?             @length(1, 1000) // Описание магазина

    owner              User                @relation(fields: [ownerId], references: [id]) // Владелец магазина
    ownerId            String              @unique // ID владельца

    address            Address             @relation(fields: [addressId], references: [id]) // Адрес магазина
    addressId          String

    contactPhone       String?             @length(1, 50) // Телефон магазина
    contactEmail       String?             @length(1, 100) // Email магазина
    website            String?             @length(1, 200) // Сайт магазина

    isActive           Boolean             @default(true) // Активен ли магазин
    isVerified         Boolean             @default(false) // Проверен ли магазин

    inventoryLocations InventoryLocation[] // Складские локации магазина
    shopParts          ShopPart[] // Запчасти в наличии
    orders             Order[]    // Заказы магазина

    createdAt          DateTime            @default(now()) // Дата создания
    updatedAt          DateTime            @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update', auth() != null && auth().id == ownerId)
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@index([ownerId])
    @@index([addressId])
    @@index([isActive])
    @@index([isVerified])
    @@map("shop")
}

// СКЛАДСКИЕ ЛОКАЦИИ
model InventoryLocation {
    id           String     @id @default(uuid()) // Уникальный идентификатор локации
    name         String     @length(1, 200) // Название локации
    description  String?    @length(1, 500) // Описание локации

    shop         Shop       @relation(fields: [shopId], references: [id]) // Магазин
    shopId       String

    address      Address    @relation(fields: [addressId], references: [id]) // Адрес локации
    addressId    String

    isActive     Boolean    @default(true) // Активна ли локация
    isPrimary    Boolean    @default(false) // Основная ли локация
    locationType String     @default("WAREHOUSE") // Тип локации

    workingHours Json?     // Часы работы локации
    contactPhone String?    @length(1, 50) // Телефон локации
    contactEmail String?    @length(1, 100) // Email локации

    shopParts    ShopPart[] // Запчасти в этой локации

    createdAt    DateTime   @default(now()) // Дата создания
    updatedAt    DateTime   @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().shop.id == shopId)
    @@index([shopId])
    @@index([addressId])
    @@index([isActive])
    @@index([isPrimary])
    @@map("inventory_location")
}

// ТОВАРЫ В МАГАЗИНАХ
model ShopPart {
    id                  String             @id @default(uuid()) // Уникальный идентификатор предложения

    part                Part               @relation(fields: [partId], references: [id]) // Запчасть
    partId              String

    shop                Shop               @relation(fields: [shopId], references: [id]) // Магазин
    shopId              String

    inventoryLocation   InventoryLocation? @relation(fields: [inventoryLocationId], references: [id]) // Складская локация
    inventoryLocationId String?

    supplierPartNumber  String?            @length(1, 100) // Артикул/ID товара у поставщика (для интеграций)
    price               Float              @gte(0) // Цена
    currency            String             @default("RUB") @length(3, 3) // Валюта
    quantity            Int                @gte(0) @default(0) // Количество
    condition           PartCondition      @default(NEW) // Состояние запчасти

    isAvailable         Boolean            @default(true) // Доступно ли предложение
    leadTime            Int?               @gte(0) // Срок поставки в днях
    minOrderQty         Int                @default(1) @gte(1) // Минимальное количество для заказа
    warrantyMonths      Int?               @gte(0) // Гарантийный срок в месяцах

    notes               String?            @length(1, 1000) // Примечания

    orderItems          OrderItem[] // Позиции заказов

    createdAt           DateTime           @default(now()) // Дата создания
    updatedAt           DateTime           @updatedAt // Дата последнего обновления

    @@allow('read', true)
    @@allow('create,update,delete', auth() != null && auth().shop.id == shopId)
    @@unique([partId, shopId])
    @@unique([shopId, supplierPartNumber]) // Уникальный артикул поставщика в рамках магазина
    @@index([partId])
    @@index([shopId])
    @@index([supplierPartNumber])
    @@index([inventoryLocationId])
    @@index([isAvailable])
    @@index([price])
    @@index([condition])
    @@map("shop_part")
}

// КОРЗИНЫ
model Cart {
    id        String     @id @default(uuid()) // Уникальный идентификатор корзины

    user      User       @relation(fields: [userId], references: [id]) // Владелец корзины
    userId    String

    items     CartItem[] // Позиции в корзине

    createdAt DateTime   @default(now()) // Дата создания
    updatedAt DateTime   @updatedAt // Дата последнего обновления

    @@allow('read,create,update,delete', auth() != null && auth().id == userId)
    @@unique([userId])
    @@map("cart")
}

// ПОЗИЦИИ В КОРЗИНАХ
model CartItem {
    id        String   @id @default(uuid()) // Уникальный идентификатор позиции

    cart      Cart     @relation(fields: [cartId], references: [id]) // Корзина
    cartId    String

    part      Part     @relation(fields: [partId], references: [id]) // Запчасть
    partId    String

    quantity  Int      @gte(1) @default(1) // Количество

    createdAt DateTime @default(now()) // Дата создания
    updatedAt DateTime @updatedAt // Дата последнего обновления

    @@allow('read,create,update,delete', auth() != null)
    @@unique([cartId, partId])
    @@index([cartId])
    @@index([partId])
    @@map("cart_item")
}

// ЗАКАЗЫ
model Order {
    id          String      @id @default(uuid()) // Уникальный идентификатор заказа
    orderNumber String      @unique @length(1, 50) // Номер заказа

    user        User        @relation(fields: [userId], references: [id]) // Покупатель
    userId      String

    shop        Shop        @relation(fields: [shopId], references: [id]) // Магазин
    shopId      String

    status      OrderStatus @default(DRAFT) // Статус заказа
    orderType   OrderType   @default(REGULAR) // Тип заказа

    totalAmount Float       @gte(0) @default(0) // Общая сумма заказа
    currency    String      @default("RUB") @length(3, 3) // Валюта

    notes       String?     @length(1, 1000) // Комментарии к заказу

    items       OrderItem[] // Позиции заказа

    createdAt   DateTime    @default(now()) // Дата создания
    updatedAt   DateTime    @updatedAt // Дата последнего обновления

    @@allow('read', auth() != null && (auth().id == userId || auth().shop.id == shopId))
    @@allow('create', auth() != null)
    @@allow('update', auth() != null && (auth().id == userId || auth().shop.id == shopId))
    @@allow('delete', auth() != null && auth().role == 'SERVICE')
    @@index([userId])
    @@index([shopId])
    @@index([status])
    @@index([orderType])
    @@index([createdAt])
    @@map("order")
}

// ПОЗИЦИИ ЗАКАЗОВ
model OrderItem {
    id         String   @id @default(uuid()) // Уникальный идентификатор позиции

    order      Order    @relation(fields: [orderId], references: [id]) // Заказ
    orderId    String

    shopPart   ShopPart @relation(fields: [shopPartId], references: [id]) // Предложение магазина
    shopPartId String

    quantity   Int      @gte(1) // Количество
    unitPrice  Float    @gte(0) // Цена за единицу
    totalPrice Float    @gte(0) // Общая стоимость позиции

    createdAt  DateTime @default(now()) // Дата создания
    updatedAt  DateTime @updatedAt // Дата последнего обновления

    @@allow('read', auth() != null)
    @@allow('create,update,delete', auth() != null)
    @@index([orderId])
    @@index([shopPartId])
    @@map("order_item")
}

// =====================================================
// СИСТЕМА АУДИТА
// =====================================================

// ЕДИНЫЙ ЖУРНАЛ АУДИТА
model AdminAuditLog {
    id         String   @id @default(uuid()) // Уникальный идентификатор записи аудита

    entityType String   @length(1, 100) // Тип сущности: Part, InterchangeabilityGroup и т.д.
    entityId   String     // ID сущности
    action     String   @length(1, 50) // Тип действия: CREATE, UPDATE, DELETE и т.д.

    oldData    Json?      // Старые данные
    newData    Json?      // Новые данные

    userId     String?    // Кто выполнил действие
    reason     String?  @length(1, 500) // Причина изменения
    source     String?  @length(1, 100) // Источник: MANUAL, IMPORT, API

    createdAt  DateTime @default(now()) // Дата создания

    @@allow('read', auth() != null && auth().role == 'SERVICE')
    @@allow('create', true)
    @@deny('update,delete', true)
    @@index([entityType])
    @@index([entityId])
    @@index([action])
    @@index([userId])
    @@index([createdAt])
    @@index([entityType, entityId])
    @@map("admin_audit_log")
}
